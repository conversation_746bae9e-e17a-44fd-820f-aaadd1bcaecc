{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-route-loader/index.ts"], "sourcesContent": ["import type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport type { MiddlewareConfig } from '../../../analysis/get-page-static-info'\n\nimport { stringify } from 'querystring'\nimport {\n  type ModuleBuildInfo,\n  getModuleBuildInfo,\n} from '../get-module-build-info'\nimport { RouteKind } from '../../../../server/route-kind'\nimport { normalizePagePath } from '../../../../shared/lib/page-path/normalize-page-path'\nimport { decodeFromBase64, encodeToBase64 } from '../utils'\nimport { isInstrumentationHookFile } from '../../../utils'\nimport { loadEntrypoint } from '../../../load-entrypoint'\nimport type { MappedPages } from '../../../build-context'\n\ntype RouteLoaderOptionsPagesAPIInput = {\n  kind: RouteKind.PAGES_API\n  page: string\n  preferredRegion: string | string[] | undefined\n  absolutePagePath: string\n  middlewareConfig: MiddlewareConfig\n}\n\ntype RouteLoaderOptionsPagesInput = {\n  kind: RouteKind.PAGES\n  page: string\n  pages: MappedPages\n  preferredRegion: string | string[] | undefined\n  absolutePagePath: string\n  middlewareConfig: MiddlewareConfig\n}\n\ntype RouteLoaderOptionsInput =\n  | RouteLoaderOptionsPagesInput\n  | RouteLoaderOptionsPagesAPIInput\n\ntype RouteLoaderPagesAPIOptions = {\n  kind: RouteKind.PAGES_API\n\n  /**\n   * The page name for this particular route.\n   */\n  page: string\n\n  /**\n   * The preferred region for this route.\n   */\n  preferredRegion: string | string[] | undefined\n\n  /**\n   * The absolute path to the userland page file.\n   */\n  absolutePagePath: string\n\n  /**\n   * The middleware config for this route.\n   */\n  middlewareConfigBase64: string\n}\n\ntype RouteLoaderPagesOptions = {\n  kind: RouteKind.PAGES\n\n  /**\n   * The page name for this particular route.\n   */\n  page: string\n\n  /**\n   * The preferred region for this route.\n   */\n  preferredRegion: string | string[] | undefined\n\n  /**\n   * The absolute path to the userland page file.\n   */\n  absolutePagePath: string\n\n  /**\n   * The absolute paths to the app path file.\n   */\n  absoluteAppPath: string\n\n  /**\n   * The absolute paths to the document path file.\n   */\n  absoluteDocumentPath: string\n\n  /**\n   * The middleware config for this route.\n   */\n  middlewareConfigBase64: string\n}\n\n/**\n * The options for the route loader.\n */\ntype RouteLoaderOptions = RouteLoaderPagesOptions | RouteLoaderPagesAPIOptions\n\n/**\n * Returns the loader entry for a given page.\n *\n * @param options the options to create the loader entry\n * @returns the encoded loader entry\n */\nexport function getRouteLoaderEntry(options: RouteLoaderOptionsInput): string {\n  switch (options.kind) {\n    case RouteKind.PAGES: {\n      const query: RouteLoaderPagesOptions = {\n        kind: options.kind,\n        page: options.page,\n        preferredRegion: options.preferredRegion,\n        absolutePagePath: options.absolutePagePath,\n        // These are the path references to the internal components that may be\n        // overridden by userland components.\n        absoluteAppPath: options.pages['/_app'],\n        absoluteDocumentPath: options.pages['/_document'],\n        middlewareConfigBase64: encodeToBase64(options.middlewareConfig),\n      }\n\n      return `next-route-loader?${stringify(query)}!`\n    }\n    case RouteKind.PAGES_API: {\n      const query: RouteLoaderPagesAPIOptions = {\n        kind: options.kind,\n        page: options.page,\n        preferredRegion: options.preferredRegion,\n        absolutePagePath: options.absolutePagePath,\n        middlewareConfigBase64: encodeToBase64(options.middlewareConfig),\n      }\n\n      return `next-route-loader?${stringify(query)}!`\n    }\n    default: {\n      throw new Error('Invariant: Unexpected route kind')\n    }\n  }\n}\n\nconst loadPages = async (\n  {\n    page,\n    absolutePagePath,\n    absoluteDocumentPath,\n    absoluteAppPath,\n    preferredRegion,\n    middlewareConfigBase64,\n  }: RouteLoaderPagesOptions,\n  buildInfo: ModuleBuildInfo\n) => {\n  const middlewareConfig: MiddlewareConfig = decodeFromBase64(\n    middlewareConfigBase64\n  )\n\n  // Attach build info to the module.\n  buildInfo.route = {\n    page,\n    absolutePagePath,\n    preferredRegion,\n    middlewareConfig,\n  }\n\n  let file = await loadEntrypoint('pages', {\n    VAR_USERLAND: absolutePagePath,\n    VAR_MODULE_DOCUMENT: absoluteDocumentPath,\n    VAR_MODULE_APP: absoluteAppPath,\n    VAR_DEFINITION_PAGE: normalizePagePath(page),\n    VAR_DEFINITION_PATHNAME: page,\n  })\n\n  if (isInstrumentationHookFile(page)) {\n    // When we're building the instrumentation page (only when the\n    // instrumentation file conflicts with a page also labeled\n    // /instrumentation) hoist the `register` method.\n    file += '\\nexport const register = hoist(userland, \"register\")'\n  }\n\n  return file\n}\n\nconst loadPagesAPI = async (\n  {\n    page,\n    absolutePagePath,\n    preferredRegion,\n    middlewareConfigBase64,\n  }: RouteLoaderPagesAPIOptions,\n  buildInfo: ModuleBuildInfo\n) => {\n  const middlewareConfig: MiddlewareConfig = decodeFromBase64(\n    middlewareConfigBase64\n  )\n\n  // Attach build info to the module.\n  buildInfo.route = {\n    page,\n    absolutePagePath,\n    preferredRegion,\n    middlewareConfig,\n  }\n\n  return await loadEntrypoint('pages-api', {\n    VAR_USERLAND: absolutePagePath,\n    VAR_DEFINITION_PAGE: normalizePagePath(page),\n    VAR_DEFINITION_PATHNAME: page,\n  })\n}\n\n/**\n * Handles the `next-route-loader` options.\n * @returns the loader definition function\n */\nconst loader: webpack.LoaderDefinitionFunction<RouteLoaderOptions> =\n  async function () {\n    if (!this._module) {\n      throw new Error('Invariant: expected this to reference a module')\n    }\n\n    const buildInfo = getModuleBuildInfo(this._module)\n    const opts = this.getOptions()\n\n    switch (opts.kind) {\n      case RouteKind.PAGES: {\n        return await loadPages(opts, buildInfo)\n      }\n      case RouteKind.PAGES_API: {\n        return await loadPagesAPI(opts, buildInfo)\n      }\n      default: {\n        throw new Error('Invariant: Unexpected route kind')\n      }\n    }\n  }\n\nexport default loader\n"], "names": ["getRouteLoaderEntry", "options", "kind", "RouteKind", "PAGES", "query", "page", "preferredRegion", "absolutePagePath", "absoluteAppPath", "pages", "absoluteDocumentPath", "middlewareConfigBase64", "encodeToBase64", "middlewareConfig", "stringify", "PAGES_API", "Error", "loadPages", "buildInfo", "decodeFromBase64", "route", "file", "loadEntrypoint", "VAR_USERLAND", "VAR_MODULE_DOCUMENT", "VAR_MODULE_APP", "VAR_DEFINITION_PAGE", "normalizePagePath", "VAR_DEFINITION_PATHNAME", "isInstrumentationHookFile", "loadPagesAPI", "loader", "_module", "getModuleBuildInfo", "opts", "getOptions"], "mappings": ";;;;;;;;;;;;;;;IA0OA,OAAqB;eAArB;;IAjIgBA,mBAAmB;eAAnBA;;;6BAtGU;oCAInB;2BACmB;mCACQ;uBACe;wBACP;gCACX;AA6FxB,SAASA,oBAAoBC,OAAgC;IAClE,OAAQA,QAAQC,IAAI;QAClB,KAAKC,oBAAS,CAACC,KAAK;YAAE;gBACpB,MAAMC,QAAiC;oBACrCH,MAAMD,QAAQC,IAAI;oBAClBI,MAAML,QAAQK,IAAI;oBAClBC,iBAAiBN,QAAQM,eAAe;oBACxCC,kBAAkBP,QAAQO,gBAAgB;oBAC1C,uEAAuE;oBACvE,qCAAqC;oBACrCC,iBAAiBR,QAAQS,KAAK,CAAC,QAAQ;oBACvCC,sBAAsBV,QAAQS,KAAK,CAAC,aAAa;oBACjDE,wBAAwBC,IAAAA,qBAAc,EAACZ,QAAQa,gBAAgB;gBACjE;gBAEA,OAAO,CAAC,kBAAkB,EAAEC,IAAAA,sBAAS,EAACV,OAAO,CAAC,CAAC;YACjD;QACA,KAAKF,oBAAS,CAACa,SAAS;YAAE;gBACxB,MAAMX,QAAoC;oBACxCH,MAAMD,QAAQC,IAAI;oBAClBI,MAAML,QAAQK,IAAI;oBAClBC,iBAAiBN,QAAQM,eAAe;oBACxCC,kBAAkBP,QAAQO,gBAAgB;oBAC1CI,wBAAwBC,IAAAA,qBAAc,EAACZ,QAAQa,gBAAgB;gBACjE;gBAEA,OAAO,CAAC,kBAAkB,EAAEC,IAAAA,sBAAS,EAACV,OAAO,CAAC,CAAC;YACjD;QACA;YAAS;gBACP,MAAM,qBAA6C,CAA7C,IAAIY,MAAM,qCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA4C;YACpD;IACF;AACF;AAEA,MAAMC,YAAY,OAChB,EACEZ,IAAI,EACJE,gBAAgB,EAChBG,oBAAoB,EACpBF,eAAe,EACfF,eAAe,EACfK,sBAAsB,EACE,EAC1BO;IAEA,MAAML,mBAAqCM,IAAAA,uBAAgB,EACzDR;IAGF,mCAAmC;IACnCO,UAAUE,KAAK,GAAG;QAChBf;QACAE;QACAD;QACAO;IACF;IAEA,IAAIQ,OAAO,MAAMC,IAAAA,8BAAc,EAAC,SAAS;QACvCC,cAAchB;QACdiB,qBAAqBd;QACrBe,gBAAgBjB;QAChBkB,qBAAqBC,IAAAA,oCAAiB,EAACtB;QACvCuB,yBAAyBvB;IAC3B;IAEA,IAAIwB,IAAAA,iCAAyB,EAACxB,OAAO;QACnC,8DAA8D;QAC9D,0DAA0D;QAC1D,iDAAiD;QACjDgB,QAAQ;IACV;IAEA,OAAOA;AACT;AAEA,MAAMS,eAAe,OACnB,EACEzB,IAAI,EACJE,gBAAgB,EAChBD,eAAe,EACfK,sBAAsB,EACK,EAC7BO;IAEA,MAAML,mBAAqCM,IAAAA,uBAAgB,EACzDR;IAGF,mCAAmC;IACnCO,UAAUE,KAAK,GAAG;QAChBf;QACAE;QACAD;QACAO;IACF;IAEA,OAAO,MAAMS,IAAAA,8BAAc,EAAC,aAAa;QACvCC,cAAchB;QACdmB,qBAAqBC,IAAAA,oCAAiB,EAACtB;QACvCuB,yBAAyBvB;IAC3B;AACF;AAEA;;;CAGC,GACD,MAAM0B,SACJ;IACE,IAAI,CAAC,IAAI,CAACC,OAAO,EAAE;QACjB,MAAM,qBAA2D,CAA3D,IAAIhB,MAAM,mDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA0D;IAClE;IAEA,MAAME,YAAYe,IAAAA,sCAAkB,EAAC,IAAI,CAACD,OAAO;IACjD,MAAME,OAAO,IAAI,CAACC,UAAU;IAE5B,OAAQD,KAAKjC,IAAI;QACf,KAAKC,oBAAS,CAACC,KAAK;YAAE;gBACpB,OAAO,MAAMc,UAAUiB,MAAMhB;YAC/B;QACA,KAAKhB,oBAAS,CAACa,SAAS;YAAE;gBACxB,OAAO,MAAMe,aAAaI,MAAMhB;YAClC;QACA;YAAS;gBACP,MAAM,qBAA6C,CAA7C,IAAIF,MAAM,qCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA4C;YACpD;IACF;AACF;MAEF,WAAee"}