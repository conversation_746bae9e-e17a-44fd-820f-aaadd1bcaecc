"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/checkin/page",{

/***/ "(app-pages-browser)/./src/utils/machineOwnership.ts":
/*!***************************************!*\
  !*** ./src/utils/machineOwnership.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   canAdjustTime: () => (/* binding */ canAdjustTime),\n/* harmony export */   getOwnershipBadgeClasses: () => (/* binding */ getOwnershipBadgeClasses),\n/* harmony export */   getOwnershipDisplay: () => (/* binding */ getOwnershipDisplay),\n/* harmony export */   hasOwner: () => (/* binding */ hasOwner),\n/* harmony export */   isCurrentUserOwner: () => (/* binding */ isCurrentUserOwner)\n/* harmony export */ });\n/* harmony import */ var _userIdentification__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./userIdentification */ \"(app-pages-browser)/./src/utils/userIdentification.ts\");\n// Utility functions for machine ownership and user identification\n\n/**\n * Check if the current user owns/started a specific machine\n */ function isCurrentUserOwner(machine) {\n    const currentUserId = (0,_userIdentification__WEBPACK_IMPORTED_MODULE_0__.getDeviceUserId)();\n    return machine.startedByUserId === currentUserId;\n}\n/**\n * Check if a machine has an owner (was started by someone)\n */ function hasOwner(machine) {\n    return !!machine.startedByUserId;\n}\n/**\n * Get display text for machine ownership\n */ function getOwnershipDisplay(machine) {\n    if (!hasOwner(machine)) {\n        return \"\";\n    }\n    if (isCurrentUserOwner(machine)) {\n        return \"Your Machine\";\n    }\n    return \"Started by \".concat(machine.startedByUserId);\n}\n/**\n * Get ownership badge color classes\n */ function getOwnershipBadgeClasses(machine) {\n    if (!hasOwner(machine)) {\n        return \"\";\n    }\n    if (isCurrentUserOwner(machine)) {\n        return \"bg-green-100 text-green-800 border-green-200\";\n    }\n    return \"bg-blue-100 text-blue-800 border-blue-200\";\n}\n/**\n * Check if current user can adjust time for a machine\n * Only available after 10 minutes of machine running\n */ function canAdjustTime(machine) {\n    if (machine.status !== \"running\" || !isCurrentUserOwner(machine)) {\n        return false;\n    }\n    // Check if machine has been running for at least 10 minutes\n    if (machine.startAt) {\n        const now = new Date();\n        const timeSinceStart = now.getTime() - machine.startAt.getTime();\n        const tenMinutesInMs = 10 * 60 * 1000;\n        return timeSinceStart >= tenMinutesInMs;\n    }\n    return false;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/machineOwnership.ts\n"));

/***/ })

});