{"version": 3, "sources": ["../../../src/client/request/search-params.browser.dev.ts"], "sourcesContent": ["import type { SearchParams } from '../../server/request/search-params'\n\nimport { ReflectAdapter } from '../../server/web/spec-extension/adapters/reflect'\nimport {\n  describeStringPropertyAccess,\n  describeHasCheckingStringProperty,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\n\ninterface CacheLifetime {}\nconst CachedSearchParams = new WeakMap<CacheLifetime, Promise<SearchParams>>()\n\nexport function makeUntrackedExoticSearchParamsWithDevWarnings(\n  underlyingSearchParams: SearchParams\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n\n  const promise = Promise.resolve(underlyingSearchParams)\n\n  Object.keys(underlyingSearchParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n      unproxiedProperties.push(prop)\n    } else {\n      proxiedProperties.add(prop)\n      ;(promise as any)[prop] = underlyingSearchParams[prop]\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeStringPropertyAccess('searchParams', prop)\n          warnForSyncAccess(expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return Reflect.set(target, prop, value, receiver)\n    },\n    has(target, prop) {\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeHasCheckingStringProperty(\n            'searchParams',\n            prop\n          )\n          warnForSyncAccess(expression)\n        }\n      }\n      return Reflect.has(target, prop)\n    },\n    ownKeys(target) {\n      warnForSyncSpread()\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedSearchParams.set(underlyingSearchParams, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction warnForSyncAccess(expression: string) {\n  console.error(\n    `A searchParam property was accessed directly with ${expression}. ` +\n      `\\`searchParams\\` should be unwrapped with \\`React.use()\\` before accessing its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction warnForSyncSpread() {\n  console.error(\n    `The keys of \\`searchParams\\` were accessed directly. ` +\n      `\\`searchParams\\` should be unwrapped with \\`React.use()\\` before accessing its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n"], "names": ["makeUntrackedExoticSearchParamsWithDevWarnings", "CachedSearchParams", "WeakMap", "underlyingSearchParams", "cachedSearchParams", "get", "proxiedProperties", "Set", "unproxiedProperties", "promise", "Promise", "resolve", "Object", "keys", "for<PERSON>ach", "prop", "wellKnownProperties", "has", "push", "add", "proxiedPromise", "Proxy", "target", "receiver", "Reflect", "expression", "describeStringPropertyAccess", "warnForSyncAccess", "ReflectAdapter", "set", "value", "delete", "describeHasCheckingStringProperty", "ownKeys", "warnForSyncSpread", "console", "error"], "mappings": ";;;;+BAYgBA;;;eAAAA;;;yBAVe;8BAKxB;AAGP,MAAMC,qBAAqB,IAAIC;AAExB,SAASF,+CACdG,sBAAoC;IAEpC,MAAMC,qBAAqBH,mBAAmBI,GAAG,CAACF;IAClD,IAAIC,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAME,oBAAoB,IAAIC;IAC9B,MAAMC,sBAAqC,EAAE;IAE7C,MAAMC,UAAUC,QAAQC,OAAO,CAACR;IAEhCS,OAAOC,IAAI,CAACV,wBAAwBW,OAAO,CAAC,CAACC;QAC3C,IAAIC,iCAAmB,CAACC,GAAG,CAACF,OAAO;YACjC,kEAAkE;YAClE,kEAAkE;YAClEP,oBAAoBU,IAAI,CAACH;QAC3B,OAAO;YACLT,kBAAkBa,GAAG,CAACJ;YACpBN,OAAe,CAACM,KAAK,GAAGZ,sBAAsB,CAACY,KAAK;QACxD;IACF;IAEA,MAAMK,iBAAiB,IAAIC,MAAMZ,SAAS;QACxCJ,KAAIiB,MAAM,EAAEP,IAAI,EAAEQ,QAAQ;YACxB,IAAI,OAAOR,SAAS,UAAU;gBAC5B,IACE,CAACC,iCAAmB,CAACC,GAAG,CAACF,SACxBT,CAAAA,kBAAkBW,GAAG,CAACF,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BS,QAAQP,GAAG,CAACK,QAAQP,UAAU,KAAI,GACpC;oBACA,MAAMU,aAAaC,IAAAA,0CAA4B,EAAC,gBAAgBX;oBAChEY,kBAAkBF;gBACpB;YACF;YACA,OAAOG,uBAAc,CAACvB,GAAG,CAACiB,QAAQP,MAAMQ;QAC1C;QACAM,KAAIP,MAAM,EAAEP,IAAI,EAAEe,KAAK,EAAEP,QAAQ;YAC/B,IAAI,OAAOR,SAAS,UAAU;gBAC5BT,kBAAkByB,MAAM,CAAChB;YAC3B;YACA,OAAOS,QAAQK,GAAG,CAACP,QAAQP,MAAMe,OAAOP;QAC1C;QACAN,KAAIK,MAAM,EAAEP,IAAI;YACd,IAAI,OAAOA,SAAS,UAAU;gBAC5B,IACE,CAACC,iCAAmB,CAACC,GAAG,CAACF,SACxBT,CAAAA,kBAAkBW,GAAG,CAACF,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BS,QAAQP,GAAG,CAACK,QAAQP,UAAU,KAAI,GACpC;oBACA,MAAMU,aAAaO,IAAAA,+CAAiC,EAClD,gBACAjB;oBAEFY,kBAAkBF;gBACpB;YACF;YACA,OAAOD,QAAQP,GAAG,CAACK,QAAQP;QAC7B;QACAkB,SAAQX,MAAM;YACZY;YACA,OAAOV,QAAQS,OAAO,CAACX;QACzB;IACF;IAEArB,mBAAmB4B,GAAG,CAAC1B,wBAAwBiB;IAC/C,OAAOA;AACT;AAEA,SAASO,kBAAkBF,UAAkB;IAC3CU,QAAQC,KAAK,CACX,AAAC,uDAAoDX,aAAW,OAC7D,4FACA;AAEP;AAEA,SAASS;IACPC,QAAQC,KAAK,CACX,AAAC,wDACE,4FACA;AAEP"}