{"version": 3, "sources": ["../../../src/client/components/layout-router.tsx"], "sourcesContent": ["'use client'\n\nimport type {\n  <PERSON>acheNode,\n  LazyCacheNode,\n  LoadingModuleData,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport type {\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../../server/app-render/types'\nimport type { ErrorComponent } from './error-boundary'\nimport type { FocusAndScrollRef } from './router-reducer/router-reducer-types'\n\nimport React, {\n  useContext,\n  use,\n  startTransition,\n  Suspense,\n  useDeferredValue,\n  type JSX,\n} from 'react'\nimport ReactDOM from 'react-dom'\nimport {\n  LayoutRouterContext,\n  GlobalLayoutRouterContext,\n  TemplateContext,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport { fetchServerResponse } from './router-reducer/fetch-server-response'\nimport { unresolvedThenable } from './unresolved-thenable'\nimport { ErrorBoundary } from './error-boundary'\nimport { matchSegment } from './match-segments'\nimport { handleSmoothScroll } from '../../shared/lib/router/utils/handle-smooth-scroll'\nimport { RedirectBoundary } from './redirect-boundary'\nimport { HTTPAccessFallbackBoundary } from './http-access-fallback/error-boundary'\nimport { createRouterCacheKey } from './router-reducer/create-router-cache-key'\nimport { hasInterceptionRouteInCurrentTree } from './router-reducer/reducers/has-interception-route-in-current-tree'\n\n/**\n * Add refetch marker to router state at the point of the current layout segment.\n * This ensures the response returned is not further down than the current layout segment.\n */\nfunction walkAddRefetch(\n  segmentPathToWalk: FlightSegmentPath | undefined,\n  treeToRecreate: FlightRouterState\n): FlightRouterState {\n  if (segmentPathToWalk) {\n    const [segment, parallelRouteKey] = segmentPathToWalk\n    const isLast = segmentPathToWalk.length === 2\n\n    if (matchSegment(treeToRecreate[0], segment)) {\n      if (treeToRecreate[1].hasOwnProperty(parallelRouteKey)) {\n        if (isLast) {\n          const subTree = walkAddRefetch(\n            undefined,\n            treeToRecreate[1][parallelRouteKey]\n          )\n          return [\n            treeToRecreate[0],\n            {\n              ...treeToRecreate[1],\n              [parallelRouteKey]: [\n                subTree[0],\n                subTree[1],\n                subTree[2],\n                'refetch',\n              ],\n            },\n          ]\n        }\n\n        return [\n          treeToRecreate[0],\n          {\n            ...treeToRecreate[1],\n            [parallelRouteKey]: walkAddRefetch(\n              segmentPathToWalk.slice(2),\n              treeToRecreate[1][parallelRouteKey]\n            ),\n          },\n        ]\n      }\n    }\n  }\n\n  return treeToRecreate\n}\n\nconst __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE = (\n  ReactDOM as any\n).__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE\n\n// TODO-APP: Replace with new React API for finding dom nodes without a `ref` when available\n/**\n * Wraps ReactDOM.findDOMNode with additional logic to hide React Strict Mode warning\n */\nfunction findDOMNode(\n  instance: React.ReactInstance | null | undefined\n): Element | Text | null {\n  // Tree-shake for server bundle\n  if (typeof window === 'undefined') return null\n\n  // __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE.findDOMNode is null during module init.\n  // We need to lazily reference it.\n  const internal_reactDOMfindDOMNode =\n    __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE.findDOMNode\n  return internal_reactDOMfindDOMNode(instance)\n}\n\nconst rectProperties = [\n  'bottom',\n  'height',\n  'left',\n  'right',\n  'top',\n  'width',\n  'x',\n  'y',\n] as const\n/**\n * Check if a HTMLElement is hidden or fixed/sticky position\n */\nfunction shouldSkipElement(element: HTMLElement) {\n  // we ignore fixed or sticky positioned elements since they'll likely pass the \"in-viewport\" check\n  // and will result in a situation we bail on scroll because of something like a fixed nav,\n  // even though the actual page content is offscreen\n  if (['sticky', 'fixed'].includes(getComputedStyle(element).position)) {\n    if (process.env.NODE_ENV === 'development') {\n      console.warn(\n        'Skipping auto-scroll behavior due to `position: sticky` or `position: fixed` on element:',\n        element\n      )\n    }\n    return true\n  }\n\n  // Uses `getBoundingClientRect` to check if the element is hidden instead of `offsetParent`\n  // because `offsetParent` doesn't consider document/body\n  const rect = element.getBoundingClientRect()\n  return rectProperties.every((item) => rect[item] === 0)\n}\n\n/**\n * Check if the top corner of the HTMLElement is in the viewport.\n */\nfunction topOfElementInViewport(element: HTMLElement, viewportHeight: number) {\n  const rect = element.getBoundingClientRect()\n  return rect.top >= 0 && rect.top <= viewportHeight\n}\n\n/**\n * Find the DOM node for a hash fragment.\n * If `top` the page has to scroll to the top of the page. This mirrors the browser's behavior.\n * If the hash fragment is an id, the page has to scroll to the element with that id.\n * If the hash fragment is a name, the page has to scroll to the first element with that name.\n */\nfunction getHashFragmentDomNode(hashFragment: string) {\n  // If the hash fragment is `top` the page has to scroll to the top of the page.\n  if (hashFragment === 'top') {\n    return document.body\n  }\n\n  // If the hash fragment is an id, the page has to scroll to the element with that id.\n  return (\n    document.getElementById(hashFragment) ??\n    // If the hash fragment is a name, the page has to scroll to the first element with that name.\n    document.getElementsByName(hashFragment)[0]\n  )\n}\ninterface ScrollAndFocusHandlerProps {\n  focusAndScrollRef: FocusAndScrollRef\n  children: React.ReactNode\n  segmentPath: FlightSegmentPath\n}\nclass InnerScrollAndFocusHandler extends React.Component<ScrollAndFocusHandlerProps> {\n  handlePotentialScroll = () => {\n    // Handle scroll and focus, it's only applied once in the first useEffect that triggers that changed.\n    const { focusAndScrollRef, segmentPath } = this.props\n\n    if (focusAndScrollRef.apply) {\n      // segmentPaths is an array of segment paths that should be scrolled to\n      // if the current segment path is not in the array, the scroll is not applied\n      // unless the array is empty, in which case the scroll is always applied\n      if (\n        focusAndScrollRef.segmentPaths.length !== 0 &&\n        !focusAndScrollRef.segmentPaths.some((scrollRefSegmentPath) =>\n          segmentPath.every((segment, index) =>\n            matchSegment(segment, scrollRefSegmentPath[index])\n          )\n        )\n      ) {\n        return\n      }\n\n      let domNode:\n        | ReturnType<typeof getHashFragmentDomNode>\n        | ReturnType<typeof findDOMNode> = null\n      const hashFragment = focusAndScrollRef.hashFragment\n\n      if (hashFragment) {\n        domNode = getHashFragmentDomNode(hashFragment)\n      }\n\n      // `findDOMNode` is tricky because it returns just the first child if the component is a fragment.\n      // This already caused a bug where the first child was a <link/> in head.\n      if (!domNode) {\n        domNode = findDOMNode(this)\n      }\n\n      // If there is no DOM node this layout-router level is skipped. It'll be handled higher-up in the tree.\n      if (!(domNode instanceof Element)) {\n        return\n      }\n\n      // Verify if the element is a HTMLElement and if we want to consider it for scroll behavior.\n      // If the element is skipped, try to select the next sibling and try again.\n      while (!(domNode instanceof HTMLElement) || shouldSkipElement(domNode)) {\n        if (process.env.NODE_ENV !== 'production') {\n          if (domNode.parentElement?.localName === 'head') {\n            // TODO: We enter this state when metadata was rendered as part of the page or via Next.js.\n            // This is always a bug in Next.js and caused by React hoisting metadata.\n            // We need to replace `findDOMNode` in favor of Fragment Refs (when available) so that we can skip over metadata.\n          }\n        }\n\n        // No siblings found that match the criteria are found, so handle scroll higher up in the tree instead.\n        if (domNode.nextElementSibling === null) {\n          return\n        }\n        domNode = domNode.nextElementSibling\n      }\n\n      // State is mutated to ensure that the focus and scroll is applied only once.\n      focusAndScrollRef.apply = false\n      focusAndScrollRef.hashFragment = null\n      focusAndScrollRef.segmentPaths = []\n\n      handleSmoothScroll(\n        () => {\n          // In case of hash scroll, we only need to scroll the element into view\n          if (hashFragment) {\n            ;(domNode as HTMLElement).scrollIntoView()\n\n            return\n          }\n          // Store the current viewport height because reading `clientHeight` causes a reflow,\n          // and it won't change during this function.\n          const htmlElement = document.documentElement\n          const viewportHeight = htmlElement.clientHeight\n\n          // If the element's top edge is already in the viewport, exit early.\n          if (topOfElementInViewport(domNode as HTMLElement, viewportHeight)) {\n            return\n          }\n\n          // Otherwise, try scrolling go the top of the document to be backward compatible with pages\n          // scrollIntoView() called on `<html/>` element scrolls horizontally on chrome and firefox (that shouldn't happen)\n          // We could use it to scroll horizontally following RTL but that also seems to be broken - it will always scroll left\n          // scrollLeft = 0 also seems to ignore RTL and manually checking for RTL is too much hassle so we will scroll just vertically\n          htmlElement.scrollTop = 0\n\n          // Scroll to domNode if domNode is not in viewport when scrolled to top of document\n          if (!topOfElementInViewport(domNode as HTMLElement, viewportHeight)) {\n            // Scroll into view doesn't scroll horizontally by default when not needed\n            ;(domNode as HTMLElement).scrollIntoView()\n          }\n        },\n        {\n          // We will force layout by querying domNode position\n          dontForceLayout: true,\n          onlyHashChange: focusAndScrollRef.onlyHashChange,\n        }\n      )\n\n      // Mutate after scrolling so that it can be read by `handleSmoothScroll`\n      focusAndScrollRef.onlyHashChange = false\n\n      // Set focus on the element\n      domNode.focus()\n    }\n  }\n\n  componentDidMount() {\n    this.handlePotentialScroll()\n  }\n\n  componentDidUpdate() {\n    // Because this property is overwritten in handlePotentialScroll it's fine to always run it when true as it'll be set to false for subsequent renders.\n    if (this.props.focusAndScrollRef.apply) {\n      this.handlePotentialScroll()\n    }\n  }\n\n  render() {\n    return this.props.children\n  }\n}\n\nfunction ScrollAndFocusHandler({\n  segmentPath,\n  children,\n}: {\n  segmentPath: FlightSegmentPath\n  children: React.ReactNode\n}) {\n  const context = useContext(GlobalLayoutRouterContext)\n  if (!context) {\n    throw new Error('invariant global layout router not mounted')\n  }\n\n  return (\n    <InnerScrollAndFocusHandler\n      segmentPath={segmentPath}\n      focusAndScrollRef={context.focusAndScrollRef}\n    >\n      {children}\n    </InnerScrollAndFocusHandler>\n  )\n}\n\n/**\n * InnerLayoutRouter handles rendering the provided segment based on the cache.\n */\nfunction InnerLayoutRouter({\n  tree,\n  segmentPath,\n  cacheNode,\n  url,\n}: {\n  tree: FlightRouterState\n  segmentPath: FlightSegmentPath\n  cacheNode: CacheNode\n  url: string\n}) {\n  const context = useContext(GlobalLayoutRouterContext)\n  if (!context) {\n    throw new Error('invariant global layout router not mounted')\n  }\n\n  const { changeByServerResponse, tree: fullTree } = context\n\n  // `rsc` represents the renderable node for this segment.\n\n  // If this segment has a `prefetchRsc`, it's the statically prefetched data.\n  // We should use that on initial render instead of `rsc`. Then we'll switch\n  // to `rsc` when the dynamic response streams in.\n  //\n  // If no prefetch data is available, then we go straight to rendering `rsc`.\n  const resolvedPrefetchRsc =\n    cacheNode.prefetchRsc !== null ? cacheNode.prefetchRsc : cacheNode.rsc\n\n  // We use `useDeferredValue` to handle switching between the prefetched and\n  // final values. The second argument is returned on initial render, then it\n  // re-renders with the first argument.\n  const rsc: any = useDeferredValue(cacheNode.rsc, resolvedPrefetchRsc)\n\n  // `rsc` is either a React node or a promise for a React node, except we\n  // special case `null` to represent that this segment's data is missing. If\n  // it's a promise, we need to unwrap it so we can determine whether or not the\n  // data is missing.\n  const resolvedRsc: React.ReactNode =\n    typeof rsc === 'object' && rsc !== null && typeof rsc.then === 'function'\n      ? use(rsc)\n      : rsc\n\n  if (!resolvedRsc) {\n    // The data for this segment is not available, and there's no pending\n    // navigation that will be able to fulfill it. We need to fetch more from\n    // the server and patch the cache.\n\n    // Check if there's already a pending request.\n    let lazyData = cacheNode.lazyData\n    if (lazyData === null) {\n      /**\n       * Router state with refetch marker added\n       */\n      // TODO-APP: remove ''\n      const refetchTree = walkAddRefetch(['', ...segmentPath], fullTree)\n      const includeNextUrl = hasInterceptionRouteInCurrentTree(fullTree)\n      cacheNode.lazyData = lazyData = fetchServerResponse(\n        new URL(url, location.origin),\n        {\n          flightRouterState: refetchTree,\n          nextUrl: includeNextUrl ? context.nextUrl : null,\n        }\n      ).then((serverResponse) => {\n        startTransition(() => {\n          changeByServerResponse({\n            previousTree: fullTree,\n            serverResponse,\n          })\n        })\n\n        return serverResponse\n      })\n\n      // Suspend while waiting for lazyData to resolve\n      use(lazyData)\n    }\n    // Suspend infinitely as `changeByServerResponse` will cause a different part of the tree to be rendered.\n    // A falsey `resolvedRsc` indicates missing data -- we should not commit that branch, and we need to wait for the data to arrive.\n    use(unresolvedThenable) as never\n  }\n\n  // If we get to this point, then we know we have something we can render.\n  const subtree = (\n    // The layout router context narrows down tree and childNodes at each level.\n    <LayoutRouterContext.Provider\n      value={{\n        parentTree: tree,\n        parentCacheNode: cacheNode,\n        parentSegmentPath: segmentPath,\n\n        // TODO-APP: overriding of url for parallel routes\n        url: url,\n      }}\n    >\n      {resolvedRsc}\n    </LayoutRouterContext.Provider>\n  )\n  // Ensure root layout is not wrapped in a div as the root layout renders `<html>`\n  return subtree\n}\n\n/**\n * Renders suspense boundary with the provided \"loading\" property as the fallback.\n * If no loading property is provided it renders the children without a suspense boundary.\n */\nfunction LoadingBoundary({\n  loading,\n  children,\n}: {\n  loading: LoadingModuleData | Promise<LoadingModuleData>\n  children: React.ReactNode\n}): JSX.Element {\n  // If loading is a promise, unwrap it. This happens in cases where we haven't\n  // yet received the loading data from the server — which includes whether or\n  // not this layout has a loading component at all.\n  //\n  // It's OK to suspend here instead of inside the fallback because this\n  // promise will resolve simultaneously with the data for the segment itself.\n  // So it will never suspend for longer than it would have if we didn't use\n  // a Suspense fallback at all.\n  let loadingModuleData\n  if (\n    typeof loading === 'object' &&\n    loading !== null &&\n    typeof (loading as any).then === 'function'\n  ) {\n    const promiseForLoading = loading as Promise<LoadingModuleData>\n    loadingModuleData = use(promiseForLoading)\n  } else {\n    loadingModuleData = loading as LoadingModuleData\n  }\n\n  if (loadingModuleData) {\n    const loadingRsc = loadingModuleData[0]\n    const loadingStyles = loadingModuleData[1]\n    const loadingScripts = loadingModuleData[2]\n    return (\n      <Suspense\n        fallback={\n          <>\n            {loadingStyles}\n            {loadingScripts}\n            {loadingRsc}\n          </>\n        }\n      >\n        {children}\n      </Suspense>\n    )\n  }\n\n  return <>{children}</>\n}\n\n/**\n * OuterLayoutRouter handles the current segment as well as <Offscreen> rendering of other segments.\n * It can be rendered next to each other with a different `parallelRouterKey`, allowing for Parallel routes.\n */\nexport default function OuterLayoutRouter({\n  parallelRouterKey,\n  error,\n  errorStyles,\n  errorScripts,\n  templateStyles,\n  templateScripts,\n  template,\n  notFound,\n  forbidden,\n  unauthorized,\n}: {\n  parallelRouterKey: string\n  error: ErrorComponent | undefined\n  errorStyles: React.ReactNode | undefined\n  errorScripts: React.ReactNode | undefined\n  templateStyles: React.ReactNode | undefined\n  templateScripts: React.ReactNode | undefined\n  template: React.ReactNode\n  notFound: React.ReactNode | undefined\n  forbidden: React.ReactNode | undefined\n  unauthorized: React.ReactNode | undefined\n}) {\n  const context = useContext(LayoutRouterContext)\n  if (!context) {\n    throw new Error('invariant expected layout router to be mounted')\n  }\n\n  const { parentTree, parentCacheNode, parentSegmentPath, url } = context\n\n  // Get the CacheNode for this segment by reading it from the parent segment's\n  // child map.\n  const parentParallelRoutes = parentCacheNode.parallelRoutes\n  let segmentMap = parentParallelRoutes.get(parallelRouterKey)\n  // If the parallel router cache node does not exist yet, create it.\n  // This writes to the cache when there is no item in the cache yet. It never *overwrites* existing cache items which is why it's safe in concurrent mode.\n  if (!segmentMap) {\n    segmentMap = new Map()\n    parentParallelRoutes.set(parallelRouterKey, segmentMap)\n  }\n\n  // Get the active segment in the tree\n  // The reason arrays are used in the data format is that these are transferred from the server to the browser so it's optimized to save bytes.\n  const parentTreeSegment = parentTree[0]\n  const tree = parentTree[1][parallelRouterKey]\n  const treeSegment = tree[0]\n\n  const segmentPath =\n    parentSegmentPath === null\n      ? // TODO: The root segment value is currently omitted from the segment\n        // path. This has led to a bunch of special cases scattered throughout\n        // the code. We should clean this up.\n        [parallelRouterKey]\n      : parentSegmentPath.concat([parentTreeSegment, parallelRouterKey])\n\n  // The \"state\" key of a segment is the one passed to React — it represents the\n  // identity of the UI tree. Whenever the state key changes, the tree is\n  // recreated and the state is reset. In the App Router model, search params do\n  // not cause state to be lost, so two segments with the same segment path but\n  // different search params should have the same state key.\n  //\n  // The \"cache\" key of a segment, however, *does* include the search params, if\n  // it's possible that the segment accessed the search params on the server.\n  // (This only applies to page segments; layout segments cannot access search\n  // params on the server.)\n  const cacheKey = createRouterCacheKey(treeSegment)\n  const stateKey = createRouterCacheKey(treeSegment, true) // no search params\n\n  // Read segment path from the parallel router cache node.\n  let cacheNode = segmentMap.get(cacheKey)\n  if (cacheNode === undefined) {\n    // When data is not available during rendering client-side we need to fetch\n    // it from the server.\n    const newLazyCacheNode: LazyCacheNode = {\n      lazyData: null,\n      rsc: null,\n      prefetchRsc: null,\n      head: null,\n      prefetchHead: null,\n      parallelRoutes: new Map(),\n      loading: null,\n    }\n\n    // Flight data fetch kicked off during render and put into the cache.\n    cacheNode = newLazyCacheNode\n    segmentMap.set(cacheKey, newLazyCacheNode)\n  }\n\n  /*\n    - Error boundary\n      - Only renders error boundary if error component is provided.\n      - Rendered for each segment to ensure they have their own error state.\n    - Loading boundary\n      - Only renders suspense boundary if loading components is provided.\n      - Rendered for each segment to ensure they have their own loading state.\n      - Passed to the router during rendering to ensure it can be immediately rendered when suspending on a Flight fetch.\n  */\n\n  // TODO: The loading module data for a segment is stored on the parent, then\n  // applied to each of that parent segment's parallel route slots. In the\n  // simple case where there's only one parallel route (the `children` slot),\n  // this is no different from if the loading module data where stored on the\n  // child directly. But I'm not sure this actually makes sense when there are\n  // multiple parallel routes. It's not a huge issue because you always have\n  // the option to define a narrower loading boundary for a particular slot. But\n  // this sort of smells like an implementation accident to me.\n  const loadingModuleData = parentCacheNode.loading\n\n  return (\n    <TemplateContext.Provider\n      key={stateKey}\n      value={\n        <ScrollAndFocusHandler segmentPath={segmentPath}>\n          <ErrorBoundary\n            errorComponent={error}\n            errorStyles={errorStyles}\n            errorScripts={errorScripts}\n          >\n            <LoadingBoundary loading={loadingModuleData}>\n              <HTTPAccessFallbackBoundary\n                notFound={notFound}\n                forbidden={forbidden}\n                unauthorized={unauthorized}\n              >\n                <RedirectBoundary>\n                  <InnerLayoutRouter\n                    url={url}\n                    tree={tree}\n                    cacheNode={cacheNode}\n                    segmentPath={segmentPath}\n                  />\n                </RedirectBoundary>\n              </HTTPAccessFallbackBoundary>\n            </LoadingBoundary>\n          </ErrorBoundary>\n        </ScrollAndFocusHandler>\n      }\n    >\n      {templateStyles}\n      {templateScripts}\n      {template}\n    </TemplateContext.Provider>\n  )\n}\n"], "names": ["OuterLayoutRouter", "walkAddRefetch", "segmentPathToWalk", "treeToRecreate", "segment", "parallelRouteKey", "isLast", "length", "matchSegment", "hasOwnProperty", "subTree", "undefined", "slice", "__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE", "ReactDOM", "findDOMNode", "instance", "window", "internal_reactDOMfindDOMNode", "rectProperties", "shouldSkipElement", "element", "includes", "getComputedStyle", "position", "process", "env", "NODE_ENV", "console", "warn", "rect", "getBoundingClientRect", "every", "item", "topOfElementInViewport", "viewportHeight", "top", "getHashFragmentDomNode", "hashFragment", "document", "body", "getElementById", "getElementsByName", "InnerScrollAndFocusHandler", "React", "Component", "componentDidMount", "handlePotentialScroll", "componentDidUpdate", "props", "focusAndScrollRef", "apply", "render", "children", "segmentPath", "segmentPaths", "some", "scrollRefSegmentPath", "index", "domNode", "Element", "HTMLElement", "parentElement", "localName", "nextElement<PERSON><PERSON>ling", "handleSmoothScroll", "scrollIntoView", "htmlElement", "documentElement", "clientHeight", "scrollTop", "dontForceLayout", "onlyHashChange", "focus", "ScrollAndFocusHandler", "context", "useContext", "GlobalLayoutRouterContext", "Error", "InnerLayoutRouter", "tree", "cacheNode", "url", "changeByServerResponse", "fullTree", "resolvedPrefetchRsc", "prefetchRsc", "rsc", "useDeferredValue", "resolvedRsc", "then", "use", "lazyData", "refetchTree", "includeNextUrl", "hasInterceptionRouteInCurrentTree", "fetchServerResponse", "URL", "location", "origin", "flightRouterState", "nextUrl", "serverResponse", "startTransition", "previousTree", "unresolvedThenable", "subtree", "LayoutRouterContext", "Provider", "value", "parentTree", "parentCacheNode", "parentSegmentPath", "LoadingBoundary", "loading", "loadingModuleData", "promiseForLoading", "loadingRsc", "loadingStyles", "loadingScripts", "Suspense", "fallback", "parallel<PERSON><PERSON>er<PERSON>ey", "error", "errorStyles", "errorScripts", "templateStyles", "templateScripts", "template", "notFound", "forbidden", "unauthorized", "parentParallelRoutes", "parallelRoutes", "segmentMap", "get", "Map", "set", "parentTreeSegment", "treeSegment", "concat", "cache<PERSON>ey", "createRouterCache<PERSON>ey", "stateKey", "newLazyCacheNode", "head", "prefetchHead", "TemplateContext", "Error<PERSON>ou<PERSON><PERSON>", "errorComponent", "HTTPAccessFallbackBoundary", "RedirectBoundary"], "mappings": "AAAA;;;;;+BA6dA;;;CAGC,GACD;;;eAAwBA;;;;;;iEA5cjB;mEACc;+CAKd;qCAC6B;oCACD;+BACL;+BACD;oCACM;kCACF;gCACU;sCACN;mDACa;AAElD;;;CAGC,GACD,SAASC,eACPC,iBAAgD,EAChDC,cAAiC;IAEjC,IAAID,mBAAmB;QACrB,MAAM,CAACE,SAASC,iBAAiB,GAAGH;QACpC,MAAMI,SAASJ,kBAAkBK,MAAM,KAAK;QAE5C,IAAIC,IAAAA,2BAAY,EAACL,cAAc,CAAC,EAAE,EAAEC,UAAU;YAC5C,IAAID,cAAc,CAAC,EAAE,CAACM,cAAc,CAACJ,mBAAmB;gBACtD,IAAIC,QAAQ;oBACV,MAAMI,UAAUT,eACdU,WACAR,cAAc,CAAC,EAAE,CAACE,iBAAiB;oBAErC,OAAO;wBACLF,cAAc,CAAC,EAAE;wBACjB;4BACE,GAAGA,cAAc,CAAC,EAAE;4BACpB,CAACE,iBAAiB,EAAE;gCAClBK,OAAO,CAAC,EAAE;gCACVA,OAAO,CAAC,EAAE;gCACVA,OAAO,CAAC,EAAE;gCACV;6BACD;wBACH;qBACD;gBACH;gBAEA,OAAO;oBACLP,cAAc,CAAC,EAAE;oBACjB;wBACE,GAAGA,cAAc,CAAC,EAAE;wBACpB,CAACE,iBAAiB,EAAEJ,eAClBC,kBAAkBU,KAAK,CAAC,IACxBT,cAAc,CAAC,EAAE,CAACE,iBAAiB;oBAEvC;iBACD;YACH;QACF;IACF;IAEA,OAAOF;AACT;AAEA,MAAMU,+DAA+D,AACnEC,iBAAQ,CACRD,4DAA4D;AAE9D,4FAA4F;AAC5F;;CAEC,GACD,SAASE,YACPC,QAAgD;IAEhD,+BAA+B;IAC/B,IAAI,OAAOC,WAAW,aAAa,OAAO;IAE1C,uGAAuG;IACvG,kCAAkC;IAClC,MAAMC,+BACJL,6DAA6DE,WAAW;IAC1E,OAAOG,6BAA6BF;AACtC;AAEA,MAAMG,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD;;CAEC,GACD,SAASC,kBAAkBC,OAAoB;IAC7C,kGAAkG;IAClG,0FAA0F;IAC1F,mDAAmD;IACnD,IAAI;QAAC;QAAU;KAAQ,CAACC,QAAQ,CAACC,iBAAiBF,SAASG,QAAQ,GAAG;QACpE,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1CC,QAAQC,IAAI,CACV,4FACAR;QAEJ;QACA,OAAO;IACT;IAEA,2FAA2F;IAC3F,wDAAwD;IACxD,MAAMS,OAAOT,QAAQU,qBAAqB;IAC1C,OAAOZ,eAAea,KAAK,CAAC,CAACC,OAASH,IAAI,CAACG,KAAK,KAAK;AACvD;AAEA;;CAEC,GACD,SAASC,uBAAuBb,OAAoB,EAAEc,cAAsB;IAC1E,MAAML,OAAOT,QAAQU,qBAAqB;IAC1C,OAAOD,KAAKM,GAAG,IAAI,KAAKN,KAAKM,GAAG,IAAID;AACtC;AAEA;;;;;CAKC,GACD,SAASE,uBAAuBC,YAAoB;IAClD,+EAA+E;IAC/E,IAAIA,iBAAiB,OAAO;QAC1B,OAAOC,SAASC,IAAI;IACtB;QAIED;IAFF,qFAAqF;IACrF,OACEA,CAAAA,2BAAAA,SAASE,cAAc,CAACH,yBAAxBC,2BACA,8FAA8F;IAC9FA,SAASG,iBAAiB,CAACJ,aAAa,CAAC,EAAE;AAE/C;AAMA,MAAMK,mCAAmCC,cAAK,CAACC,SAAS;IA4GtDC,oBAAoB;QAClB,IAAI,CAACC,qBAAqB;IAC5B;IAEAC,qBAAqB;QACnB,sJAAsJ;QACtJ,IAAI,IAAI,CAACC,KAAK,CAACC,iBAAiB,CAACC,KAAK,EAAE;YACtC,IAAI,CAACJ,qBAAqB;QAC5B;IACF;IAEAK,SAAS;QACP,OAAO,IAAI,CAACH,KAAK,CAACI,QAAQ;IAC5B;;QAzHF,qBACEN,wBAAwB;YACtB,qGAAqG;YACrG,MAAM,EAAEG,iBAAiB,EAAEI,WAAW,EAAE,GAAG,IAAI,CAACL,KAAK;YAErD,IAAIC,kBAAkBC,KAAK,EAAE;gBAC3B,uEAAuE;gBACvE,6EAA6E;gBAC7E,wEAAwE;gBACxE,IACED,kBAAkBK,YAAY,CAAChD,MAAM,KAAK,KAC1C,CAAC2C,kBAAkBK,YAAY,CAACC,IAAI,CAAC,CAACC,uBACpCH,YAAYtB,KAAK,CAAC,CAAC5B,SAASsD,QAC1BlD,IAAAA,2BAAY,EAACJ,SAASqD,oBAAoB,CAACC,MAAM,KAGrD;oBACA;gBACF;gBAEA,IAAIC,UAEiC;gBACrC,MAAMrB,eAAeY,kBAAkBZ,YAAY;gBAEnD,IAAIA,cAAc;oBAChBqB,UAAUtB,uBAAuBC;gBACnC;gBAEA,kGAAkG;gBAClG,yEAAyE;gBACzE,IAAI,CAACqB,SAAS;oBACZA,UAAU5C,YAAY,IAAI;gBAC5B;gBAEA,uGAAuG;gBACvG,IAAI,CAAE4C,CAAAA,mBAAmBC,OAAM,GAAI;oBACjC;gBACF;gBAEA,4FAA4F;gBAC5F,2EAA2E;gBAC3E,MAAO,CAAED,CAAAA,mBAAmBE,WAAU,KAAMzC,kBAAkBuC,SAAU;oBACtE,IAAIlC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;4BACrCgC;wBAAJ,IAAIA,EAAAA,yBAAAA,QAAQG,aAAa,qBAArBH,uBAAuBI,SAAS,MAAK,QAAQ;wBAC/C,2FAA2F;wBAC3F,yEAAyE;wBACzE,iHAAiH;wBACnH;oBACF;oBAEA,uGAAuG;oBACvG,IAAIJ,QAAQK,kBAAkB,KAAK,MAAM;wBACvC;oBACF;oBACAL,UAAUA,QAAQK,kBAAkB;gBACtC;gBAEA,6EAA6E;gBAC7Ed,kBAAkBC,KAAK,GAAG;gBAC1BD,kBAAkBZ,YAAY,GAAG;gBACjCY,kBAAkBK,YAAY,GAAG,EAAE;gBAEnCU,IAAAA,sCAAkB,EAChB;oBACE,uEAAuE;oBACvE,IAAI3B,cAAc;;wBACdqB,QAAwBO,cAAc;wBAExC;oBACF;oBACA,oFAAoF;oBACpF,4CAA4C;oBAC5C,MAAMC,cAAc5B,SAAS6B,eAAe;oBAC5C,MAAMjC,iBAAiBgC,YAAYE,YAAY;oBAE/C,oEAAoE;oBACpE,IAAInC,uBAAuByB,SAAwBxB,iBAAiB;wBAClE;oBACF;oBAEA,2FAA2F;oBAC3F,kHAAkH;oBAClH,qHAAqH;oBACrH,6HAA6H;oBAC7HgC,YAAYG,SAAS,GAAG;oBAExB,mFAAmF;oBACnF,IAAI,CAACpC,uBAAuByB,SAAwBxB,iBAAiB;wBACnE,0EAA0E;;wBACxEwB,QAAwBO,cAAc;oBAC1C;gBACF,GACA;oBACE,oDAAoD;oBACpDK,iBAAiB;oBACjBC,gBAAgBtB,kBAAkBsB,cAAc;gBAClD;gBAGF,wEAAwE;gBACxEtB,kBAAkBsB,cAAc,GAAG;gBAEnC,2BAA2B;gBAC3Bb,QAAQc,KAAK;YACf;QACF;;AAgBF;AAEA,SAASC,sBAAsB,KAM9B;IAN8B,IAAA,EAC7BpB,WAAW,EACXD,QAAQ,EAIT,GAN8B;IAO7B,MAAMsB,UAAUC,IAAAA,iBAAU,EAACC,wDAAyB;IACpD,IAAI,CAACF,SAAS;QACZ,MAAM,qBAAuD,CAAvD,IAAIG,MAAM,+CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAsD;IAC9D;IAEA,qBACE,qBAACnC;QACCW,aAAaA;QACbJ,mBAAmByB,QAAQzB,iBAAiB;kBAE3CG;;AAGP;AAEA;;CAEC,GACD,SAAS0B,kBAAkB,KAU1B;IAV0B,IAAA,EACzBC,IAAI,EACJ1B,WAAW,EACX2B,SAAS,EACTC,GAAG,EAMJ,GAV0B;IAWzB,MAAMP,UAAUC,IAAAA,iBAAU,EAACC,wDAAyB;IACpD,IAAI,CAACF,SAAS;QACZ,MAAM,qBAAuD,CAAvD,IAAIG,MAAM,+CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAsD;IAC9D;IAEA,MAAM,EAAEK,sBAAsB,EAAEH,MAAMI,QAAQ,EAAE,GAAGT;IAEnD,yDAAyD;IAEzD,4EAA4E;IAC5E,2EAA2E;IAC3E,iDAAiD;IACjD,EAAE;IACF,4EAA4E;IAC5E,MAAMU,sBACJJ,UAAUK,WAAW,KAAK,OAAOL,UAAUK,WAAW,GAAGL,UAAUM,GAAG;IAExE,2EAA2E;IAC3E,2EAA2E;IAC3E,sCAAsC;IACtC,MAAMA,MAAWC,IAAAA,uBAAgB,EAACP,UAAUM,GAAG,EAAEF;IAEjD,wEAAwE;IACxE,2EAA2E;IAC3E,8EAA8E;IAC9E,mBAAmB;IACnB,MAAMI,cACJ,OAAOF,QAAQ,YAAYA,QAAQ,QAAQ,OAAOA,IAAIG,IAAI,KAAK,aAC3DC,IAAAA,UAAG,EAACJ,OACJA;IAEN,IAAI,CAACE,aAAa;QAChB,qEAAqE;QACrE,yEAAyE;QACzE,kCAAkC;QAElC,8CAA8C;QAC9C,IAAIG,WAAWX,UAAUW,QAAQ;QACjC,IAAIA,aAAa,MAAM;YACrB;;OAEC,GACD,sBAAsB;YACtB,MAAMC,cAAc5F,eAAe;gBAAC;mBAAOqD;aAAY,EAAE8B;YACzD,MAAMU,iBAAiBC,IAAAA,oEAAiC,EAACX;YACzDH,UAAUW,QAAQ,GAAGA,WAAWI,IAAAA,wCAAmB,EACjD,IAAIC,IAAIf,KAAKgB,SAASC,MAAM,GAC5B;gBACEC,mBAAmBP;gBACnBQ,SAASP,iBAAiBnB,QAAQ0B,OAAO,GAAG;YAC9C,GACAX,IAAI,CAAC,CAACY;gBACNC,IAAAA,sBAAe,EAAC;oBACdpB,uBAAuB;wBACrBqB,cAAcpB;wBACdkB;oBACF;gBACF;gBAEA,OAAOA;YACT;YAEA,gDAAgD;YAChDX,IAAAA,UAAG,EAACC;QACN;QACA,yGAAyG;QACzG,iIAAiI;QACjID,IAAAA,UAAG,EAACc,sCAAkB;IACxB;IAEA,yEAAyE;IACzE,MAAMC,UACJ,4EAA4E;kBAC5E,qBAACC,kDAAmB,CAACC,QAAQ;QAC3BC,OAAO;YACLC,YAAY9B;YACZ+B,iBAAiB9B;YACjB+B,mBAAmB1D;YAEnB,kDAAkD;YAClD4B,KAAKA;QACP;kBAECO;;IAGL,iFAAiF;IACjF,OAAOiB;AACT;AAEA;;;CAGC,GACD,SAASO,gBAAgB,KAMxB;IANwB,IAAA,EACvBC,OAAO,EACP7D,QAAQ,EAIT,GANwB;IAOvB,6EAA6E;IAC7E,4EAA4E;IAC5E,kDAAkD;IAClD,EAAE;IACF,sEAAsE;IACtE,4EAA4E;IAC5E,0EAA0E;IAC1E,8BAA8B;IAC9B,IAAI8D;IACJ,IACE,OAAOD,YAAY,YACnBA,YAAY,QACZ,OAAO,AAACA,QAAgBxB,IAAI,KAAK,YACjC;QACA,MAAM0B,oBAAoBF;QAC1BC,oBAAoBxB,IAAAA,UAAG,EAACyB;IAC1B,OAAO;QACLD,oBAAoBD;IACtB;IAEA,IAAIC,mBAAmB;QACrB,MAAME,aAAaF,iBAAiB,CAAC,EAAE;QACvC,MAAMG,gBAAgBH,iBAAiB,CAAC,EAAE;QAC1C,MAAMI,iBAAiBJ,iBAAiB,CAAC,EAAE;QAC3C,qBACE,qBAACK,eAAQ;YACPC,wBACE;;oBACGH;oBACAC;oBACAF;;;sBAIJhE;;IAGP;IAEA,qBAAO;kBAAGA;;AACZ;AAMe,SAASrD,kBAAkB,KAsBzC;IAtByC,IAAA,EACxC0H,iBAAiB,EACjBC,KAAK,EACLC,WAAW,EACXC,YAAY,EACZC,cAAc,EACdC,eAAe,EACfC,QAAQ,EACRC,QAAQ,EACRC,SAAS,EACTC,YAAY,EAYb,GAtByC;IAuBxC,MAAMxD,UAAUC,IAAAA,iBAAU,EAAC+B,kDAAmB;IAC9C,IAAI,CAAChC,SAAS;QACZ,MAAM,qBAA2D,CAA3D,IAAIG,MAAM,mDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA0D;IAClE;IAEA,MAAM,EAAEgC,UAAU,EAAEC,eAAe,EAAEC,iBAAiB,EAAE9B,GAAG,EAAE,GAAGP;IAEhE,6EAA6E;IAC7E,aAAa;IACb,MAAMyD,uBAAuBrB,gBAAgBsB,cAAc;IAC3D,IAAIC,aAAaF,qBAAqBG,GAAG,CAACb;IAC1C,mEAAmE;IACnE,yJAAyJ;IACzJ,IAAI,CAACY,YAAY;QACfA,aAAa,IAAIE;QACjBJ,qBAAqBK,GAAG,CAACf,mBAAmBY;IAC9C;IAEA,qCAAqC;IACrC,8IAA8I;IAC9I,MAAMI,oBAAoB5B,UAAU,CAAC,EAAE;IACvC,MAAM9B,OAAO8B,UAAU,CAAC,EAAE,CAACY,kBAAkB;IAC7C,MAAMiB,cAAc3D,IAAI,CAAC,EAAE;IAE3B,MAAM1B,cACJ0D,sBAAsB,OAElB,sEAAsE;IACtE,qCAAqC;IACrC;QAACU;KAAkB,GACnBV,kBAAkB4B,MAAM,CAAC;QAACF;QAAmBhB;KAAkB;IAErE,8EAA8E;IAC9E,uEAAuE;IACvE,8EAA8E;IAC9E,6EAA6E;IAC7E,0DAA0D;IAC1D,EAAE;IACF,8EAA8E;IAC9E,2EAA2E;IAC3E,4EAA4E;IAC5E,yBAAyB;IACzB,MAAMmB,WAAWC,IAAAA,0CAAoB,EAACH;IACtC,MAAMI,WAAWD,IAAAA,0CAAoB,EAACH,aAAa,MAAM,mBAAmB;;IAE5E,yDAAyD;IACzD,IAAI1D,YAAYqD,WAAWC,GAAG,CAACM;IAC/B,IAAI5D,cAActE,WAAW;QAC3B,2EAA2E;QAC3E,sBAAsB;QACtB,MAAMqI,mBAAkC;YACtCpD,UAAU;YACVL,KAAK;YACLD,aAAa;YACb2D,MAAM;YACNC,cAAc;YACdb,gBAAgB,IAAIG;YACpBtB,SAAS;QACX;QAEA,qEAAqE;QACrEjC,YAAY+D;QACZV,WAAWG,GAAG,CAACI,UAAUG;IAC3B;IAEA;;;;;;;;EAQA,GAEA,4EAA4E;IAC5E,wEAAwE;IACxE,2EAA2E;IAC3E,2EAA2E;IAC3E,4EAA4E;IAC5E,0EAA0E;IAC1E,8EAA8E;IAC9E,6DAA6D;IAC7D,MAAM7B,oBAAoBJ,gBAAgBG,OAAO;IAEjD,qBACE,sBAACiC,8CAAe,CAACvC,QAAQ;QAEvBC,qBACE,qBAACnC;YAAsBpB,aAAaA;sBAClC,cAAA,qBAAC8F,4BAAa;gBACZC,gBAAgB1B;gBAChBC,aAAaA;gBACbC,cAAcA;0BAEd,cAAA,qBAACZ;oBAAgBC,SAASC;8BACxB,cAAA,qBAACmC,0CAA0B;wBACzBrB,UAAUA;wBACVC,WAAWA;wBACXC,cAAcA;kCAEd,cAAA,qBAACoB,kCAAgB;sCACf,cAAA,qBAACxE;gCACCG,KAAKA;gCACLF,MAAMA;gCACNC,WAAWA;gCACX3B,aAAaA;;;;;;;;YAS1BwE;YACAC;YACAC;;OA9BIe;AAiCX"}