{"version": 3, "sources": ["../../../../src/build/segment-config/app/collect-root-param-keys.ts"], "sourcesContent": ["import { getSegmentParam } from '../../../server/app-render/get-segment-param'\nimport type { LoadComponentsReturnType } from '../../../server/load-components'\nimport type { AppPageModule } from '../../../server/route-modules/app-page/module'\nimport type AppPageRouteModule from '../../../server/route-modules/app-page/module'\nimport type { AppRouteModule } from '../../../server/route-modules/app-route/module'\nimport {\n  isAppPageRouteModule,\n  isAppRouteRouteModule,\n} from '../../../server/route-modules/checks'\nimport { InvariantError } from '../../../shared/lib/invariant-error'\n\nfunction collectAppPageRootParamKeys(\n  routeModule: AppPageRouteModule\n): readonly string[] {\n  let rootParams: string[] = []\n\n  let current = routeModule.userland.loaderTree\n  while (current) {\n    const [name, parallelRoutes, modules] = current\n\n    // If this is a dynamic segment, then we collect the param.\n    const param = getSegmentParam(name)?.param\n    if (param) {\n      rootParams.push(param)\n    }\n\n    // If this has a layout module, then we've found the root layout because\n    // we return once we found the first layout.\n    if (typeof modules.layout !== 'undefined') {\n      return rootParams\n    }\n\n    // This didn't include a root layout, so we need to continue. We don't need\n    // to collect from other parallel routes because we can't have a parallel\n    // route above a root layout.\n    current = parallelRoutes.children\n  }\n\n  // If we didn't find a root layout, then we don't have any params.\n  return []\n}\n\n/**\n * Collects the segments for a given route module.\n *\n * @param components the loaded components\n * @returns the segments for the route module\n */\nexport function collectRootParamKeys({\n  routeModule,\n}: LoadComponentsReturnType<\n  AppPageModule | AppRouteModule\n>): readonly string[] {\n  if (isAppRouteRouteModule(routeModule)) {\n    return []\n  }\n\n  if (isAppPageRouteModule(routeModule)) {\n    return collectAppPageRootParamKeys(routeModule)\n  }\n\n  throw new InvariantError(\n    'Expected a route module to be one of app route or page'\n  )\n}\n"], "names": ["collectRootParamKeys", "collectAppPageRootParamKeys", "routeModule", "rootParams", "current", "userland", "loaderTree", "getSegmentParam", "name", "parallelRoutes", "modules", "param", "push", "layout", "children", "isAppRouteRouteModule", "isAppPageRouteModule", "InvariantError"], "mappings": ";;;;+BAgDgBA;;;eAAAA;;;iCAhDgB;wBAQzB;gCACwB;AAE/B,SAASC,4BACPC,WAA+B;IAE/B,IAAIC,aAAuB,EAAE;IAE7B,IAAIC,UAAUF,YAAYG,QAAQ,CAACC,UAAU;IAC7C,MAAOF,QAAS;YAIAG;QAHd,MAAM,CAACC,MAAMC,gBAAgBC,QAAQ,GAAGN;QAExC,2DAA2D;QAC3D,MAAMO,SAAQJ,mBAAAA,IAAAA,gCAAe,EAACC,0BAAhBD,iBAAuBI,KAAK;QAC1C,IAAIA,OAAO;YACTR,WAAWS,IAAI,CAACD;QAClB;QAEA,wEAAwE;QACxE,4CAA4C;QAC5C,IAAI,OAAOD,QAAQG,MAAM,KAAK,aAAa;YACzC,OAAOV;QACT;QAEA,2EAA2E;QAC3E,yEAAyE;QACzE,6BAA6B;QAC7BC,UAAUK,eAAeK,QAAQ;IACnC;IAEA,kEAAkE;IAClE,OAAO,EAAE;AACX;AAQO,SAASd,qBAAqB,EACnCE,WAAW,EAGZ;IACC,IAAIa,IAAAA,6BAAqB,EAACb,cAAc;QACtC,OAAO,EAAE;IACX;IAEA,IAAIc,IAAAA,4BAAoB,EAACd,cAAc;QACrC,OAAOD,4BAA4BC;IACrC;IAEA,MAAM,qBAEL,CAFK,IAAIe,8BAAc,CACtB,2DADI,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF"}