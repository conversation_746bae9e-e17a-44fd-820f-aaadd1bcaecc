"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Page)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _src_components_DashboardGrid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/src/components/DashboardGrid */ \"(app-pages-browser)/./src/components/DashboardGrid.tsx\");\n/* harmony import */ var _src_components_LaundryCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/components/LaundryCard */ \"(app-pages-browser)/./src/components/LaundryCard.tsx\");\n/* harmony import */ var _src_components_NoiseCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/src/components/NoiseCard */ \"(app-pages-browser)/./src/components/NoiseCard.tsx\");\n/* harmony import */ var _src_components_AnnouncementsCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/src/components/AnnouncementsCard */ \"(app-pages-browser)/./src/components/AnnouncementsCard.tsx\");\n/* harmony import */ var _src_components_HelpMeCard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/src/components/HelpMeCard */ \"(app-pages-browser)/./src/components/HelpMeCard.tsx\");\n/* harmony import */ var _src_components_ConnectionTest__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/src/components/ConnectionTest */ \"(app-pages-browser)/./src/components/ConnectionTest.tsx\");\n/* harmony import */ var _src_components_DebugPanel__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/src/components/DebugPanel */ \"(app-pages-browser)/./src/components/DebugPanel.tsx\");\n/* harmony import */ var _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/src/hooks/useSupabaseData */ \"(app-pages-browser)/./src/hooks/useSupabaseData.tsx\");\n/* harmony import */ var _src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/src/utils/userIdentification */ \"(app-pages-browser)/./src/utils/userIdentification.ts\");\n/* harmony import */ var _src_components_UserSettings__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/src/components/UserSettings */ \"(app-pages-browser)/./src/components/UserSettings.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction Page() {\n    _s();\n    const { refreshData, isLoading, error } = (0,_src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_9__[\"default\"])();\n    const [userName, setUserName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"User\");\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showUserSettings, setShowUserSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Handle client-side hydration\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Page.useEffect\": ()=>{\n            setIsClient(true);\n            setUserName((0,_src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_10__.getUserDisplayName)());\n        }\n    }[\"Page.useEffect\"], []);\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-bgLight flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-8 rounded-lg shadow-lg text-center max-w-md border border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-warn text-lg mb-4 font-semibold\",\n                        children: \"Connection Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"Unable to connect to the database. Please try again later.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: refreshData,\n                        className: \"bg-accent hover:bg-teal-600 text-white px-6 py-2 rounded-lg font-medium transition-colors\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                lineNumber: 30,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8 px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 text-lg\",\n                                    children: [\n                                        \"Welcome back, \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold text-accent\",\n                                            children: userName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 31\n                                        }, this),\n                                        \"!\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 border-2 border-accent border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Refreshing...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowUserSettings(true),\n                                        className: \"px-4 py-2 rounded-lg text-sm text-primary border border-gray-300 hover:bg-gray-50 shadow-sm transition-colors\",\n                                        children: \"⚙️ Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: refreshData,\n                                        disabled: isLoading,\n                                        className: \"px-4 py-2 rounded-lg text-sm text-white font-medium transition-all \".concat(isLoading ? \"bg-gray-400 cursor-not-allowed\" : \"bg-accent hover:bg-teal-600 shadow-md hover:shadow-lg\"),\n                                        children: \"\\uD83D\\uDD04 Refresh\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 pt-4 border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap justify-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        var _document_getElementById;\n                                        return (_document_getElementById = document.getElementById('laundry-machines')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.scrollIntoView({\n                                            behavior: 'smooth'\n                                        });\n                                    },\n                                    className: \"px-4 py-2 bg-white border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-accent hover:text-white hover:border-accent transition-all duration-200 shadow-sm hover:shadow-md\",\n                                    children: \"\\uD83D\\uDC55 Laundry Machines\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        var _document_getElementById;\n                                        return (_document_getElementById = document.getElementById('help-requests')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.scrollIntoView({\n                                            behavior: 'smooth'\n                                        });\n                                    },\n                                    className: \"px-4 py-2 bg-white border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-accent hover:text-white hover:border-accent transition-all duration-200 shadow-sm hover:shadow-md\",\n                                    children: \"\\uD83C\\uDD98 Help Me\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        var _document_getElementById;\n                                        return (_document_getElementById = document.getElementById('community-board')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.scrollIntoView({\n                                            behavior: 'smooth'\n                                        });\n                                    },\n                                    className: \"px-4 py-2 bg-white border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-accent hover:text-white hover:border-accent transition-all duration-200 shadow-sm hover:shadow-md\",\n                                    children: \"\\uD83D\\uDCE2 Announcements\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        var _document_getElementById;\n                                        return (_document_getElementById = document.getElementById('noise-reports')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.scrollIntoView({\n                                            behavior: 'smooth'\n                                        });\n                                    },\n                                    className: \"px-4 py-2 bg-white border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-accent hover:text-white hover:border-accent transition-all duration-200 shadow-sm hover:shadow-md\",\n                                    children: \"\\uD83D\\uDD07 Noise Reports\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_ConnectionTest__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_DashboardGrid__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_LaundryCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, \"laundry-machines\", false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_HelpMeCard__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, \"help-requests\", false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_AnnouncementsCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, \"community-board\", false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_NoiseCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, \"noise-reports\", false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_UserSettings__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: showUserSettings,\n                onClose: ()=>setShowUserSettings(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n             true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_DebugPanel__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                lineNumber: 126,\n                columnNumber: 50\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n_s(Page, \"acmWQi/kYAv5Dh0ikiwC4kmghK0=\", false, function() {\n    return [\n        _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    ];\n});\n_c = Page;\nvar _c;\n$RefreshReg$(_c, \"Page\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});