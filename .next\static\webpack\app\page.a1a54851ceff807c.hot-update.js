"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/HelpMeCard.tsx":
/*!***************************************!*\
  !*** ./src/components/HelpMeCard.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HelpMeCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/src/hooks/useSupabaseData */ \"(app-pages-browser)/./src/hooks/useSupabaseData.tsx\");\n/* harmony import */ var _src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/utils/userIdentification */ \"(app-pages-browser)/./src/utils/userIdentification.ts\");\n/* harmony import */ var _ui_CardWrapper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/CardWrapper */ \"(app-pages-browser)/./src/components/ui/CardWrapper.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction HelpMeCard() {\n    _s();\n    const { helpMe, addHelpRequest, deleteHelpRequest } = (0,_src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const [description, setDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HelpMeCard.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"HelpMeCard.useEffect\"], []);\n    const handleRequestHelp = async ()=>{\n        if (!description.trim() || isSubmitting || !isClient) return;\n        setIsSubmitting(true);\n        const user = (0,_src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_3__.getUserDisplayName)();\n        const success = await addHelpRequest(user, description.trim());\n        if (success) {\n            setDescription(\"\");\n        }\n        setIsSubmitting(false);\n    };\n    const handleDelete = async (id)=>{\n        if (confirm(\"Are you sure you want to delete this help request?\")) {\n            await deleteHelpRequest(id);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_CardWrapper__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        id: \"help-requests\",\n        color: \"bgDark\",\n        className: \"border-l-4 border-purple-500 h-full\",\n        count: helpMe.length,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-3 h-3 bg-purple-500 rounded-full mr-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-primary\",\n                        children: \"\\uD83C\\uDD98 Help Requests\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 bg-white p-4 rounded-lg border border-gray-200 shadow-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                        value: description,\n                        onChange: (e)=>setDescription(e.target.value),\n                        placeholder: \"Describe what you need help with...\",\n                        className: \"w-full border border-gray-300 rounded px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 h-20 resize-none mb-3\",\n                        disabled: isSubmitting || !isClient,\n                        maxLength: 500\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-500 mb-3\",\n                        children: [\n                            description.length,\n                            \"/500 characters\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleRequestHelp,\n                        disabled: isSubmitting || !description.trim() || !isClient,\n                        className: \"w-full p-3 rounded-lg text-white font-medium transition-all duration-200 \".concat(isSubmitting || !description.trim() || !isClient ? \"bg-gray-400 cursor-not-allowed\" : \"bg-purple-500 hover:bg-purple-600 shadow-md hover:shadow-lg transform hover:scale-[1.02]\"),\n                        children: !isClient ? \"Loading...\" : isSubmitting ? \"Requesting...\" : \"🙋‍♂️ Request Help\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3 max-h-64 overflow-y-auto\",\n                children: [\n                    helpMe.map((entry)=>{\n                        const canDelete = isClient && (0,_src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_3__.isCurrentUserPost)(entry.user);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white border border-gray-200 rounded-lg p-3 shadow-sm hover:shadow-md transition-shadow\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-start\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-700 mb-2\",\n                                                children: entry.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center text-xs text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-primary\",\n                                                                children: entry.user\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                                                                lineNumber: 83,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            canDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-accent/10 text-accent px-2 py-1 rounded-full\",\n                                                                children: \"Your post\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                                                                lineNumber: 84,\n                                                                columnNumber: 37\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                                                        lineNumber: 82,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: entry.timestamp.toLocaleString()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                                                        lineNumber: 86,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 17\n                                    }, this),\n                                    canDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleDelete(entry.id),\n                                        className: \"text-warn text-xs hover:text-red-700 ml-2 px-2 py-1 hover:bg-red-50 rounded transition-colors\",\n                                        title: \"Delete your post\",\n                                        children: \"Delete\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 15\n                            }, this)\n                        }, entry.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 13\n                        }, this);\n                    }),\n                    helpMe.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-gray-600 py-4\",\n                        children: \"No help requests yet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 33\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\n_s(HelpMeCard, \"yL1wbSnjAG4l3h4CtaP2HQSNuik=\", false, function() {\n    return [\n        _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n    ];\n});\n_c = HelpMeCard;\nvar _c;\n$RefreshReg$(_c, \"HelpMeCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/HelpMeCard.tsx\n"));

/***/ })

});