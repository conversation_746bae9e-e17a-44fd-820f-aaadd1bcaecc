{"version": 3, "sources": ["../../../../src/client/components/router-reducer/aliased-prefetch-navigations.ts"], "sourcesContent": ["import type {\n  CacheNodeSeedData,\n  FlightRouterState,\n} from '../../../server/app-render/types'\nimport type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport {\n  addSearchParamsIfPageSegment,\n  PAGE_SEGMENT_KEY,\n} from '../../../shared/lib/segment'\nimport type { NormalizedFlightData } from '../../flight-data-helpers'\nimport { createEmptyCacheNode } from '../app-router'\nimport { applyRouterStatePatchToTree } from './apply-router-state-patch-to-tree'\nimport { createHrefFromUrl } from './create-href-from-url'\nimport { createRouterCacheKey } from './create-router-cache-key'\nimport { fillCacheWithNewSubTreeDataButOnlyLoading } from './fill-cache-with-new-subtree-data'\nimport { handleMutable } from './handle-mutable'\nimport type { Mutable, ReadonlyReducerState } from './router-reducer-types'\n\n/**\n * This is a stop-gap until per-segment caching is implemented. It leverages the `aliased` flag that is added\n * to prefetch entries when it's determined that the loading state from that entry should be used for this navigation.\n * This function takes the aliased entry and only applies the loading state to the updated cache node.\n * We should remove this once per-segment fetching is implemented as ideally the prefetch cache will contain a\n * more granular segment map and so the router will be able to simply re-use the loading segment for the new navigation.\n */\nexport function handleAliasedPrefetchEntry(\n  state: ReadonlyReducerState,\n  flightData: string | NormalizedFlightData[],\n  url: URL,\n  mutable: Mutable\n) {\n  let currentTree = state.tree\n  let currentCache = state.cache\n  const href = createHrefFromUrl(url)\n  let applied\n\n  if (typeof flightData === 'string') {\n    return false\n  }\n\n  for (const normalizedFlightData of flightData) {\n    // If the segment doesn't have a loading component, we don't need to do anything.\n    if (!hasLoadingComponentInSeedData(normalizedFlightData.seedData)) {\n      continue\n    }\n\n    let treePatch = normalizedFlightData.tree\n    // Segments are keyed by searchParams (e.g. __PAGE__?{\"foo\":\"bar\"}). We might return a less specific, param-less entry,\n    // so we ensure that the final tree contains the correct searchParams (reflected in the URL) are provided in the updated FlightRouterState tree.\n    // We only do this on the first read, as otherwise we'd be overwriting the searchParams that may have already been set\n    treePatch = addSearchParamsToPageSegments(\n      treePatch,\n      Object.fromEntries(url.searchParams)\n    )\n\n    const { seedData, isRootRender, pathToSegment } = normalizedFlightData\n    // TODO-APP: remove ''\n    const flightSegmentPathWithLeadingEmpty = ['', ...pathToSegment]\n\n    // Segments are keyed by searchParams (e.g. __PAGE__?{\"foo\":\"bar\"}). We might return a less specific, param-less entry,\n    // so we ensure that the final tree contains the correct searchParams (reflected in the URL) are provided in the updated FlightRouterState tree.\n    // We only do this on the first read, as otherwise we'd be overwriting the searchParams that may have already been set\n    treePatch = addSearchParamsToPageSegments(\n      treePatch,\n      Object.fromEntries(url.searchParams)\n    )\n\n    let newTree = applyRouterStatePatchToTree(\n      flightSegmentPathWithLeadingEmpty,\n      currentTree,\n      treePatch,\n      href\n    )\n\n    const newCache = createEmptyCacheNode()\n\n    // The prefetch cache entry was aliased -- this signals that we only fill in the cache with the\n    // loading state and not the actual parallel route seed data.\n    if (isRootRender && seedData) {\n      // Fill in the cache with the new loading / rsc data\n      const rsc = seedData[1]\n      const loading = seedData[3]\n      newCache.loading = loading\n      newCache.rsc = rsc\n\n      // Construct a new tree and apply the aliased loading state for each parallel route\n      fillNewTreeWithOnlyLoadingSegments(\n        newCache,\n        currentCache,\n        treePatch,\n        seedData\n      )\n    } else {\n      // Copy rsc for the root node of the cache.\n      newCache.rsc = currentCache.rsc\n      newCache.prefetchRsc = currentCache.prefetchRsc\n      newCache.loading = currentCache.loading\n      newCache.parallelRoutes = new Map(currentCache.parallelRoutes)\n\n      // copy the loading state only into the leaf node (the part that changed)\n      fillCacheWithNewSubTreeDataButOnlyLoading(\n        newCache,\n        currentCache,\n        normalizedFlightData\n      )\n    }\n\n    // If we don't have an updated tree, there's no reason to update the cache, as the tree\n    // dictates what cache nodes to render.\n    if (newTree) {\n      currentTree = newTree\n      currentCache = newCache\n      applied = true\n    }\n  }\n\n  if (!applied) {\n    return false\n  }\n\n  mutable.patchedTree = currentTree\n  mutable.cache = currentCache\n  mutable.canonicalUrl = href\n  mutable.hashFragment = url.hash\n\n  return handleMutable(state, mutable)\n}\n\nfunction hasLoadingComponentInSeedData(seedData: CacheNodeSeedData | null) {\n  if (!seedData) return false\n\n  const parallelRoutes = seedData[2]\n  const loading = seedData[3]\n\n  if (loading) {\n    return true\n  }\n\n  for (const key in parallelRoutes) {\n    if (hasLoadingComponentInSeedData(parallelRoutes[key])) {\n      return true\n    }\n  }\n\n  return false\n}\n\nfunction fillNewTreeWithOnlyLoadingSegments(\n  newCache: CacheNode,\n  existingCache: CacheNode,\n  routerState: FlightRouterState,\n  cacheNodeSeedData: CacheNodeSeedData | null\n) {\n  const isLastSegment = Object.keys(routerState[1]).length === 0\n  if (isLastSegment) {\n    return\n  }\n\n  for (const key in routerState[1]) {\n    const parallelRouteState = routerState[1][key]\n    const segmentForParallelRoute = parallelRouteState[0]\n    const cacheKey = createRouterCacheKey(segmentForParallelRoute)\n\n    const parallelSeedData =\n      cacheNodeSeedData !== null && cacheNodeSeedData[2][key] !== undefined\n        ? cacheNodeSeedData[2][key]\n        : null\n\n    let newCacheNode: CacheNode\n    if (parallelSeedData !== null) {\n      // New data was sent from the server.\n      const rsc = parallelSeedData[1]\n      const loading = parallelSeedData[3]\n      newCacheNode = {\n        lazyData: null,\n        // copy the layout but null the page segment as that's not meant to be used\n        rsc: segmentForParallelRoute.includes(PAGE_SEGMENT_KEY) ? null : rsc,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading,\n      }\n    } else {\n      // No data available for this node. This will trigger a lazy fetch\n      // during render.\n      newCacheNode = {\n        lazyData: null,\n        rsc: null,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading: null,\n      }\n    }\n\n    const existingParallelRoutes = newCache.parallelRoutes.get(key)\n    if (existingParallelRoutes) {\n      existingParallelRoutes.set(cacheKey, newCacheNode)\n    } else {\n      newCache.parallelRoutes.set(key, new Map([[cacheKey, newCacheNode]]))\n    }\n\n    fillNewTreeWithOnlyLoadingSegments(\n      newCacheNode,\n      existingCache,\n      parallelRouteState,\n      parallelSeedData\n    )\n  }\n}\n\n/**\n * Add search params to the page segments in the flight router state\n * Page segments that are associated with search params have a page segment key\n * followed by a query string. This function will add those params to the page segment.\n * This is useful if we return an aliased prefetch entry (ie, won't have search params)\n * but the canonical router URL has search params.\n */\nexport function addSearchParamsToPageSegments(\n  flightRouterState: FlightRouterState,\n  searchParams: Record<string, string | string[] | undefined>\n): FlightRouterState {\n  const [segment, parallelRoutes, ...rest] = flightRouterState\n\n  // If it's a page segment, modify the segment by adding search params\n  if (segment.includes(PAGE_SEGMENT_KEY)) {\n    const newSegment = addSearchParamsIfPageSegment(segment, searchParams)\n    return [newSegment, parallelRoutes, ...rest]\n  }\n\n  // Otherwise, recurse through the parallel routes and return a new tree\n  const updatedParallelRoutes: { [key: string]: FlightRouterState } = {}\n\n  for (const [key, parallelRoute] of Object.entries(parallelRoutes)) {\n    updatedParallelRoutes[key] = addSearchParamsToPageSegments(\n      parallelRoute,\n      searchParams\n    )\n  }\n\n  return [segment, updatedParallelRoutes, ...rest]\n}\n"], "names": ["addSearchParamsToPageSegments", "handleAliasedPrefetchEntry", "state", "flightData", "url", "mutable", "currentTree", "tree", "currentCache", "cache", "href", "createHrefFromUrl", "applied", "normalizedFlightData", "hasLoadingComponentInSeedData", "seedData", "treePatch", "Object", "fromEntries", "searchParams", "isRootRender", "pathToSegment", "flightSegmentPathWithLeadingEmpty", "newTree", "applyRouterStatePatchToTree", "newCache", "createEmptyCacheNode", "rsc", "loading", "fillNewTreeWithOnlyLoadingSegments", "prefetchRsc", "parallelRoutes", "Map", "fillCacheWithNewSubTreeDataButOnlyLoading", "patchedTree", "canonicalUrl", "hashFragment", "hash", "handleMutable", "key", "existingCache", "routerState", "cacheNodeSeedData", "isLastSegment", "keys", "length", "parallelRouteState", "segmentForParallelRoute", "cache<PERSON>ey", "createRouterCache<PERSON>ey", "parallelSeedData", "undefined", "newCacheNode", "lazyData", "includes", "PAGE_SEGMENT_KEY", "head", "prefetchHead", "existingParallelRoutes", "get", "set", "flightRouterState", "segment", "rest", "newSegment", "addSearchParamsIfPageSegment", "updatedParallelRoutes", "parallelRoute", "entries"], "mappings": ";;;;;;;;;;;;;;;IA4NgBA,6BAA6B;eAA7BA;;IAnMAC,0BAA0B;eAA1BA;;;yBAjBT;2BAE8B;6CACO;mCACV;sCACG;6CACqB;+BAC5B;AAUvB,SAASA,2BACdC,KAA2B,EAC3BC,UAA2C,EAC3CC,GAAQ,EACRC,OAAgB;IAEhB,IAAIC,cAAcJ,MAAMK,IAAI;IAC5B,IAAIC,eAAeN,MAAMO,KAAK;IAC9B,MAAMC,OAAOC,IAAAA,oCAAiB,EAACP;IAC/B,IAAIQ;IAEJ,IAAI,OAAOT,eAAe,UAAU;QAClC,OAAO;IACT;IAEA,KAAK,MAAMU,wBAAwBV,WAAY;QAC7C,iFAAiF;QACjF,IAAI,CAACW,8BAA8BD,qBAAqBE,QAAQ,GAAG;YACjE;QACF;QAEA,IAAIC,YAAYH,qBAAqBN,IAAI;QACzC,uHAAuH;QACvH,gJAAgJ;QAChJ,sHAAsH;QACtHS,YAAYhB,8BACVgB,WACAC,OAAOC,WAAW,CAACd,IAAIe,YAAY;QAGrC,MAAM,EAAEJ,QAAQ,EAAEK,YAAY,EAAEC,aAAa,EAAE,GAAGR;QAClD,sBAAsB;QACtB,MAAMS,oCAAoC;YAAC;eAAOD;SAAc;QAEhE,uHAAuH;QACvH,gJAAgJ;QAChJ,sHAAsH;QACtHL,YAAYhB,8BACVgB,WACAC,OAAOC,WAAW,CAACd,IAAIe,YAAY;QAGrC,IAAII,UAAUC,IAAAA,wDAA2B,EACvCF,mCACAhB,aACAU,WACAN;QAGF,MAAMe,WAAWC,IAAAA,+BAAoB;QAErC,+FAA+F;QAC/F,6DAA6D;QAC7D,IAAIN,gBAAgBL,UAAU;YAC5B,oDAAoD;YACpD,MAAMY,MAAMZ,QAAQ,CAAC,EAAE;YACvB,MAAMa,UAAUb,QAAQ,CAAC,EAAE;YAC3BU,SAASG,OAAO,GAAGA;YACnBH,SAASE,GAAG,GAAGA;YAEf,mFAAmF;YACnFE,mCACEJ,UACAjB,cACAQ,WACAD;QAEJ,OAAO;YACL,2CAA2C;YAC3CU,SAASE,GAAG,GAAGnB,aAAamB,GAAG;YAC/BF,SAASK,WAAW,GAAGtB,aAAasB,WAAW;YAC/CL,SAASG,OAAO,GAAGpB,aAAaoB,OAAO;YACvCH,SAASM,cAAc,GAAG,IAAIC,IAAIxB,aAAauB,cAAc;YAE7D,yEAAyE;YACzEE,IAAAA,sEAAyC,EACvCR,UACAjB,cACAK;QAEJ;QAEA,uFAAuF;QACvF,uCAAuC;QACvC,IAAIU,SAAS;YACXjB,cAAciB;YACdf,eAAeiB;YACfb,UAAU;QACZ;IACF;IAEA,IAAI,CAACA,SAAS;QACZ,OAAO;IACT;IAEAP,QAAQ6B,WAAW,GAAG5B;IACtBD,QAAQI,KAAK,GAAGD;IAChBH,QAAQ8B,YAAY,GAAGzB;IACvBL,QAAQ+B,YAAY,GAAGhC,IAAIiC,IAAI;IAE/B,OAAOC,IAAAA,4BAAa,EAACpC,OAAOG;AAC9B;AAEA,SAASS,8BAA8BC,QAAkC;IACvE,IAAI,CAACA,UAAU,OAAO;IAEtB,MAAMgB,iBAAiBhB,QAAQ,CAAC,EAAE;IAClC,MAAMa,UAAUb,QAAQ,CAAC,EAAE;IAE3B,IAAIa,SAAS;QACX,OAAO;IACT;IAEA,IAAK,MAAMW,OAAOR,eAAgB;QAChC,IAAIjB,8BAA8BiB,cAAc,CAACQ,IAAI,GAAG;YACtD,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA,SAASV,mCACPJ,QAAmB,EACnBe,aAAwB,EACxBC,WAA8B,EAC9BC,iBAA2C;IAE3C,MAAMC,gBAAgB1B,OAAO2B,IAAI,CAACH,WAAW,CAAC,EAAE,EAAEI,MAAM,KAAK;IAC7D,IAAIF,eAAe;QACjB;IACF;IAEA,IAAK,MAAMJ,OAAOE,WAAW,CAAC,EAAE,CAAE;QAChC,MAAMK,qBAAqBL,WAAW,CAAC,EAAE,CAACF,IAAI;QAC9C,MAAMQ,0BAA0BD,kBAAkB,CAAC,EAAE;QACrD,MAAME,WAAWC,IAAAA,0CAAoB,EAACF;QAEtC,MAAMG,mBACJR,sBAAsB,QAAQA,iBAAiB,CAAC,EAAE,CAACH,IAAI,KAAKY,YACxDT,iBAAiB,CAAC,EAAE,CAACH,IAAI,GACzB;QAEN,IAAIa;QACJ,IAAIF,qBAAqB,MAAM;YAC7B,qCAAqC;YACrC,MAAMvB,MAAMuB,gBAAgB,CAAC,EAAE;YAC/B,MAAMtB,UAAUsB,gBAAgB,CAAC,EAAE;YACnCE,eAAe;gBACbC,UAAU;gBACV,2EAA2E;gBAC3E1B,KAAKoB,wBAAwBO,QAAQ,CAACC,yBAAgB,IAAI,OAAO5B;gBACjEG,aAAa;gBACb0B,MAAM;gBACNC,cAAc;gBACd1B,gBAAgB,IAAIC;gBACpBJ;YACF;QACF,OAAO;YACL,kEAAkE;YAClE,iBAAiB;YACjBwB,eAAe;gBACbC,UAAU;gBACV1B,KAAK;gBACLG,aAAa;gBACb0B,MAAM;gBACNC,cAAc;gBACd1B,gBAAgB,IAAIC;gBACpBJ,SAAS;YACX;QACF;QAEA,MAAM8B,yBAAyBjC,SAASM,cAAc,CAAC4B,GAAG,CAACpB;QAC3D,IAAImB,wBAAwB;YAC1BA,uBAAuBE,GAAG,CAACZ,UAAUI;QACvC,OAAO;YACL3B,SAASM,cAAc,CAAC6B,GAAG,CAACrB,KAAK,IAAIP,IAAI;gBAAC;oBAACgB;oBAAUI;iBAAa;aAAC;QACrE;QAEAvB,mCACEuB,cACAZ,eACAM,oBACAI;IAEJ;AACF;AASO,SAASlD,8BACd6D,iBAAoC,EACpC1C,YAA2D;IAE3D,MAAM,CAAC2C,SAAS/B,gBAAgB,GAAGgC,KAAK,GAAGF;IAE3C,qEAAqE;IACrE,IAAIC,QAAQR,QAAQ,CAACC,yBAAgB,GAAG;QACtC,MAAMS,aAAaC,IAAAA,qCAA4B,EAACH,SAAS3C;QACzD,OAAO;YAAC6C;YAAYjC;eAAmBgC;SAAK;IAC9C;IAEA,uEAAuE;IACvE,MAAMG,wBAA8D,CAAC;IAErE,KAAK,MAAM,CAAC3B,KAAK4B,cAAc,IAAIlD,OAAOmD,OAAO,CAACrC,gBAAiB;QACjEmC,qBAAqB,CAAC3B,IAAI,GAAGvC,8BAC3BmE,eACAhD;IAEJ;IAEA,OAAO;QAAC2C;QAASI;WAA0BH;KAAK;AAClD"}