{"version": 3, "sources": ["../../../src/build/next-config-ts/transpile-config.ts"], "sourcesContent": ["import type { Options as SWCOptions } from '@swc/core'\nimport type { CompilerOptions } from 'typescript'\nimport { join } from 'node:path'\nimport { readFile } from 'node:fs/promises'\nimport { deregisterHook, registerHook, requireFromString } from './require-hook'\nimport { parseJsonFile } from '../load-jsconfig'\n\nfunction resolveSWCOptions(\n  cwd: string,\n  compilerOptions: CompilerOptions\n): SWCOptions {\n  const resolvedBaseUrl = join(cwd, compilerOptions.baseUrl ?? '.')\n  return {\n    jsc: {\n      target: 'es5',\n      parser: {\n        syntax: 'typescript',\n      },\n      paths: compilerOptions.paths,\n      baseUrl: resolvedBaseUrl,\n    },\n    module: {\n      type: 'commonjs',\n    },\n    isModule: 'unknown',\n  } satisfies SWCOptions\n}\n\nasync function lazilyGetTSConfig(cwd: string) {\n  let tsConfig: { compilerOptions: CompilerOptions }\n  try {\n    tsConfig = parseJsonFile(join(cwd, 'tsconfig.json'))\n  } catch (error) {\n    // ignore if tsconfig.json does not exist\n    if ((error as NodeJS.ErrnoException).code !== 'ENOENT') {\n      throw error\n    }\n    tsConfig = { compilerOptions: {} }\n  }\n\n  return tsConfig\n}\n\nexport async function transpileConfig({\n  nextConfigPath,\n  cwd,\n}: {\n  nextConfigPath: string\n  cwd: string\n}) {\n  let hasRequire = false\n  try {\n    const { compilerOptions } = await lazilyGetTSConfig(cwd)\n    const swcOptions = resolveSWCOptions(cwd, compilerOptions)\n\n    const nextConfigString = await readFile(nextConfigPath, 'utf8')\n    // lazy require swc since it loads React before even setting NODE_ENV\n    // resulting loading Development React on Production\n    const { transform } = require('../swc')\n    const { code } = await transform(nextConfigString, swcOptions)\n\n    // register require hook only if require exists\n    if (code.includes('require(')) {\n      registerHook(swcOptions)\n      hasRequire = true\n    }\n\n    // filename & extension don't matter here\n    return requireFromString(code, join(cwd, 'next.config.compiled.js'))\n  } catch (error) {\n    throw error\n  } finally {\n    if (hasRequire) {\n      deregisterHook()\n    }\n  }\n}\n"], "names": ["transpileConfig", "resolveSWCOptions", "cwd", "compilerOptions", "resolvedBaseUrl", "join", "baseUrl", "jsc", "target", "parser", "syntax", "paths", "module", "type", "isModule", "lazilyGetTSConfig", "tsConfig", "parseJsonFile", "error", "code", "nextConfigPath", "hasRequire", "swcOptions", "nextConfigString", "readFile", "transform", "require", "includes", "registerHook", "requireFromString", "deregisterHook"], "mappings": ";;;;+BA2CsBA;;;eAAAA;;;0BAzCD;0BACI;6BACuC;8BAClC;AAE9B,SAASC,kBACPC,GAAW,EACXC,eAAgC;IAEhC,MAAMC,kBAAkBC,IAAAA,cAAI,EAACH,KAAKC,gBAAgBG,OAAO,IAAI;IAC7D,OAAO;QACLC,KAAK;YACHC,QAAQ;YACRC,QAAQ;gBACNC,QAAQ;YACV;YACAC,OAAOR,gBAAgBQ,KAAK;YAC5BL,SAASF;QACX;QACAQ,QAAQ;YACNC,MAAM;QACR;QACAC,UAAU;IACZ;AACF;AAEA,eAAeC,kBAAkBb,GAAW;IAC1C,IAAIc;IACJ,IAAI;QACFA,WAAWC,IAAAA,2BAAa,EAACZ,IAAAA,cAAI,EAACH,KAAK;IACrC,EAAE,OAAOgB,OAAO;QACd,yCAAyC;QACzC,IAAI,AAACA,MAAgCC,IAAI,KAAK,UAAU;YACtD,MAAMD;QACR;QACAF,WAAW;YAAEb,iBAAiB,CAAC;QAAE;IACnC;IAEA,OAAOa;AACT;AAEO,eAAehB,gBAAgB,EACpCoB,cAAc,EACdlB,GAAG,EAIJ;IACC,IAAImB,aAAa;IACjB,IAAI;QACF,MAAM,EAAElB,eAAe,EAAE,GAAG,MAAMY,kBAAkBb;QACpD,MAAMoB,aAAarB,kBAAkBC,KAAKC;QAE1C,MAAMoB,mBAAmB,MAAMC,IAAAA,kBAAQ,EAACJ,gBAAgB;QACxD,qEAAqE;QACrE,oDAAoD;QACpD,MAAM,EAAEK,SAAS,EAAE,GAAGC,QAAQ;QAC9B,MAAM,EAAEP,IAAI,EAAE,GAAG,MAAMM,UAAUF,kBAAkBD;QAEnD,+CAA+C;QAC/C,IAAIH,KAAKQ,QAAQ,CAAC,aAAa;YAC7BC,IAAAA,yBAAY,EAACN;YACbD,aAAa;QACf;QAEA,yCAAyC;QACzC,OAAOQ,IAAAA,8BAAiB,EAACV,MAAMd,IAAAA,cAAI,EAACH,KAAK;IAC3C,EAAE,OAAOgB,OAAO;QACd,MAAMA;IACR,SAAU;QACR,IAAIG,YAAY;YACdS,IAAAA,2BAAc;QAChB;IACF;AACF"}