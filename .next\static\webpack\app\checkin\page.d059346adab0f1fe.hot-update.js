"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/checkin/page",{

/***/ "(app-pages-browser)/./app/checkin/page.tsx":
/*!******************************!*\
  !*** ./app/checkin/page.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CheckInPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var qrcode_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! qrcode.react */ \"(app-pages-browser)/./node_modules/.pnpm/qrcode.react@4.2.0_react@19.0.0/node_modules/qrcode.react/lib/esm/index.js\");\n/* harmony import */ var _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/src/hooks/useSupabaseData */ \"(app-pages-browser)/./src/hooks/useSupabaseData.tsx\");\n/* harmony import */ var _src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/src/hooks/useCountdown */ \"(app-pages-browser)/./src/hooks/useCountdown.tsx\");\n/* harmony import */ var _src_utils_machineOwnership__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/src/utils/machineOwnership */ \"(app-pages-browser)/./src/utils/machineOwnership.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction CheckInPage() {\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const machineId = searchParams.get(\"machine\");\n    const { laundry, toggleMachineStatus, reserveMachine, adjustMachineTime, isLoading, refreshData } = (0,_src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    const [machine, setMachine] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [actionResult, setActionResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [customTime, setCustomTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"60\");\n    const [adjustTime, setAdjustTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isAdjusting, setIsAdjusting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [adjustResult, setAdjustResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const countdown = (0,_src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(machine === null || machine === void 0 ? void 0 : machine.endAt, machine === null || machine === void 0 ? void 0 : machine.graceEndAt);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CheckInPage.useEffect\": ()=>{\n            if (!machineId) return;\n            const foundMachine = laundry.find({\n                \"CheckInPage.useEffect.foundMachine\": (m)=>m.id === machineId\n            }[\"CheckInPage.useEffect.foundMachine\"]);\n            setMachine(foundMachine || null);\n        }\n    }[\"CheckInPage.useEffect\"], [\n        machineId,\n        laundry\n    ]);\n    // Reduced auto-refresh to every 10 seconds to prevent conflicts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CheckInPage.useEffect\": ()=>{\n            const interval = setInterval({\n                \"CheckInPage.useEffect.interval\": ()=>{\n                    refreshData();\n                }\n            }[\"CheckInPage.useEffect.interval\"], 10000);\n            return ({\n                \"CheckInPage.useEffect\": ()=>clearInterval(interval)\n            })[\"CheckInPage.useEffect\"];\n        }\n    }[\"CheckInPage.useEffect\"], [\n        refreshData\n    ]);\n    const handleToggle = async ()=>{\n        if (!machineId || isProcessing) return;\n        // Validate time input for free machines\n        if ((machine === null || machine === void 0 ? void 0 : machine.status) === \"free\") {\n            const timeNum = parseInt(customTime);\n            if (isNaN(timeNum) || timeNum < 15 || timeNum > 180) {\n                setActionResult(\"Please enter a valid time limit\");\n                return;\n            }\n        }\n        setIsProcessing(true);\n        setActionResult(\"\");\n        let success = false;\n        if ((machine === null || machine === void 0 ? void 0 : machine.status) === \"free\") {\n            // Always use custom time for starting machines\n            success = await reserveMachine(machineId, \"\".concat(customTime, \" minutes\"));\n        } else {\n            // Use default toggle for stopping/collecting\n            success = await toggleMachineStatus(machineId);\n        }\n        if (success) {\n            setActionResult(\"Status updated successfully!\");\n            // Single refresh after 2 seconds to confirm the change\n            setTimeout(()=>refreshData(), 2000);\n        } else {\n            setActionResult(\"Error updating machine status\");\n        }\n        setIsProcessing(false);\n    };\n    const getStatusDisplay = ()=>{\n        if (!machine) return \"\";\n        switch(machine.status){\n            case \"free\":\n                return \"🟢 Available\";\n            case \"running\":\n                const hours = Math.floor(countdown.secondsLeft / 3600);\n                const minutes = Math.floor(countdown.secondsLeft % 3600 / 60);\n                const seconds = countdown.secondsLeft % 60;\n                return \"\\uD83D\\uDD35 In Use - \".concat(hours, \":\").concat(minutes.toString().padStart(2, \"0\"), \":\").concat(seconds.toString().padStart(2, \"0\"), \" left\");\n            case \"finishedGrace\":\n                // Calculate grace period time remaining\n                let graceTimeDisplay = \"5:00\";\n                if (machine.graceEndAt) {\n                    const graceMinutes = Math.floor(countdown.graceSecondsLeft / 60);\n                    const graceSeconds = countdown.graceSecondsLeft % 60;\n                    graceTimeDisplay = \"\".concat(graceMinutes, \":\").concat(graceSeconds.toString().padStart(2, \"0\"));\n                }\n                return \"⚠️ Please collect items - \".concat(graceTimeDisplay, \" left\");\n            default:\n                return \"Unknown\";\n        }\n    };\n    const getButtonText = ()=>{\n        if (isProcessing) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, this),\n                    \"Processing...\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 108,\n                columnNumber: 9\n            }, this);\n        }\n        const currentUserId = getDeviceUserId();\n        const isOwner = (machine === null || machine === void 0 ? void 0 : machine.startedByUserId) === currentUserId;\n        switch(machine === null || machine === void 0 ? void 0 : machine.status){\n            case \"free\":\n                return \"▶️ Start Using Machine (\".concat(customTime, \" min)\");\n            case \"running\":\n                return isOwner ? \"⏹️ Stop Using Machine\" : \"🚫 Machine in Use by Another User\";\n            case \"finishedGrace\":\n                return isOwner ? \"✅ I've Collected My Items\" : \"🚫 Another User's Items\";\n            default:\n                return \"Update Status\";\n        }\n    };\n    const getButtonColor = ()=>{\n        if (isProcessing) return \"bg-gray-400 cursor-not-allowed\";\n        const currentUserId = getDeviceUserId();\n        const isOwner = (machine === null || machine === void 0 ? void 0 : machine.startedByUserId) === currentUserId;\n        switch(machine === null || machine === void 0 ? void 0 : machine.status){\n            case \"free\":\n                return \"bg-blue-500 hover:bg-blue-600\";\n            case \"running\":\n                return isOwner ? \"bg-red-500 hover:bg-red-600\" : \"bg-gray-400 cursor-not-allowed\";\n            case \"finishedGrace\":\n                return isOwner ? \"bg-green-500 hover:bg-green-600\" : \"bg-gray-400 cursor-not-allowed\";\n            default:\n                return \"bg-gray-500\";\n        }\n    };\n    const isButtonDisabled = ()=>{\n        if (isProcessing) return true;\n        const currentUserId = getDeviceUserId();\n        const isOwner = (machine === null || machine === void 0 ? void 0 : machine.startedByUserId) === currentUserId;\n        // Disable if machine is busy and user is not the owner\n        if (((machine === null || machine === void 0 ? void 0 : machine.status) === \"running\" || (machine === null || machine === void 0 ? void 0 : machine.status) === \"finishedGrace\") && !isOwner) {\n            return true;\n        }\n        return false;\n    };\n    const handleBackToHome = ()=>{\n        router.push(\"/\");\n    };\n    const handleAdjustTime = async ()=>{\n        if (!machineId || isAdjusting) return;\n        const minutesNum = parseInt(adjustTime);\n        if (isNaN(minutesNum) || minutesNum < 1 || minutesNum > 120) {\n            setAdjustResult(\"Please enter a number between 1-120 minutes\");\n            return;\n        }\n        setIsAdjusting(true);\n        setAdjustResult(\"\");\n        const result = await adjustMachineTime(machineId, minutesNum);\n        if (result.success) {\n            setAdjustResult(\"Timer updated successfully\");\n            setAdjustTime(\"\");\n            // Refresh data after successful adjustment\n            setTimeout(()=>refreshData(), 1000);\n        } else {\n            setAdjustResult(result.error || \"Failed to update timer\");\n        }\n        setIsAdjusting(false);\n    };\n    // Custom display names for machines (same logic as LaundryCard)\n    const getDisplayName = (machine)=>{\n        if (!machine) return \"\";\n        const isWasher = machine.name.toLowerCase().includes(\"washer\");\n        if (isWasher) {\n            // Extract number from washer name (e.g., \"Washer 1\" -> \"1\")\n            const numberMatch = machine.name.match(/\\d+/);\n            const number = numberMatch ? numberMatch[0] : \"\";\n            return \"Washer \".concat(number);\n        } else {\n            // For dryers, use \"Dryer 5\" through \"Dryer 8\" based on original number\n            const numberMatch = machine.name.match(/\\d+/);\n            const originalNumber = numberMatch ? Number.parseInt(numberMatch[0]) : 0;\n            const newNumber = originalNumber + 4 // Map 1->5, 2->6, 3->7, 4->8\n            ;\n            return \"Dryer \".concat(newNumber);\n        }\n    };\n    if (isLoading && !machine) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-8 rounded-lg shadow text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-lg\",\n                        children: \"Getting machine information...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 215,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 214,\n            columnNumber: 7\n        }, this);\n    }\n    if (!machineId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-8 rounded-lg shadow text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-600 text-lg mb-4\",\n                        children: \"Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600\",\n                        children: \"No machine ID provided.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 226,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 225,\n            columnNumber: 7\n        }, this);\n    }\n    if (!machine) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-8 rounded-lg shadow text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-600 text-lg mb-4\",\n                        children: \"Machine Not Found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"The requested machine could not be found.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: refreshData,\n                        className: \"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded\",\n                        children: \"Retry Loading\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 237,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 236,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white p-8 rounded-lg shadow text-center max-w-md w-full mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-4\",\n                    children: \"Machine Check-In\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-600 mb-3\",\n                            children: \"Scan to access this page\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white p-3 rounded-lg border-2 border-gray-200 shadow-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(qrcode_react__WEBPACK_IMPORTED_MODULE_3__.QRCodeSVG, {\n                                    value: \"\".concat( true ? window.location.origin : 0, \"/checkin?machine=\").concat(machineId),\n                                    size: 80,\n                                    fgColor: \"#1A1F36\",\n                                    bgColor: \"#FFFFFF\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-lg font-medium\",\n                            children: getDisplayName(machine)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm mt-1 font-semibold \".concat(machine.status === \"free\" ? \"text-green-600\" : machine.status === \"running\" ? \"text-blue-600\" : machine.status === \"finishedGrace\" ? \"text-orange-600\" : \"text-red-600\", \" \").concat(machine.status === \"finishedGrace\" ? \"animate-pulse\" : \"\"),\n                            children: [\n                                \"Status: \",\n                                getStatusDisplay()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, this),\n                        machine.status === \"running\" && machine.endAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500 mt-1\",\n                            children: [\n                                \"Ends at: \",\n                                machine.endAt.toLocaleTimeString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 13\n                        }, this),\n                        machine.status === \"finishedGrace\" && machine.graceEndAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-orange-500 mt-1\",\n                            children: [\n                                \"Grace period ends at: \",\n                                machine.graceEndAt.toLocaleTimeString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 13\n                        }, this),\n                        machine.startedByUserId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-600 mt-2\",\n                            children: (0,_src_utils_machineOwnership__WEBPACK_IMPORTED_MODULE_6__.getOwnershipDisplay)(machine)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 268,\n                    columnNumber: 9\n                }, this),\n                machine.status === \"free\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium text-blue-800 mb-2\",\n                            children: \"Set Time Limit\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"number\",\n                                    value: customTime,\n                                    onChange: (e)=>setCustomTime(e.target.value),\n                                    placeholder: \"Enter minutes\",\n                                    className: \"border border-gray-300 rounded-md p-3 w-24 text-center text-lg font-medium focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-600 font-medium\",\n                                    children: \"minutes\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-blue-600 mt-2 text-center\",\n                            children: \"Add a time limit accordingly\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 11\n                }, this),\n                machine.status === \"running\" && (0,_src_utils_machineOwnership__WEBPACK_IMPORTED_MODULE_6__.isCurrentUserOwner)(machine) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: (0,_src_utils_machineOwnership__WEBPACK_IMPORTED_MODULE_6__.canAdjustTime)(machine) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm font-medium text-blue-800 mb-2\",\n                                children: \"Adjust Timer\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-blue-600 mb-3\",\n                                children: [\n                                    \"Current: \",\n                                    machine.endAt ? Math.max(0, Math.ceil((machine.endAt.getTime() - Date.now()) / (1000 * 60))) : 0,\n                                    \" minutes remaining\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"number\",\n                                        value: adjustTime,\n                                        onChange: (e)=>setAdjustTime(e.target.value),\n                                        placeholder: \"Minutes (1-120)\",\n                                        min: \"1\",\n                                        max: \"120\",\n                                        disabled: isAdjusting,\n                                        className: \"flex-1 border rounded p-2 text-center text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleAdjustTime,\n                                        disabled: isAdjusting || !adjustTime,\n                                        className: \"bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1\",\n                                        children: [\n                                            isAdjusting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 border border-white border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 23\n                                            }, this),\n                                            isAdjusting ? \"Updating...\" : \"Update Timer\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 17\n                            }, this),\n                            adjustResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs p-2 rounded \".concat(adjustResult.includes(\"successfully\") ? \"bg-green-100 text-green-800 border border-green-200\" : \"bg-red-100 text-red-800 border border-red-200\"),\n                                children: adjustResult\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 19\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 15\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 p-4 bg-gray-50 border border-gray-200 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm font-medium text-gray-600 mb-2\",\n                                children: \"Timer Adjustment\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-500\",\n                                children: [\n                                    \"Available in \",\n                                    (0,_src_utils_machineOwnership__WEBPACK_IMPORTED_MODULE_6__.getTimeUntilAdjustmentAvailable)(machine),\n                                    \" minutes\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleToggle,\n                    disabled: isButtonDisabled(),\n                    className: \"w-full p-3 rounded text-white font-medium mb-4 \".concat(getButtonColor()),\n                    children: getButtonText()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 376,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleBackToHome,\n                    className: \"w-full p-3 rounded-lg bg-gray-800 hover:bg-gray-900 text-white font-medium mb-4 transition-colors duration-200 shadow-md\",\n                    children: \"← Back to Home\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 385,\n                    columnNumber: 9\n                }, this),\n                actionResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm mb-4 p-2 rounded \".concat(actionResult.includes(\"Error\") ? \"bg-red-100 text-red-700\" : \"bg-green-100 text-green-700\"),\n                    children: actionResult\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 393,\n                    columnNumber: 11\n                }, this),\n                machine.status === \"finishedGrace\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-orange-50 border border-orange-200 rounded p-3 text-sm text-orange-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium mb-1\",\n                            children: \"⚠️ Grace Period Active\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Please collect your items within the time limit. The machine will become available automatically when the grace period ends.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 405,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 403,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 250,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n        lineNumber: 249,\n        columnNumber: 5\n    }, this);\n}\n_s(CheckInPage, \"v+h7YXyy6gXcj/Rvi3VDQhkCD98=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        _src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n});\n_c = CheckInPage;\nvar _c;\n$RefreshReg$(_c, \"CheckInPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/checkin/page.tsx\n"));

/***/ })

});