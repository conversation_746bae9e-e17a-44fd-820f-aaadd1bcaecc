"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@supabase+postgrest-js@1.19.4";
exports.ids = ["vendor-chunks/@supabase+postgrest-js@1.19.4"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js":
/*!***************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js ***!
  \***************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n// @ts-ignore\nconst node_fetch_1 = __importDefault(__webpack_require__(/*! @supabase/node-fetch */ \"(ssr)/./node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/lib/index.js\"));\nconst PostgrestError_1 = __importDefault(__webpack_require__(/*! ./PostgrestError */ \"(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js\"));\nclass PostgrestBuilder {\n    constructor(builder) {\n        this.shouldThrowOnError = false;\n        this.method = builder.method;\n        this.url = builder.url;\n        this.headers = builder.headers;\n        this.schema = builder.schema;\n        this.body = builder.body;\n        this.shouldThrowOnError = builder.shouldThrowOnError;\n        this.signal = builder.signal;\n        this.isMaybeSingle = builder.isMaybeSingle;\n        if (builder.fetch) {\n            this.fetch = builder.fetch;\n        }\n        else if (typeof fetch === 'undefined') {\n            this.fetch = node_fetch_1.default;\n        }\n        else {\n            this.fetch = fetch;\n        }\n    }\n    /**\n     * If there's an error with the query, throwOnError will reject the promise by\n     * throwing the error instead of returning it as part of a successful response.\n     *\n     * {@link https://github.com/supabase/supabase-js/issues/92}\n     */\n    throwOnError() {\n        this.shouldThrowOnError = true;\n        return this;\n    }\n    /**\n     * Set an HTTP header for the request.\n     */\n    setHeader(name, value) {\n        this.headers = Object.assign({}, this.headers);\n        this.headers[name] = value;\n        return this;\n    }\n    then(onfulfilled, onrejected) {\n        // https://postgrest.org/en/stable/api.html#switching-schemas\n        if (this.schema === undefined) {\n            // skip\n        }\n        else if (['GET', 'HEAD'].includes(this.method)) {\n            this.headers['Accept-Profile'] = this.schema;\n        }\n        else {\n            this.headers['Content-Profile'] = this.schema;\n        }\n        if (this.method !== 'GET' && this.method !== 'HEAD') {\n            this.headers['Content-Type'] = 'application/json';\n        }\n        // NOTE: Invoke w/o `this` to avoid illegal invocation error.\n        // https://github.com/supabase/postgrest-js/pull/247\n        const _fetch = this.fetch;\n        let res = _fetch(this.url.toString(), {\n            method: this.method,\n            headers: this.headers,\n            body: JSON.stringify(this.body),\n            signal: this.signal,\n        }).then(async (res) => {\n            var _a, _b, _c;\n            let error = null;\n            let data = null;\n            let count = null;\n            let status = res.status;\n            let statusText = res.statusText;\n            if (res.ok) {\n                if (this.method !== 'HEAD') {\n                    const body = await res.text();\n                    if (body === '') {\n                        // Prefer: return=minimal\n                    }\n                    else if (this.headers['Accept'] === 'text/csv') {\n                        data = body;\n                    }\n                    else if (this.headers['Accept'] &&\n                        this.headers['Accept'].includes('application/vnd.pgrst.plan+text')) {\n                        data = body;\n                    }\n                    else {\n                        data = JSON.parse(body);\n                    }\n                }\n                const countHeader = (_a = this.headers['Prefer']) === null || _a === void 0 ? void 0 : _a.match(/count=(exact|planned|estimated)/);\n                const contentRange = (_b = res.headers.get('content-range')) === null || _b === void 0 ? void 0 : _b.split('/');\n                if (countHeader && contentRange && contentRange.length > 1) {\n                    count = parseInt(contentRange[1]);\n                }\n                // Temporary partial fix for https://github.com/supabase/postgrest-js/issues/361\n                // Issue persists e.g. for `.insert([...]).select().maybeSingle()`\n                if (this.isMaybeSingle && this.method === 'GET' && Array.isArray(data)) {\n                    if (data.length > 1) {\n                        error = {\n                            // https://github.com/PostgREST/postgrest/blob/a867d79c42419af16c18c3fb019eba8df992626f/src/PostgREST/Error.hs#L553\n                            code: 'PGRST116',\n                            details: `Results contain ${data.length} rows, application/vnd.pgrst.object+json requires 1 row`,\n                            hint: null,\n                            message: 'JSON object requested, multiple (or no) rows returned',\n                        };\n                        data = null;\n                        count = null;\n                        status = 406;\n                        statusText = 'Not Acceptable';\n                    }\n                    else if (data.length === 1) {\n                        data = data[0];\n                    }\n                    else {\n                        data = null;\n                    }\n                }\n            }\n            else {\n                const body = await res.text();\n                try {\n                    error = JSON.parse(body);\n                    // Workaround for https://github.com/supabase/postgrest-js/issues/295\n                    if (Array.isArray(error) && res.status === 404) {\n                        data = [];\n                        error = null;\n                        status = 200;\n                        statusText = 'OK';\n                    }\n                }\n                catch (_d) {\n                    // Workaround for https://github.com/supabase/postgrest-js/issues/295\n                    if (res.status === 404 && body === '') {\n                        status = 204;\n                        statusText = 'No Content';\n                    }\n                    else {\n                        error = {\n                            message: body,\n                        };\n                    }\n                }\n                if (error && this.isMaybeSingle && ((_c = error === null || error === void 0 ? void 0 : error.details) === null || _c === void 0 ? void 0 : _c.includes('0 rows'))) {\n                    error = null;\n                    status = 200;\n                    statusText = 'OK';\n                }\n                if (error && this.shouldThrowOnError) {\n                    throw new PostgrestError_1.default(error);\n                }\n            }\n            const postgrestResponse = {\n                error,\n                data,\n                count,\n                status,\n                statusText,\n            };\n            return postgrestResponse;\n        });\n        if (!this.shouldThrowOnError) {\n            res = res.catch((fetchError) => {\n                var _a, _b, _c;\n                return ({\n                    error: {\n                        message: `${(_a = fetchError === null || fetchError === void 0 ? void 0 : fetchError.name) !== null && _a !== void 0 ? _a : 'FetchError'}: ${fetchError === null || fetchError === void 0 ? void 0 : fetchError.message}`,\n                        details: `${(_b = fetchError === null || fetchError === void 0 ? void 0 : fetchError.stack) !== null && _b !== void 0 ? _b : ''}`,\n                        hint: '',\n                        code: `${(_c = fetchError === null || fetchError === void 0 ? void 0 : fetchError.code) !== null && _c !== void 0 ? _c : ''}`,\n                    },\n                    data: null,\n                    count: null,\n                    status: 0,\n                    statusText: '',\n                });\n            });\n        }\n        return res.then(onfulfilled, onrejected);\n    }\n    /**\n     * Override the type of the returned `data`.\n     *\n     * @typeParam NewResult - The new result type to override with\n     * @deprecated Use overrideTypes<yourType, { merge: false }>() method at the end of your call chain instead\n     */\n    returns() {\n        /* istanbul ignore next */\n        return this;\n    }\n    /**\n     * Override the type of the returned `data` field in the response.\n     *\n     * @typeParam NewResult - The new type to cast the response data to\n     * @typeParam Options - Optional type configuration (defaults to { merge: true })\n     * @typeParam Options.merge - When true, merges the new type with existing return type. When false, replaces the existing types entirely (defaults to true)\n     * @example\n     * ```typescript\n     * // Merge with existing types (default behavior)\n     * const query = supabase\n     *   .from('users')\n     *   .select()\n     *   .overrideTypes<{ custom_field: string }>()\n     *\n     * // Replace existing types completely\n     * const replaceQuery = supabase\n     *   .from('users')\n     *   .select()\n     *   .overrideTypes<{ id: number; name: string }, { merge: false }>()\n     * ```\n     * @returns A PostgrestBuilder instance with the new type\n     */\n    overrideTypes() {\n        return this;\n    }\n}\nexports[\"default\"] = PostgrestBuilder;\n//# sourceMappingURL=PostgrestBuilder.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestClient.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestClient.js ***!
  \**************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst PostgrestQueryBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestQueryBuilder */ \"(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js\"));\nconst PostgrestFilterBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestFilterBuilder */ \"(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js\"));\nconst constants_1 = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/constants.js\");\n/**\n * PostgREST client.\n *\n * @typeParam Database - Types for the schema from the [type\n * generator](https://supabase.com/docs/reference/javascript/next/typescript-support)\n *\n * @typeParam SchemaName - Postgres schema to switch to. Must be a string\n * literal, the same one passed to the constructor. If the schema is not\n * `\"public\"`, this must be supplied manually.\n */\nclass PostgrestClient {\n    // TODO: Add back shouldThrowOnError once we figure out the typings\n    /**\n     * Creates a PostgREST client.\n     *\n     * @param url - URL of the PostgREST endpoint\n     * @param options - Named parameters\n     * @param options.headers - Custom headers\n     * @param options.schema - Postgres schema to switch to\n     * @param options.fetch - Custom fetch\n     */\n    constructor(url, { headers = {}, schema, fetch, } = {}) {\n        this.url = url;\n        this.headers = Object.assign(Object.assign({}, constants_1.DEFAULT_HEADERS), headers);\n        this.schemaName = schema;\n        this.fetch = fetch;\n    }\n    /**\n     * Perform a query on a table or a view.\n     *\n     * @param relation - The table or view name to query\n     */\n    from(relation) {\n        const url = new URL(`${this.url}/${relation}`);\n        return new PostgrestQueryBuilder_1.default(url, {\n            headers: Object.assign({}, this.headers),\n            schema: this.schemaName,\n            fetch: this.fetch,\n        });\n    }\n    /**\n     * Select a schema to query or perform an function (rpc) call.\n     *\n     * The schema needs to be on the list of exposed schemas inside Supabase.\n     *\n     * @param schema - The schema to query\n     */\n    schema(schema) {\n        return new PostgrestClient(this.url, {\n            headers: this.headers,\n            schema,\n            fetch: this.fetch,\n        });\n    }\n    /**\n     * Perform a function call.\n     *\n     * @param fn - The function name to call\n     * @param args - The arguments to pass to the function call\n     * @param options - Named parameters\n     * @param options.head - When set to `true`, `data` will not be returned.\n     * Useful if you only need the count.\n     * @param options.get - When set to `true`, the function will be called with\n     * read-only access mode.\n     * @param options.count - Count algorithm to use to count rows returned by the\n     * function. Only applicable for [set-returning\n     * functions](https://www.postgresql.org/docs/current/functions-srf.html).\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */\n    rpc(fn, args = {}, { head = false, get = false, count, } = {}) {\n        let method;\n        const url = new URL(`${this.url}/rpc/${fn}`);\n        let body;\n        if (head || get) {\n            method = head ? 'HEAD' : 'GET';\n            Object.entries(args)\n                // params with undefined value needs to be filtered out, otherwise it'll\n                // show up as `?param=undefined`\n                .filter(([_, value]) => value !== undefined)\n                // array values need special syntax\n                .map(([name, value]) => [name, Array.isArray(value) ? `{${value.join(',')}}` : `${value}`])\n                .forEach(([name, value]) => {\n                url.searchParams.append(name, value);\n            });\n        }\n        else {\n            method = 'POST';\n            body = args;\n        }\n        const headers = Object.assign({}, this.headers);\n        if (count) {\n            headers['Prefer'] = `count=${count}`;\n        }\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url,\n            headers,\n            schema: this.schemaName,\n            body,\n            fetch: this.fetch,\n            allowEmpty: false,\n        });\n    }\n}\nexports[\"default\"] = PostgrestClient;\n//# sourceMappingURL=PostgrestClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n/**\n * Error format\n *\n * {@link https://postgrest.org/en/stable/api.html?highlight=options#errors-and-http-status-codes}\n */\nclass PostgrestError extends Error {\n    constructor(context) {\n        super(context.message);\n        this.name = 'PostgrestError';\n        this.details = context.details;\n        this.hint = context.hint;\n        this.code = context.code;\n    }\n}\nexports[\"default\"] = PostgrestError;\n//# sourceMappingURL=PostgrestError.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3Bvc3RncmVzdC1qc0AxLjE5LjQvbm9kZV9tb2R1bGVzL0BzdXBhYmFzZS9wb3N0Z3Jlc3QtanMvZGlzdC9janMvUG9zdGdyZXN0RXJyb3IuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Q7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWU7QUFDZiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxtZW1ldFxcRGVza3RvcFxcZG9ybV8yMVxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHN1cGFiYXNlK3Bvc3RncmVzdC1qc0AxLjE5LjRcXG5vZGVfbW9kdWxlc1xcQHN1cGFiYXNlXFxwb3N0Z3Jlc3QtanNcXGRpc3RcXGNqc1xcUG9zdGdyZXN0RXJyb3IuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG4vKipcbiAqIEVycm9yIGZvcm1hdFxuICpcbiAqIHtAbGluayBodHRwczovL3Bvc3RncmVzdC5vcmcvZW4vc3RhYmxlL2FwaS5odG1sP2hpZ2hsaWdodD1vcHRpb25zI2Vycm9ycy1hbmQtaHR0cC1zdGF0dXMtY29kZXN9XG4gKi9cbmNsYXNzIFBvc3RncmVzdEVycm9yIGV4dGVuZHMgRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKGNvbnRleHQpIHtcbiAgICAgICAgc3VwZXIoY29udGV4dC5tZXNzYWdlKTtcbiAgICAgICAgdGhpcy5uYW1lID0gJ1Bvc3RncmVzdEVycm9yJztcbiAgICAgICAgdGhpcy5kZXRhaWxzID0gY29udGV4dC5kZXRhaWxzO1xuICAgICAgICB0aGlzLmhpbnQgPSBjb250ZXh0LmhpbnQ7XG4gICAgICAgIHRoaXMuY29kZSA9IGNvbnRleHQuY29kZTtcbiAgICB9XG59XG5leHBvcnRzLmRlZmF1bHQgPSBQb3N0Z3Jlc3RFcnJvcjtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPVBvc3RncmVzdEVycm9yLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js":
/*!*********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js ***!
  \*********************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst PostgrestTransformBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestTransformBuilder */ \"(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js\"));\nclass PostgrestFilterBuilder extends PostgrestTransformBuilder_1.default {\n    /**\n     * Match only rows where `column` is equal to `value`.\n     *\n     * To check if the value of `column` is NULL, you should use `.is()` instead.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    eq(column, value) {\n        this.url.searchParams.append(column, `eq.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is not equal to `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    neq(column, value) {\n        this.url.searchParams.append(column, `neq.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is greater than `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    gt(column, value) {\n        this.url.searchParams.append(column, `gt.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is greater than or equal to `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    gte(column, value) {\n        this.url.searchParams.append(column, `gte.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is less than `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    lt(column, value) {\n        this.url.searchParams.append(column, `lt.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is less than or equal to `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    lte(column, value) {\n        this.url.searchParams.append(column, `lte.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches `pattern` case-sensitively.\n     *\n     * @param column - The column to filter on\n     * @param pattern - The pattern to match with\n     */\n    like(column, pattern) {\n        this.url.searchParams.append(column, `like.${pattern}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches all of `patterns` case-sensitively.\n     *\n     * @param column - The column to filter on\n     * @param patterns - The patterns to match with\n     */\n    likeAllOf(column, patterns) {\n        this.url.searchParams.append(column, `like(all).{${patterns.join(',')}}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches any of `patterns` case-sensitively.\n     *\n     * @param column - The column to filter on\n     * @param patterns - The patterns to match with\n     */\n    likeAnyOf(column, patterns) {\n        this.url.searchParams.append(column, `like(any).{${patterns.join(',')}}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches `pattern` case-insensitively.\n     *\n     * @param column - The column to filter on\n     * @param pattern - The pattern to match with\n     */\n    ilike(column, pattern) {\n        this.url.searchParams.append(column, `ilike.${pattern}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches all of `patterns` case-insensitively.\n     *\n     * @param column - The column to filter on\n     * @param patterns - The patterns to match with\n     */\n    ilikeAllOf(column, patterns) {\n        this.url.searchParams.append(column, `ilike(all).{${patterns.join(',')}}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches any of `patterns` case-insensitively.\n     *\n     * @param column - The column to filter on\n     * @param patterns - The patterns to match with\n     */\n    ilikeAnyOf(column, patterns) {\n        this.url.searchParams.append(column, `ilike(any).{${patterns.join(',')}}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` IS `value`.\n     *\n     * For non-boolean columns, this is only relevant for checking if the value of\n     * `column` is NULL by setting `value` to `null`.\n     *\n     * For boolean columns, you can also set `value` to `true` or `false` and it\n     * will behave the same way as `.eq()`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    is(column, value) {\n        this.url.searchParams.append(column, `is.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is included in the `values` array.\n     *\n     * @param column - The column to filter on\n     * @param values - The values array to filter with\n     */\n    in(column, values) {\n        const cleanedValues = Array.from(new Set(values))\n            .map((s) => {\n            // handle postgrest reserved characters\n            // https://postgrest.org/en/v7.0.0/api.html#reserved-characters\n            if (typeof s === 'string' && new RegExp('[,()]').test(s))\n                return `\"${s}\"`;\n            else\n                return `${s}`;\n        })\n            .join(',');\n        this.url.searchParams.append(column, `in.(${cleanedValues})`);\n        return this;\n    }\n    /**\n     * Only relevant for jsonb, array, and range columns. Match only rows where\n     * `column` contains every element appearing in `value`.\n     *\n     * @param column - The jsonb, array, or range column to filter on\n     * @param value - The jsonb, array, or range value to filter with\n     */\n    contains(column, value) {\n        if (typeof value === 'string') {\n            // range types can be inclusive '[', ']' or exclusive '(', ')' so just\n            // keep it simple and accept a string\n            this.url.searchParams.append(column, `cs.${value}`);\n        }\n        else if (Array.isArray(value)) {\n            // array\n            this.url.searchParams.append(column, `cs.{${value.join(',')}}`);\n        }\n        else {\n            // json\n            this.url.searchParams.append(column, `cs.${JSON.stringify(value)}`);\n        }\n        return this;\n    }\n    /**\n     * Only relevant for jsonb, array, and range columns. Match only rows where\n     * every element appearing in `column` is contained by `value`.\n     *\n     * @param column - The jsonb, array, or range column to filter on\n     * @param value - The jsonb, array, or range value to filter with\n     */\n    containedBy(column, value) {\n        if (typeof value === 'string') {\n            // range\n            this.url.searchParams.append(column, `cd.${value}`);\n        }\n        else if (Array.isArray(value)) {\n            // array\n            this.url.searchParams.append(column, `cd.{${value.join(',')}}`);\n        }\n        else {\n            // json\n            this.url.searchParams.append(column, `cd.${JSON.stringify(value)}`);\n        }\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where every element in\n     * `column` is greater than any element in `range`.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */\n    rangeGt(column, range) {\n        this.url.searchParams.append(column, `sr.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where every element in\n     * `column` is either contained in `range` or greater than any element in\n     * `range`.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */\n    rangeGte(column, range) {\n        this.url.searchParams.append(column, `nxl.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where every element in\n     * `column` is less than any element in `range`.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */\n    rangeLt(column, range) {\n        this.url.searchParams.append(column, `sl.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where every element in\n     * `column` is either contained in `range` or less than any element in\n     * `range`.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */\n    rangeLte(column, range) {\n        this.url.searchParams.append(column, `nxr.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where `column` is\n     * mutually exclusive to `range` and there can be no element between the two\n     * ranges.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */\n    rangeAdjacent(column, range) {\n        this.url.searchParams.append(column, `adj.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for array and range columns. Match only rows where\n     * `column` and `value` have an element in common.\n     *\n     * @param column - The array or range column to filter on\n     * @param value - The array or range value to filter with\n     */\n    overlaps(column, value) {\n        if (typeof value === 'string') {\n            // range\n            this.url.searchParams.append(column, `ov.${value}`);\n        }\n        else {\n            // array\n            this.url.searchParams.append(column, `ov.{${value.join(',')}}`);\n        }\n        return this;\n    }\n    /**\n     * Only relevant for text and tsvector columns. Match only rows where\n     * `column` matches the query string in `query`.\n     *\n     * @param column - The text or tsvector column to filter on\n     * @param query - The query text to match with\n     * @param options - Named parameters\n     * @param options.config - The text search configuration to use\n     * @param options.type - Change how the `query` text is interpreted\n     */\n    textSearch(column, query, { config, type } = {}) {\n        let typePart = '';\n        if (type === 'plain') {\n            typePart = 'pl';\n        }\n        else if (type === 'phrase') {\n            typePart = 'ph';\n        }\n        else if (type === 'websearch') {\n            typePart = 'w';\n        }\n        const configPart = config === undefined ? '' : `(${config})`;\n        this.url.searchParams.append(column, `${typePart}fts${configPart}.${query}`);\n        return this;\n    }\n    /**\n     * Match only rows where each column in `query` keys is equal to its\n     * associated value. Shorthand for multiple `.eq()`s.\n     *\n     * @param query - The object to filter with, with column names as keys mapped\n     * to their filter values\n     */\n    match(query) {\n        Object.entries(query).forEach(([column, value]) => {\n            this.url.searchParams.append(column, `eq.${value}`);\n        });\n        return this;\n    }\n    /**\n     * Match only rows which doesn't satisfy the filter.\n     *\n     * Unlike most filters, `opearator` and `value` are used as-is and need to\n     * follow [PostgREST\n     * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n     * to make sure they are properly sanitized.\n     *\n     * @param column - The column to filter on\n     * @param operator - The operator to be negated to filter with, following\n     * PostgREST syntax\n     * @param value - The value to filter with, following PostgREST syntax\n     */\n    not(column, operator, value) {\n        this.url.searchParams.append(column, `not.${operator}.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows which satisfy at least one of the filters.\n     *\n     * Unlike most filters, `filters` is used as-is and needs to follow [PostgREST\n     * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n     * to make sure it's properly sanitized.\n     *\n     * It's currently not possible to do an `.or()` filter across multiple tables.\n     *\n     * @param filters - The filters to use, following PostgREST syntax\n     * @param options - Named parameters\n     * @param options.referencedTable - Set this to filter on referenced tables\n     * instead of the parent table\n     * @param options.foreignTable - Deprecated, use `referencedTable` instead\n     */\n    or(filters, { foreignTable, referencedTable = foreignTable, } = {}) {\n        const key = referencedTable ? `${referencedTable}.or` : 'or';\n        this.url.searchParams.append(key, `(${filters})`);\n        return this;\n    }\n    /**\n     * Match only rows which satisfy the filter. This is an escape hatch - you\n     * should use the specific filter methods wherever possible.\n     *\n     * Unlike most filters, `opearator` and `value` are used as-is and need to\n     * follow [PostgREST\n     * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n     * to make sure they are properly sanitized.\n     *\n     * @param column - The column to filter on\n     * @param operator - The operator to filter with, following PostgREST syntax\n     * @param value - The value to filter with, following PostgREST syntax\n     */\n    filter(column, operator, value) {\n        this.url.searchParams.append(column, `${operator}.${value}`);\n        return this;\n    }\n}\nexports[\"default\"] = PostgrestFilterBuilder;\n//# sourceMappingURL=PostgrestFilterBuilder.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js":
/*!********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js ***!
  \********************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst PostgrestFilterBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestFilterBuilder */ \"(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js\"));\nclass PostgrestQueryBuilder {\n    constructor(url, { headers = {}, schema, fetch, }) {\n        this.url = url;\n        this.headers = headers;\n        this.schema = schema;\n        this.fetch = fetch;\n    }\n    /**\n     * Perform a SELECT query on the table or view.\n     *\n     * @param columns - The columns to retrieve, separated by commas. Columns can be renamed when returned with `customName:columnName`\n     *\n     * @param options - Named parameters\n     *\n     * @param options.head - When set to `true`, `data` will not be returned.\n     * Useful if you only need the count.\n     *\n     * @param options.count - Count algorithm to use to count rows in the table or view.\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */\n    select(columns, { head = false, count, } = {}) {\n        const method = head ? 'HEAD' : 'GET';\n        // Remove whitespaces except when quoted\n        let quoted = false;\n        const cleanedColumns = (columns !== null && columns !== void 0 ? columns : '*')\n            .split('')\n            .map((c) => {\n            if (/\\s/.test(c) && !quoted) {\n                return '';\n            }\n            if (c === '\"') {\n                quoted = !quoted;\n            }\n            return c;\n        })\n            .join('');\n        this.url.searchParams.set('select', cleanedColumns);\n        if (count) {\n            this.headers['Prefer'] = `count=${count}`;\n        }\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url: this.url,\n            headers: this.headers,\n            schema: this.schema,\n            fetch: this.fetch,\n            allowEmpty: false,\n        });\n    }\n    /**\n     * Perform an INSERT into the table or view.\n     *\n     * By default, inserted rows are not returned. To return it, chain the call\n     * with `.select()`.\n     *\n     * @param values - The values to insert. Pass an object to insert a single row\n     * or an array to insert multiple rows.\n     *\n     * @param options - Named parameters\n     *\n     * @param options.count - Count algorithm to use to count inserted rows.\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     *\n     * @param options.defaultToNull - Make missing fields default to `null`.\n     * Otherwise, use the default value for the column. Only applies for bulk\n     * inserts.\n     */\n    insert(values, { count, defaultToNull = true, } = {}) {\n        const method = 'POST';\n        const prefersHeaders = [];\n        if (this.headers['Prefer']) {\n            prefersHeaders.push(this.headers['Prefer']);\n        }\n        if (count) {\n            prefersHeaders.push(`count=${count}`);\n        }\n        if (!defaultToNull) {\n            prefersHeaders.push('missing=default');\n        }\n        this.headers['Prefer'] = prefersHeaders.join(',');\n        if (Array.isArray(values)) {\n            const columns = values.reduce((acc, x) => acc.concat(Object.keys(x)), []);\n            if (columns.length > 0) {\n                const uniqueColumns = [...new Set(columns)].map((column) => `\"${column}\"`);\n                this.url.searchParams.set('columns', uniqueColumns.join(','));\n            }\n        }\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url: this.url,\n            headers: this.headers,\n            schema: this.schema,\n            body: values,\n            fetch: this.fetch,\n            allowEmpty: false,\n        });\n    }\n    /**\n     * Perform an UPSERT on the table or view. Depending on the column(s) passed\n     * to `onConflict`, `.upsert()` allows you to perform the equivalent of\n     * `.insert()` if a row with the corresponding `onConflict` columns doesn't\n     * exist, or if it does exist, perform an alternative action depending on\n     * `ignoreDuplicates`.\n     *\n     * By default, upserted rows are not returned. To return it, chain the call\n     * with `.select()`.\n     *\n     * @param values - The values to upsert with. Pass an object to upsert a\n     * single row or an array to upsert multiple rows.\n     *\n     * @param options - Named parameters\n     *\n     * @param options.onConflict - Comma-separated UNIQUE column(s) to specify how\n     * duplicate rows are determined. Two rows are duplicates if all the\n     * `onConflict` columns are equal.\n     *\n     * @param options.ignoreDuplicates - If `true`, duplicate rows are ignored. If\n     * `false`, duplicate rows are merged with existing rows.\n     *\n     * @param options.count - Count algorithm to use to count upserted rows.\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     *\n     * @param options.defaultToNull - Make missing fields default to `null`.\n     * Otherwise, use the default value for the column. This only applies when\n     * inserting new rows, not when merging with existing rows under\n     * `ignoreDuplicates: false`. This also only applies when doing bulk upserts.\n     */\n    upsert(values, { onConflict, ignoreDuplicates = false, count, defaultToNull = true, } = {}) {\n        const method = 'POST';\n        const prefersHeaders = [`resolution=${ignoreDuplicates ? 'ignore' : 'merge'}-duplicates`];\n        if (onConflict !== undefined)\n            this.url.searchParams.set('on_conflict', onConflict);\n        if (this.headers['Prefer']) {\n            prefersHeaders.push(this.headers['Prefer']);\n        }\n        if (count) {\n            prefersHeaders.push(`count=${count}`);\n        }\n        if (!defaultToNull) {\n            prefersHeaders.push('missing=default');\n        }\n        this.headers['Prefer'] = prefersHeaders.join(',');\n        if (Array.isArray(values)) {\n            const columns = values.reduce((acc, x) => acc.concat(Object.keys(x)), []);\n            if (columns.length > 0) {\n                const uniqueColumns = [...new Set(columns)].map((column) => `\"${column}\"`);\n                this.url.searchParams.set('columns', uniqueColumns.join(','));\n            }\n        }\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url: this.url,\n            headers: this.headers,\n            schema: this.schema,\n            body: values,\n            fetch: this.fetch,\n            allowEmpty: false,\n        });\n    }\n    /**\n     * Perform an UPDATE on the table or view.\n     *\n     * By default, updated rows are not returned. To return it, chain the call\n     * with `.select()` after filters.\n     *\n     * @param values - The values to update with\n     *\n     * @param options - Named parameters\n     *\n     * @param options.count - Count algorithm to use to count updated rows.\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */\n    update(values, { count, } = {}) {\n        const method = 'PATCH';\n        const prefersHeaders = [];\n        if (this.headers['Prefer']) {\n            prefersHeaders.push(this.headers['Prefer']);\n        }\n        if (count) {\n            prefersHeaders.push(`count=${count}`);\n        }\n        this.headers['Prefer'] = prefersHeaders.join(',');\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url: this.url,\n            headers: this.headers,\n            schema: this.schema,\n            body: values,\n            fetch: this.fetch,\n            allowEmpty: false,\n        });\n    }\n    /**\n     * Perform a DELETE on the table or view.\n     *\n     * By default, deleted rows are not returned. To return it, chain the call\n     * with `.select()` after filters.\n     *\n     * @param options - Named parameters\n     *\n     * @param options.count - Count algorithm to use to count deleted rows.\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */\n    delete({ count, } = {}) {\n        const method = 'DELETE';\n        const prefersHeaders = [];\n        if (count) {\n            prefersHeaders.push(`count=${count}`);\n        }\n        if (this.headers['Prefer']) {\n            prefersHeaders.unshift(this.headers['Prefer']);\n        }\n        this.headers['Prefer'] = prefersHeaders.join(',');\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url: this.url,\n            headers: this.headers,\n            schema: this.schema,\n            fetch: this.fetch,\n            allowEmpty: false,\n        });\n    }\n}\nexports[\"default\"] = PostgrestQueryBuilder;\n//# sourceMappingURL=PostgrestQueryBuilder.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js":
/*!************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js ***!
  \************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst PostgrestBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestBuilder */ \"(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js\"));\nclass PostgrestTransformBuilder extends PostgrestBuilder_1.default {\n    /**\n     * Perform a SELECT on the query result.\n     *\n     * By default, `.insert()`, `.update()`, `.upsert()`, and `.delete()` do not\n     * return modified rows. By calling this method, modified rows are returned in\n     * `data`.\n     *\n     * @param columns - The columns to retrieve, separated by commas\n     */\n    select(columns) {\n        // Remove whitespaces except when quoted\n        let quoted = false;\n        const cleanedColumns = (columns !== null && columns !== void 0 ? columns : '*')\n            .split('')\n            .map((c) => {\n            if (/\\s/.test(c) && !quoted) {\n                return '';\n            }\n            if (c === '\"') {\n                quoted = !quoted;\n            }\n            return c;\n        })\n            .join('');\n        this.url.searchParams.set('select', cleanedColumns);\n        if (this.headers['Prefer']) {\n            this.headers['Prefer'] += ',';\n        }\n        this.headers['Prefer'] += 'return=representation';\n        return this;\n    }\n    /**\n     * Order the query result by `column`.\n     *\n     * You can call this method multiple times to order by multiple columns.\n     *\n     * You can order referenced tables, but it only affects the ordering of the\n     * parent table if you use `!inner` in the query.\n     *\n     * @param column - The column to order by\n     * @param options - Named parameters\n     * @param options.ascending - If `true`, the result will be in ascending order\n     * @param options.nullsFirst - If `true`, `null`s appear first. If `false`,\n     * `null`s appear last.\n     * @param options.referencedTable - Set this to order a referenced table by\n     * its columns\n     * @param options.foreignTable - Deprecated, use `options.referencedTable`\n     * instead\n     */\n    order(column, { ascending = true, nullsFirst, foreignTable, referencedTable = foreignTable, } = {}) {\n        const key = referencedTable ? `${referencedTable}.order` : 'order';\n        const existingOrder = this.url.searchParams.get(key);\n        this.url.searchParams.set(key, `${existingOrder ? `${existingOrder},` : ''}${column}.${ascending ? 'asc' : 'desc'}${nullsFirst === undefined ? '' : nullsFirst ? '.nullsfirst' : '.nullslast'}`);\n        return this;\n    }\n    /**\n     * Limit the query result by `count`.\n     *\n     * @param count - The maximum number of rows to return\n     * @param options - Named parameters\n     * @param options.referencedTable - Set this to limit rows of referenced\n     * tables instead of the parent table\n     * @param options.foreignTable - Deprecated, use `options.referencedTable`\n     * instead\n     */\n    limit(count, { foreignTable, referencedTable = foreignTable, } = {}) {\n        const key = typeof referencedTable === 'undefined' ? 'limit' : `${referencedTable}.limit`;\n        this.url.searchParams.set(key, `${count}`);\n        return this;\n    }\n    /**\n     * Limit the query result by starting at an offset `from` and ending at the offset `to`.\n     * Only records within this range are returned.\n     * This respects the query order and if there is no order clause the range could behave unexpectedly.\n     * The `from` and `to` values are 0-based and inclusive: `range(1, 3)` will include the second, third\n     * and fourth rows of the query.\n     *\n     * @param from - The starting index from which to limit the result\n     * @param to - The last index to which to limit the result\n     * @param options - Named parameters\n     * @param options.referencedTable - Set this to limit rows of referenced\n     * tables instead of the parent table\n     * @param options.foreignTable - Deprecated, use `options.referencedTable`\n     * instead\n     */\n    range(from, to, { foreignTable, referencedTable = foreignTable, } = {}) {\n        const keyOffset = typeof referencedTable === 'undefined' ? 'offset' : `${referencedTable}.offset`;\n        const keyLimit = typeof referencedTable === 'undefined' ? 'limit' : `${referencedTable}.limit`;\n        this.url.searchParams.set(keyOffset, `${from}`);\n        // Range is inclusive, so add 1\n        this.url.searchParams.set(keyLimit, `${to - from + 1}`);\n        return this;\n    }\n    /**\n     * Set the AbortSignal for the fetch request.\n     *\n     * @param signal - The AbortSignal to use for the fetch request\n     */\n    abortSignal(signal) {\n        this.signal = signal;\n        return this;\n    }\n    /**\n     * Return `data` as a single object instead of an array of objects.\n     *\n     * Query result must be one row (e.g. using `.limit(1)`), otherwise this\n     * returns an error.\n     */\n    single() {\n        this.headers['Accept'] = 'application/vnd.pgrst.object+json';\n        return this;\n    }\n    /**\n     * Return `data` as a single object instead of an array of objects.\n     *\n     * Query result must be zero or one row (e.g. using `.limit(1)`), otherwise\n     * this returns an error.\n     */\n    maybeSingle() {\n        // Temporary partial fix for https://github.com/supabase/postgrest-js/issues/361\n        // Issue persists e.g. for `.insert([...]).select().maybeSingle()`\n        if (this.method === 'GET') {\n            this.headers['Accept'] = 'application/json';\n        }\n        else {\n            this.headers['Accept'] = 'application/vnd.pgrst.object+json';\n        }\n        this.isMaybeSingle = true;\n        return this;\n    }\n    /**\n     * Return `data` as a string in CSV format.\n     */\n    csv() {\n        this.headers['Accept'] = 'text/csv';\n        return this;\n    }\n    /**\n     * Return `data` as an object in [GeoJSON](https://geojson.org) format.\n     */\n    geojson() {\n        this.headers['Accept'] = 'application/geo+json';\n        return this;\n    }\n    /**\n     * Return `data` as the EXPLAIN plan for the query.\n     *\n     * You need to enable the\n     * [db_plan_enabled](https://supabase.com/docs/guides/database/debugging-performance#enabling-explain)\n     * setting before using this method.\n     *\n     * @param options - Named parameters\n     *\n     * @param options.analyze - If `true`, the query will be executed and the\n     * actual run time will be returned\n     *\n     * @param options.verbose - If `true`, the query identifier will be returned\n     * and `data` will include the output columns of the query\n     *\n     * @param options.settings - If `true`, include information on configuration\n     * parameters that affect query planning\n     *\n     * @param options.buffers - If `true`, include information on buffer usage\n     *\n     * @param options.wal - If `true`, include information on WAL record generation\n     *\n     * @param options.format - The format of the output, can be `\"text\"` (default)\n     * or `\"json\"`\n     */\n    explain({ analyze = false, verbose = false, settings = false, buffers = false, wal = false, format = 'text', } = {}) {\n        var _a;\n        const options = [\n            analyze ? 'analyze' : null,\n            verbose ? 'verbose' : null,\n            settings ? 'settings' : null,\n            buffers ? 'buffers' : null,\n            wal ? 'wal' : null,\n        ]\n            .filter(Boolean)\n            .join('|');\n        // An Accept header can carry multiple media types but postgrest-js always sends one\n        const forMediatype = (_a = this.headers['Accept']) !== null && _a !== void 0 ? _a : 'application/json';\n        this.headers['Accept'] = `application/vnd.pgrst.plan+${format}; for=\"${forMediatype}\"; options=${options};`;\n        if (format === 'json')\n            return this;\n        else\n            return this;\n    }\n    /**\n     * Rollback the query.\n     *\n     * `data` will still be returned, but the query is not committed.\n     */\n    rollback() {\n        var _a;\n        if (((_a = this.headers['Prefer']) !== null && _a !== void 0 ? _a : '').trim().length > 0) {\n            this.headers['Prefer'] += ',tx=rollback';\n        }\n        else {\n            this.headers['Prefer'] = 'tx=rollback';\n        }\n        return this;\n    }\n    /**\n     * Override the type of the returned `data`.\n     *\n     * @typeParam NewResult - The new result type to override with\n     * @deprecated Use overrideTypes<yourType, { merge: false }>() method at the end of your call chain instead\n     */\n    returns() {\n        return this;\n    }\n}\nexports[\"default\"] = PostgrestTransformBuilder;\n//# sourceMappingURL=PostgrestTransformBuilder.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/constants.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/constants.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DEFAULT_HEADERS = void 0;\nconst version_1 = __webpack_require__(/*! ./version */ \"(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/version.js\");\nexports.DEFAULT_HEADERS = { 'X-Client-Info': `postgrest-js/${version_1.version}` };\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3Bvc3RncmVzdC1qc0AxLjE5LjQvbm9kZV9tb2R1bGVzL0BzdXBhYmFzZS9wb3N0Z3Jlc3QtanMvZGlzdC9janMvY29uc3RhbnRzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELHVCQUF1QjtBQUN2QixrQkFBa0IsbUJBQU8sQ0FBQyxtSUFBVztBQUNyQyx1QkFBdUIsS0FBSyxpQ0FBaUMsa0JBQWtCO0FBQy9FIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG1lbWV0XFxEZXNrdG9wXFxkb3JtXzIxXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAc3VwYWJhc2UrcG9zdGdyZXN0LWpzQDEuMTkuNFxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXHBvc3RncmVzdC1qc1xcZGlzdFxcY2pzXFxjb25zdGFudHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLkRFRkFVTFRfSEVBREVSUyA9IHZvaWQgMDtcbmNvbnN0IHZlcnNpb25fMSA9IHJlcXVpcmUoXCIuL3ZlcnNpb25cIik7XG5leHBvcnRzLkRFRkFVTFRfSEVBREVSUyA9IHsgJ1gtQ2xpZW50LUluZm8nOiBgcG9zdGdyZXN0LWpzLyR7dmVyc2lvbl8xLnZlcnNpb259YCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y29uc3RhbnRzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/index.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/index.js ***!
  \****************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PostgrestError = exports.PostgrestBuilder = exports.PostgrestTransformBuilder = exports.PostgrestFilterBuilder = exports.PostgrestQueryBuilder = exports.PostgrestClient = void 0;\n// Always update wrapper.mjs when updating this file.\nconst PostgrestClient_1 = __importDefault(__webpack_require__(/*! ./PostgrestClient */ \"(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestClient.js\"));\nexports.PostgrestClient = PostgrestClient_1.default;\nconst PostgrestQueryBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestQueryBuilder */ \"(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js\"));\nexports.PostgrestQueryBuilder = PostgrestQueryBuilder_1.default;\nconst PostgrestFilterBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestFilterBuilder */ \"(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js\"));\nexports.PostgrestFilterBuilder = PostgrestFilterBuilder_1.default;\nconst PostgrestTransformBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestTransformBuilder */ \"(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js\"));\nexports.PostgrestTransformBuilder = PostgrestTransformBuilder_1.default;\nconst PostgrestBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestBuilder */ \"(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js\"));\nexports.PostgrestBuilder = PostgrestBuilder_1.default;\nconst PostgrestError_1 = __importDefault(__webpack_require__(/*! ./PostgrestError */ \"(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js\"));\nexports.PostgrestError = PostgrestError_1.default;\nexports[\"default\"] = {\n    PostgrestClient: PostgrestClient_1.default,\n    PostgrestQueryBuilder: PostgrestQueryBuilder_1.default,\n    PostgrestFilterBuilder: PostgrestFilterBuilder_1.default,\n    PostgrestTransformBuilder: PostgrestTransformBuilder_1.default,\n    PostgrestBuilder: PostgrestBuilder_1.default,\n    PostgrestError: PostgrestError_1.default,\n};\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/version.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/version.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.version = void 0;\nexports.version = '0.0.0-automated';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3Bvc3RncmVzdC1qc0AxLjE5LjQvbm9kZV9tb2R1bGVzL0BzdXBhYmFzZS9wb3N0Z3Jlc3QtanMvZGlzdC9janMvdmVyc2lvbi5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxlQUFlO0FBQ2YsZUFBZTtBQUNmIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG1lbWV0XFxEZXNrdG9wXFxkb3JtXzIxXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAc3VwYWJhc2UrcG9zdGdyZXN0LWpzQDEuMTkuNFxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXHBvc3RncmVzdC1qc1xcZGlzdFxcY2pzXFx2ZXJzaW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy52ZXJzaW9uID0gdm9pZCAwO1xuZXhwb3J0cy52ZXJzaW9uID0gJzAuMC4wLWF1dG9tYXRlZCc7XG4vLyMgc291cmNlTWFwcGluZ1VSTD12ZXJzaW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/version.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PostgrestBuilder: () => (/* binding */ PostgrestBuilder),\n/* harmony export */   PostgrestClient: () => (/* binding */ PostgrestClient),\n/* harmony export */   PostgrestError: () => (/* binding */ PostgrestError),\n/* harmony export */   PostgrestFilterBuilder: () => (/* binding */ PostgrestFilterBuilder),\n/* harmony export */   PostgrestQueryBuilder: () => (/* binding */ PostgrestQueryBuilder),\n/* harmony export */   PostgrestTransformBuilder: () => (/* binding */ PostgrestTransformBuilder),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _cjs_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../cjs/index.js */ \"(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/index.js\");\n\nconst {\n  PostgrestClient,\n  PostgrestQueryBuilder,\n  PostgrestFilterBuilder,\n  PostgrestTransformBuilder,\n  PostgrestBuilder,\n  PostgrestError,\n} = _cjs_index_js__WEBPACK_IMPORTED_MODULE_0__\n\n\n\n// compatibility with CJS output\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n  PostgrestClient,\n  PostgrestQueryBuilder,\n  PostgrestFilterBuilder,\n  PostgrestTransformBuilder,\n  PostgrestBuilder,\n  PostgrestError,\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3Bvc3RncmVzdC1qc0AxLjE5LjQvbm9kZV9tb2R1bGVzL0BzdXBhYmFzZS9wb3N0Z3Jlc3QtanMvZGlzdC9lc20vd3JhcHBlci5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBbUM7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxFQUFFLEVBQUUsMENBQUs7O0FBU1I7O0FBRUQ7QUFDQSxpRUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbWVtZXRcXERlc2t0b3BcXGRvcm1fMjFcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZStwb3N0Z3Jlc3QtanNAMS4xOS40XFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxccG9zdGdyZXN0LWpzXFxkaXN0XFxlc21cXHdyYXBwZXIubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBpbmRleCBmcm9tICcuLi9janMvaW5kZXguanMnXG5jb25zdCB7XG4gIFBvc3RncmVzdENsaWVudCxcbiAgUG9zdGdyZXN0UXVlcnlCdWlsZGVyLFxuICBQb3N0Z3Jlc3RGaWx0ZXJCdWlsZGVyLFxuICBQb3N0Z3Jlc3RUcmFuc2Zvcm1CdWlsZGVyLFxuICBQb3N0Z3Jlc3RCdWlsZGVyLFxuICBQb3N0Z3Jlc3RFcnJvcixcbn0gPSBpbmRleFxuXG5leHBvcnQge1xuICBQb3N0Z3Jlc3RCdWlsZGVyLFxuICBQb3N0Z3Jlc3RDbGllbnQsXG4gIFBvc3RncmVzdEZpbHRlckJ1aWxkZXIsXG4gIFBvc3RncmVzdFF1ZXJ5QnVpbGRlcixcbiAgUG9zdGdyZXN0VHJhbnNmb3JtQnVpbGRlcixcbiAgUG9zdGdyZXN0RXJyb3IsXG59XG5cbi8vIGNvbXBhdGliaWxpdHkgd2l0aCBDSlMgb3V0cHV0XG5leHBvcnQgZGVmYXVsdCB7XG4gIFBvc3RncmVzdENsaWVudCxcbiAgUG9zdGdyZXN0UXVlcnlCdWlsZGVyLFxuICBQb3N0Z3Jlc3RGaWx0ZXJCdWlsZGVyLFxuICBQb3N0Z3Jlc3RUcmFuc2Zvcm1CdWlsZGVyLFxuICBQb3N0Z3Jlc3RCdWlsZGVyLFxuICBQb3N0Z3Jlc3RFcnJvcixcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs\n");

/***/ })

};
;