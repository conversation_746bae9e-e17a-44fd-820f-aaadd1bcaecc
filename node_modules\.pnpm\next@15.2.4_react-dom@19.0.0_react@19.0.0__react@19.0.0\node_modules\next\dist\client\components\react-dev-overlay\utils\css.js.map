{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/utils/css.ts"], "sourcesContent": ["export function css(\n  strings: TemplateStringsArray,\n  ...keys: readonly string[]\n): string {\n  const lastIndex = strings.length - 1\n  const str =\n    // Convert template literal into a single line string\n    strings.slice(0, lastIndex).reduce((p, s, i) => p + s + keys[i], '') +\n    strings[lastIndex]\n\n  return (\n    str\n      // Remove comments\n      .replace(/\\/\\*[\\s\\S]*?\\*\\//g, '')\n      // Remove whitespace, tabs, and newlines\n      .replace(/\\s+/g, ' ')\n      // Remove spaces before and after semicolons, and spaces after commas\n      .replace(/\\s*([:;,{}])\\s*/g, '$1')\n      // Remove extra semicolons\n      .replace(/;+}/g, '}')\n      // Trim leading and trailing whitespaces\n      .trim()\n  )\n}\n"], "names": ["css", "strings", "keys", "lastIndex", "length", "str", "slice", "reduce", "p", "s", "i", "replace", "trim"], "mappings": ";;;;+BAAgBA;;;eAAAA;;;AAAT,SAASA,IACdC,OAA6B;IAC7B,IAAA,IAAA,OAAA,UAAA,QAAA,AAAGC,OAAH,UAAA,OAAA,IAAA,OAAA,QAAA,OAAA,GAAA,OAAA,MAAA;QAAGA,KAAH,OAAA,KAAA,SAAA,CAAA,KAA0B;;IAE1B,MAAMC,YAAYF,QAAQG,MAAM,GAAG;IACnC,MAAMC,MACJ,qDAAqD;IACrDJ,QAAQK,KAAK,CAAC,GAAGH,WAAWI,MAAM,CAAC,CAACC,GAAGC,GAAGC,IAAMF,IAAIC,IAAIP,IAAI,CAACQ,EAAE,EAAE,MACjET,OAAO,CAACE,UAAU;IAEpB,OACEE,GACE,kBAAkB;KACjBM,OAAO,CAAC,qBAAqB,GAC9B,wCAAwC;KACvCA,OAAO,CAAC,QAAQ,IACjB,qEAAqE;KACpEA,OAAO,CAAC,oBAAoB,KAC7B,0BAA0B;KACzBA,OAAO,CAAC,QAAQ,IACjB,wCAAwC;KACvCC,IAAI;AAEX"}