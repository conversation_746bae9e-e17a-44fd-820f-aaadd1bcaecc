{"version": 3, "sources": ["../../src/client/page-bootstrap.ts"], "sourcesContent": ["import { hydrate, router } from './'\nimport initOnDemandEntries from './dev/on-demand-entries-client'\nimport { devBuildIndicator } from './dev/dev-build-indicator/internal/dev-build-indicator'\nimport { displayContent } from './dev/fouc'\nimport {\n  connectHMR,\n  addMessageListener,\n} from './components/react-dev-overlay/pages/websocket'\nimport {\n  assign,\n  urlQueryToSearchParams,\n} from '../shared/lib/router/utils/querystring'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from '../server/dev/hot-reloader-types'\nimport { RuntimeErrorHandler } from './components/errors/runtime-error-handler'\nimport { REACT_REFRESH_FULL_RELOAD_FROM_ERROR } from './components/react-dev-overlay/shared'\nimport { performFullReload } from './components/react-dev-overlay/pages/hot-reloader-client'\nimport { initializeDevBuildIndicatorForPageRouter } from './dev/dev-build-indicator/initialize-for-page-router'\n\nexport function pageBootstrap(assetPrefix: string) {\n  connectHMR({ assetPrefix, path: '/_next/webpack-hmr' })\n\n  return hydrate({ beforeRender: displayContent }).then(() => {\n    initOnDemandEntries()\n\n    initializeDevBuildIndicatorForPageRouter()\n\n    let reloading = false\n\n    addMessageListener((payload) => {\n      if (reloading) return\n      if ('action' in payload) {\n        switch (payload.action) {\n          case HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ERROR: {\n            const { stack, message } = JSON.parse(payload.errorJSON)\n            const error = new Error(message)\n            error.stack = stack\n            throw error\n          }\n          case HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE: {\n            reloading = true\n            window.location.reload()\n            break\n          }\n          case HMR_ACTIONS_SENT_TO_BROWSER.DEV_PAGES_MANIFEST_UPDATE: {\n            fetch(\n              `${assetPrefix}/_next/static/development/_devPagesManifest.json`\n            )\n              .then((res) => res.json())\n              .then((manifest) => {\n                window.__DEV_PAGES_MANIFEST = manifest\n              })\n              .catch((err) => {\n                console.log(`Failed to fetch devPagesManifest`, err)\n              })\n            break\n          }\n          default:\n            break\n        }\n      } else if ('event' in payload) {\n        switch (payload.event) {\n          case HMR_ACTIONS_SENT_TO_BROWSER.MIDDLEWARE_CHANGES: {\n            return window.location.reload()\n          }\n          case HMR_ACTIONS_SENT_TO_BROWSER.CLIENT_CHANGES: {\n            // This is used in `../server/dev/turbopack-utils.ts`.\n            const isOnErrorPage = window.next.router.pathname === '/_error'\n            // On the error page we want to reload the page when a page was changed\n            if (isOnErrorPage) {\n              if (RuntimeErrorHandler.hadRuntimeError) {\n                console.warn(REACT_REFRESH_FULL_RELOAD_FROM_ERROR)\n              }\n              reloading = true\n              performFullReload(null)\n            }\n            break\n          }\n          case HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ONLY_CHANGES: {\n            if (RuntimeErrorHandler.hadRuntimeError) {\n              console.warn(REACT_REFRESH_FULL_RELOAD_FROM_ERROR)\n              performFullReload(null)\n            }\n\n            const { pages } = payload\n\n            // Make sure to reload when the dev-overlay is showing for an\n            // API route\n            // TODO: Fix `__NEXT_PAGE` type\n            if (pages.includes(router.query.__NEXT_PAGE as string)) {\n              return window.location.reload()\n            }\n\n            if (!router.clc && pages.includes(router.pathname)) {\n              console.log('Refreshing page data due to server-side change')\n              devBuildIndicator.show()\n              const clearIndicator = () => devBuildIndicator.hide()\n\n              router\n                .replace(\n                  router.pathname +\n                    '?' +\n                    String(\n                      assign(\n                        urlQueryToSearchParams(router.query),\n                        new URLSearchParams(location.search)\n                      )\n                    ),\n                  router.asPath,\n                  { scroll: false }\n                )\n                .catch(() => {\n                  // trigger hard reload when failing to refresh data\n                  // to show error overlay properly\n                  location.reload()\n                })\n                .finally(clearIndicator)\n            }\n            break\n          }\n          default:\n            break\n        }\n      }\n    })\n  })\n}\n"], "names": ["pageBootstrap", "assetPrefix", "connectHMR", "path", "hydrate", "beforeRender", "displayContent", "then", "initOnDemandEntries", "initializeDevBuildIndicatorForPageRouter", "reloading", "addMessageListener", "payload", "action", "HMR_ACTIONS_SENT_TO_BROWSER", "SERVER_ERROR", "stack", "message", "JSON", "parse", "errorJSON", "error", "Error", "RELOAD_PAGE", "window", "location", "reload", "DEV_PAGES_MANIFEST_UPDATE", "fetch", "res", "json", "manifest", "__DEV_PAGES_MANIFEST", "catch", "err", "console", "log", "event", "MIDDLEWARE_CHANGES", "CLIENT_CHANGES", "isOnErrorPage", "next", "router", "pathname", "RuntimeError<PERSON>andler", "hadRuntimeError", "warn", "REACT_REFRESH_FULL_RELOAD_FROM_ERROR", "performFullReload", "SERVER_ONLY_CHANGES", "pages", "includes", "query", "__NEXT_PAGE", "clc", "devBuildIndicator", "show", "clearIndicator", "hide", "replace", "String", "assign", "urlQueryToSearchParams", "URLSearchParams", "search", "<PERSON><PERSON><PERSON>", "scroll", "finally"], "mappings": ";;;;+BAkBgBA;;;eAAAA;;;;kBAlBgB;gFACA;mCACE;sBACH;2BAIxB;6BAIA;kCACqC;qCACR;wBACiB;mCACnB;yCACuB;AAElD,SAASA,cAAcC,WAAmB;IAC/CC,IAAAA,qBAAU,EAAC;QAAED;QAAaE,MAAM;IAAqB;IAErD,OAAOC,IAAAA,SAAO,EAAC;QAAEC,cAAcC,oBAAc;IAAC,GAAGC,IAAI,CAAC;QACpDC,IAAAA,8BAAmB;QAEnBC,IAAAA,iEAAwC;QAExC,IAAIC,YAAY;QAEhBC,IAAAA,6BAAkB,EAAC,CAACC;YAClB,IAAIF,WAAW;YACf,IAAI,YAAYE,SAAS;gBACvB,OAAQA,QAAQC,MAAM;oBACpB,KAAKC,6CAA2B,CAACC,YAAY;wBAAE;4BAC7C,MAAM,EAAEC,KAAK,EAAEC,OAAO,EAAE,GAAGC,KAAKC,KAAK,CAACP,QAAQQ,SAAS;4BACvD,MAAMC,QAAQ,qBAAkB,CAAlB,IAAIC,MAAML,UAAV,qBAAA;uCAAA;4CAAA;8CAAA;4BAAiB;4BAC/BI,MAAML,KAAK,GAAGA;4BACd,MAAMK;wBACR;oBACA,KAAKP,6CAA2B,CAACS,WAAW;wBAAE;4BAC5Cb,YAAY;4BACZc,OAAOC,QAAQ,CAACC,MAAM;4BACtB;wBACF;oBACA,KAAKZ,6CAA2B,CAACa,yBAAyB;wBAAE;4BAC1DC,MACE,AAAC,KAAE3B,cAAY,oDAEdM,IAAI,CAAC,CAACsB,MAAQA,IAAIC,IAAI,IACtBvB,IAAI,CAAC,CAACwB;gCACLP,OAAOQ,oBAAoB,GAAGD;4BAChC,GACCE,KAAK,CAAC,CAACC;gCACNC,QAAQC,GAAG,CAAE,oCAAmCF;4BAClD;4BACF;wBACF;oBACA;wBACE;gBACJ;YACF,OAAO,IAAI,WAAWtB,SAAS;gBAC7B,OAAQA,QAAQyB,KAAK;oBACnB,KAAKvB,6CAA2B,CAACwB,kBAAkB;wBAAE;4BACnD,OAAOd,OAAOC,QAAQ,CAACC,MAAM;wBAC/B;oBACA,KAAKZ,6CAA2B,CAACyB,cAAc;wBAAE;4BAC/C,sDAAsD;4BACtD,MAAMC,gBAAgBhB,OAAOiB,IAAI,CAACC,MAAM,CAACC,QAAQ,KAAK;4BACtD,uEAAuE;4BACvE,IAAIH,eAAe;gCACjB,IAAII,wCAAmB,CAACC,eAAe,EAAE;oCACvCV,QAAQW,IAAI,CAACC,4CAAoC;gCACnD;gCACArC,YAAY;gCACZsC,IAAAA,oCAAiB,EAAC;4BACpB;4BACA;wBACF;oBACA,KAAKlC,6CAA2B,CAACmC,mBAAmB;wBAAE;4BACpD,IAAIL,wCAAmB,CAACC,eAAe,EAAE;gCACvCV,QAAQW,IAAI,CAACC,4CAAoC;gCACjDC,IAAAA,oCAAiB,EAAC;4BACpB;4BAEA,MAAM,EAAEE,KAAK,EAAE,GAAGtC;4BAElB,6DAA6D;4BAC7D,YAAY;4BACZ,+BAA+B;4BAC/B,IAAIsC,MAAMC,QAAQ,CAACT,QAAM,CAACU,KAAK,CAACC,WAAW,GAAa;gCACtD,OAAO7B,OAAOC,QAAQ,CAACC,MAAM;4BAC/B;4BAEA,IAAI,CAACgB,QAAM,CAACY,GAAG,IAAIJ,MAAMC,QAAQ,CAACT,QAAM,CAACC,QAAQ,GAAG;gCAClDR,QAAQC,GAAG,CAAC;gCACZmB,oCAAiB,CAACC,IAAI;gCACtB,MAAMC,iBAAiB,IAAMF,oCAAiB,CAACG,IAAI;gCAEnDhB,QAAM,CACHiB,OAAO,CACNjB,QAAM,CAACC,QAAQ,GACb,MACAiB,OACEC,IAAAA,mBAAM,EACJC,IAAAA,mCAAsB,EAACpB,QAAM,CAACU,KAAK,GACnC,IAAIW,gBAAgBtC,SAASuC,MAAM,KAGzCtB,QAAM,CAACuB,MAAM,EACb;oCAAEC,QAAQ;gCAAM,GAEjBjC,KAAK,CAAC;oCACL,mDAAmD;oCACnD,iCAAiC;oCACjCR,SAASC,MAAM;gCACjB,GACCyC,OAAO,CAACV;4BACb;4BACA;wBACF;oBACA;wBACE;gBACJ;YACF;QACF;IACF;AACF"}