{"version": 3, "sources": ["../../src/client/app-next-turbopack.ts"], "sourcesContent": ["// TODO-APP: hydration warning\n\nimport { appBootstrap } from './app-bootstrap'\n\nwindow.next.version += '-turbo'\n;(self as any).__webpack_hash__ = ''\n\nappBootstrap(() => {\n  const { hydrate } = require('./app-index')\n  hydrate()\n\n  if (process.env.NODE_ENV !== 'production') {\n    const { initializeDevBuildIndicatorForAppRouter } =\n      require('./dev/dev-build-indicator/initialize-for-app-router') as typeof import('./dev/dev-build-indicator/initialize-for-app-router')\n    initializeDevBuildIndicatorForAppRouter()\n  }\n})\n"], "names": ["window", "next", "version", "self", "__webpack_hash__", "appBootstrap", "hydrate", "require", "process", "env", "NODE_ENV", "initializeDevBuildIndicatorForAppRouter"], "mappings": "AAAA,8BAA8B;;;;;8BAED;AAE7BA,OAAOC,IAAI,CAACC,OAAO,IAAI;AACrBC,KAAaC,gBAAgB,GAAG;AAElCC,IAAAA,0BAAY,EAAC;IACX,MAAM,EAAEC,OAAO,EAAE,GAAGC,QAAQ;IAC5BD;IAEA,IAAIE,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,MAAM,EAAEC,uCAAuC,EAAE,GAC/CJ,QAAQ;QACVI;IACF;AACF"}