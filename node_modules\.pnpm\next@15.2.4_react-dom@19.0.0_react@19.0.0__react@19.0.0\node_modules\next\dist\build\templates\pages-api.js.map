{"version": 3, "sources": ["../../../src/build/templates/pages-api.ts"], "sourcesContent": ["import { PagesAPIRouteModule } from '../../server/route-modules/pages-api/module.compiled'\nimport { RouteKind } from '../../server/route-kind'\n\nimport { hoist } from './helpers'\n\n// Import the userland code.\nimport * as userland from 'VAR_USERLAND'\n\n// Re-export the handler (should be the default export).\nexport default hoist(userland, 'default')\n\n// Re-export config.\nexport const config = hoist(userland, 'config')\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new PagesAPIRouteModule({\n  definition: {\n    kind: RouteKind.PAGES_API,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n  },\n  userland,\n})\n"], "names": ["config", "routeModule", "hoist", "userland", "PagesAPIRouteModule", "definition", "kind", "RouteKind", "PAGES_API", "page", "pathname", "bundlePath", "filename"], "mappings": ";;;;;;;;;;;;;;;;IAYaA,MAAM;eAANA;;IAJb,wDAAwD;IACxD,OAAyC;eAAzC;;IAMaC,WAAW;eAAXA;;;gCAfuB;2BACV;yBAEJ;sEAGI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAG1B,WAAeC,IAAAA,cAAK,EAACC,eAAU;AAGxB,MAAMH,SAASE,IAAAA,cAAK,EAACC,eAAU;AAG/B,MAAMF,cAAc,IAAIG,mCAAmB,CAAC;IACjDC,YAAY;QACVC,MAAMC,oBAAS,CAACC,SAAS;QACzBC,MAAM;QACNC,UAAU;QACV,2CAA2C;QAC3CC,YAAY;QACZC,UAAU;IACZ;IACAT,UAAAA;AACF"}