"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/checkin/page",{

/***/ "(app-pages-browser)/./app/checkin/page.tsx":
/*!******************************!*\
  !*** ./app/checkin/page.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CheckInPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var qrcode_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! qrcode.react */ \"(app-pages-browser)/./node_modules/.pnpm/qrcode.react@4.2.0_react@19.0.0/node_modules/qrcode.react/lib/esm/index.js\");\n/* harmony import */ var _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/src/hooks/useSupabaseData */ \"(app-pages-browser)/./src/hooks/useSupabaseData.tsx\");\n/* harmony import */ var _src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/src/hooks/useCountdown */ \"(app-pages-browser)/./src/hooks/useCountdown.tsx\");\n/* harmony import */ var _src_utils_machineOwnership__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/src/utils/machineOwnership */ \"(app-pages-browser)/./src/utils/machineOwnership.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction CheckInPage() {\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const machineId = searchParams.get(\"machine\");\n    const { laundry, toggleMachineStatus, reserveMachine, adjustMachineTime, isLoading, refreshData } = (0,_src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    const [machine, setMachine] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [actionResult, setActionResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [customTime, setCustomTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"60\");\n    const [adjustTime, setAdjustTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isAdjusting, setIsAdjusting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [adjustResult, setAdjustResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const countdown = (0,_src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(machine === null || machine === void 0 ? void 0 : machine.endAt, machine === null || machine === void 0 ? void 0 : machine.graceEndAt);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CheckInPage.useEffect\": ()=>{\n            if (!machineId) return;\n            const foundMachine = laundry.find({\n                \"CheckInPage.useEffect.foundMachine\": (m)=>m.id === machineId\n            }[\"CheckInPage.useEffect.foundMachine\"]);\n            setMachine(foundMachine || null);\n        }\n    }[\"CheckInPage.useEffect\"], [\n        machineId,\n        laundry\n    ]);\n    // Reduced auto-refresh to every 10 seconds to prevent conflicts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CheckInPage.useEffect\": ()=>{\n            const interval = setInterval({\n                \"CheckInPage.useEffect.interval\": ()=>{\n                    refreshData();\n                }\n            }[\"CheckInPage.useEffect.interval\"], 10000);\n            return ({\n                \"CheckInPage.useEffect\": ()=>clearInterval(interval)\n            })[\"CheckInPage.useEffect\"];\n        }\n    }[\"CheckInPage.useEffect\"], [\n        refreshData\n    ]);\n    const handleToggle = async ()=>{\n        if (!machineId || isProcessing) return;\n        // Validate time input for free machines\n        if ((machine === null || machine === void 0 ? void 0 : machine.status) === \"free\") {\n            const timeNum = parseInt(customTime);\n            if (isNaN(timeNum) || timeNum < 15 || timeNum > 180) {\n                setActionResult(\"Please enter a valid time limit\");\n                return;\n            }\n        }\n        setIsProcessing(true);\n        setActionResult(\"\");\n        let success = false;\n        if ((machine === null || machine === void 0 ? void 0 : machine.status) === \"free\") {\n            // Always use custom time for starting machines\n            success = await reserveMachine(machineId, \"\".concat(customTime, \" minutes\"));\n        } else {\n            // Use default toggle for stopping/collecting\n            success = await toggleMachineStatus(machineId);\n        }\n        if (success) {\n            setActionResult(\"Status updated successfully!\");\n            // Single refresh after 2 seconds to confirm the change\n            setTimeout(()=>refreshData(), 2000);\n        } else {\n            setActionResult(\"Error updating machine status\");\n        }\n        setIsProcessing(false);\n    };\n    const getStatusDisplay = ()=>{\n        if (!machine) return \"\";\n        switch(machine.status){\n            case \"free\":\n                return \"🟢 Available\";\n            case \"running\":\n                const hours = Math.floor(countdown.secondsLeft / 3600);\n                const minutes = Math.floor(countdown.secondsLeft % 3600 / 60);\n                const seconds = countdown.secondsLeft % 60;\n                return \"\\uD83D\\uDD35 In Use - \".concat(hours, \":\").concat(minutes.toString().padStart(2, \"0\"), \":\").concat(seconds.toString().padStart(2, \"0\"), \" left\");\n            case \"finishedGrace\":\n                // Calculate grace period time remaining\n                let graceTimeDisplay = \"5:00\";\n                if (machine.graceEndAt) {\n                    const graceMinutes = Math.floor(countdown.graceSecondsLeft / 60);\n                    const graceSeconds = countdown.graceSecondsLeft % 60;\n                    graceTimeDisplay = \"\".concat(graceMinutes, \":\").concat(graceSeconds.toString().padStart(2, \"0\"));\n                }\n                return \"⚠️ Please collect items - \".concat(graceTimeDisplay, \" left\");\n            default:\n                return \"Unknown\";\n        }\n    };\n    const getButtonText = ()=>{\n        if (isProcessing) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, this),\n                    \"Processing...\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 108,\n                columnNumber: 9\n            }, this);\n        }\n        const currentUserId = getDeviceUserId();\n        const isOwner = (machine === null || machine === void 0 ? void 0 : machine.startedByUserId) === currentUserId;\n        switch(machine === null || machine === void 0 ? void 0 : machine.status){\n            case \"free\":\n                return \"▶️ Start Using Machine (\".concat(customTime, \" min)\");\n            case \"running\":\n                return isOwner ? \"⏹️ Stop Using Machine\" : \"🚫 Machine in Use by Another User\";\n            case \"finishedGrace\":\n                return isOwner ? \"✅ I've Collected My Items\" : \"🚫 Another User's Items\";\n            default:\n                return \"Update Status\";\n        }\n    };\n    const getButtonColor = ()=>{\n        if (isProcessing) return \"bg-gray-400 cursor-not-allowed\";\n        const currentUserId = getDeviceUserId();\n        const isOwner = (machine === null || machine === void 0 ? void 0 : machine.startedByUserId) === currentUserId;\n        switch(machine === null || machine === void 0 ? void 0 : machine.status){\n            case \"free\":\n                return \"bg-blue-500 hover:bg-blue-600\";\n            case \"running\":\n                return isOwner ? \"bg-red-500 hover:bg-red-600\" : \"bg-gray-400 cursor-not-allowed\";\n            case \"finishedGrace\":\n                return isOwner ? \"bg-green-500 hover:bg-green-600\" : \"bg-gray-400 cursor-not-allowed\";\n            default:\n                return \"bg-gray-500\";\n        }\n    };\n    const isButtonDisabled = ()=>{\n        if (isProcessing) return true;\n        const currentUserId = getDeviceUserId();\n        const isOwner = (machine === null || machine === void 0 ? void 0 : machine.startedByUserId) === currentUserId;\n        // Disable if machine is busy and user is not the owner\n        if (((machine === null || machine === void 0 ? void 0 : machine.status) === \"running\" || (machine === null || machine === void 0 ? void 0 : machine.status) === \"finishedGrace\") && !isOwner) {\n            return true;\n        }\n        return false;\n    };\n    const handleBackToHome = ()=>{\n        router.push(\"/\");\n    };\n    const handleAdjustTime = async ()=>{\n        if (!machineId || isAdjusting) return;\n        const minutesNum = parseInt(adjustTime);\n        if (isNaN(minutesNum) || minutesNum < 1 || minutesNum > 120) {\n            setAdjustResult(\"Please enter a number between 1-120 minutes\");\n            return;\n        }\n        setIsAdjusting(true);\n        setAdjustResult(\"\");\n        const result = await adjustMachineTime(machineId, minutesNum);\n        if (result.success) {\n            setAdjustResult(\"Timer updated successfully\");\n            setAdjustTime(\"\");\n            // Refresh data after successful adjustment\n            setTimeout(()=>refreshData(), 1000);\n        } else {\n            setAdjustResult(result.error || \"Failed to update timer\");\n        }\n        setIsAdjusting(false);\n    };\n    // Custom display names for machines (same logic as LaundryCard)\n    const getDisplayName = (machine)=>{\n        if (!machine) return \"\";\n        const isWasher = machine.name.toLowerCase().includes(\"washer\");\n        if (isWasher) {\n            // Extract number from washer name (e.g., \"Washer 1\" -> \"1\")\n            const numberMatch = machine.name.match(/\\d+/);\n            const number = numberMatch ? numberMatch[0] : \"\";\n            return \"Washer \".concat(number);\n        } else {\n            // For dryers, use \"Dryer 5\" through \"Dryer 8\" based on original number\n            const numberMatch = machine.name.match(/\\d+/);\n            const originalNumber = numberMatch ? Number.parseInt(numberMatch[0]) : 0;\n            const newNumber = originalNumber + 4 // Map 1->5, 2->6, 3->7, 4->8\n            ;\n            return \"Dryer \".concat(newNumber);\n        }\n    };\n    if (isLoading && !machine) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-8 rounded-lg shadow text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-lg\",\n                        children: \"Getting machine information...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 215,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 214,\n            columnNumber: 7\n        }, this);\n    }\n    if (!machineId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-8 rounded-lg shadow text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-600 text-lg mb-4\",\n                        children: \"Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600\",\n                        children: \"No machine ID provided.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 226,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 225,\n            columnNumber: 7\n        }, this);\n    }\n    if (!machine) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-8 rounded-lg shadow text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-600 text-lg mb-4\",\n                        children: \"Machine Not Found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"The requested machine could not be found.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: refreshData,\n                        className: \"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded\",\n                        children: \"Retry Loading\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 237,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 236,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white p-8 rounded-lg shadow text-center max-w-md w-full mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-4\",\n                    children: \"Machine Check-In\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-600 mb-3\",\n                            children: \"Scan to access this page\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white p-3 rounded-lg border-2 border-gray-200 shadow-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(qrcode_react__WEBPACK_IMPORTED_MODULE_3__.QRCodeSVG, {\n                                    value: \"\".concat( true ? window.location.origin : 0, \"/checkin?machine=\").concat(machineId),\n                                    size: 80,\n                                    fgColor: \"#1A1F36\",\n                                    bgColor: \"#FFFFFF\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-lg font-medium\",\n                            children: getDisplayName(machine)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm mt-1 font-semibold \".concat(machine.status === \"free\" ? \"text-green-600\" : machine.status === \"running\" ? \"text-blue-600\" : machine.status === \"finishedGrace\" ? \"text-orange-600\" : \"text-red-600\", \" \").concat(machine.status === \"finishedGrace\" ? \"animate-pulse\" : \"\"),\n                            children: [\n                                \"Status: \",\n                                getStatusDisplay()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, this),\n                        machine.status === \"running\" && machine.endAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500 mt-1\",\n                            children: [\n                                \"Ends at: \",\n                                machine.endAt.toLocaleTimeString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 13\n                        }, this),\n                        machine.status === \"finishedGrace\" && machine.graceEndAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-orange-500 mt-1\",\n                            children: [\n                                \"Grace period ends at: \",\n                                machine.graceEndAt.toLocaleTimeString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 13\n                        }, this),\n                        machine.startedByUserId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-600 mt-2\",\n                            children: (0,_src_utils_machineOwnership__WEBPACK_IMPORTED_MODULE_6__.getOwnershipDisplay)(machine)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 268,\n                    columnNumber: 9\n                }, this),\n                machine.status === \"free\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium text-blue-800 mb-2\",\n                            children: \"Set Time Limit\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"number\",\n                                    value: customTime,\n                                    onChange: (e)=>setCustomTime(e.target.value),\n                                    placeholder: \"Enter minutes\",\n                                    className: \"border border-gray-300 rounded-md p-3 w-24 text-center text-lg font-medium focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-600 font-medium\",\n                                    children: \"minutes\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-blue-600 mt-2 text-center\",\n                            children: \"Add a time limit accordingly\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 11\n                }, this),\n                machine.status === \"running\" && (0,_src_utils_machineOwnership__WEBPACK_IMPORTED_MODULE_6__.isCurrentUserOwner)(machine) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: (0,_src_utils_machineOwnership__WEBPACK_IMPORTED_MODULE_6__.canAdjustTime)(machine) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm font-medium text-blue-800 mb-2\",\n                                children: \"Adjust Timer\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-blue-600 mb-3\",\n                                children: [\n                                    \"Current: \",\n                                    machine.endAt ? Math.max(0, Math.ceil((machine.endAt.getTime() - Date.now()) / (1000 * 60))) : 0,\n                                    \" minutes remaining\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"number\",\n                                        value: adjustTime,\n                                        onChange: (e)=>setAdjustTime(e.target.value),\n                                        placeholder: \"Minutes (1-120)\",\n                                        min: \"1\",\n                                        max: \"120\",\n                                        disabled: isAdjusting,\n                                        className: \"flex-1 border rounded p-2 text-center text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleAdjustTime,\n                                        disabled: isAdjusting || !adjustTime,\n                                        className: \"bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1\",\n                                        children: [\n                                            isAdjusting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 border border-white border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 23\n                                            }, this),\n                                            isAdjusting ? \"Updating...\" : \"Update Timer\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 17\n                            }, this),\n                            adjustResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs p-2 rounded \".concat(adjustResult.includes(\"successfully\") ? \"bg-green-100 text-green-800 border border-green-200\" : \"bg-red-100 text-red-800 border border-red-200\"),\n                                children: adjustResult\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 19\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 15\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 p-4 bg-gray-50 border border-gray-200 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm font-medium text-gray-600 mb-2\",\n                                children: \"Timer Adjustment\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-500\",\n                                children: [\n                                    \"Available in \",\n                                    (0,_src_utils_machineOwnership__WEBPACK_IMPORTED_MODULE_6__.getTimeUntilAdjustmentAvailable)(machine),\n                                    \" minutes\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleToggle,\n                    disabled: isProcessing,\n                    className: \"w-full p-3 rounded text-white font-medium mb-4 \".concat(getButtonColor()),\n                    children: getButtonText()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 376,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleBackToHome,\n                    className: \"w-full p-3 rounded-lg bg-gray-800 hover:bg-gray-900 text-white font-medium mb-4 transition-colors duration-200 shadow-md\",\n                    children: \"← Back to Home\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 385,\n                    columnNumber: 9\n                }, this),\n                actionResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm mb-4 p-2 rounded \".concat(actionResult.includes(\"Error\") ? \"bg-red-100 text-red-700\" : \"bg-green-100 text-green-700\"),\n                    children: actionResult\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 393,\n                    columnNumber: 11\n                }, this),\n                machine.status === \"finishedGrace\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-orange-50 border border-orange-200 rounded p-3 text-sm text-orange-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium mb-1\",\n                            children: \"⚠️ Grace Period Active\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Please collect your items within the time limit. The machine will become available automatically when the grace period ends.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 405,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 403,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 250,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n        lineNumber: 249,\n        columnNumber: 5\n    }, this);\n}\n_s(CheckInPage, \"v+h7YXyy6gXcj/Rvi3VDQhkCD98=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        _src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n});\n_c = CheckInPage;\nvar _c;\n$RefreshReg$(_c, \"CheckInPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/checkin/page.tsx\n"));

/***/ })

});