"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/AnnouncementsCard.tsx":
/*!**********************************************!*\
  !*** ./src/components/AnnouncementsCard.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnnouncementsCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/src/hooks/useSupabaseData */ \"(app-pages-browser)/./src/hooks/useSupabaseData.tsx\");\n/* harmony import */ var _src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/utils/userIdentification */ \"(app-pages-browser)/./src/utils/userIdentification.ts\");\n/* harmony import */ var _ui_CardWrapper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/CardWrapper */ \"(app-pages-browser)/./src/components/ui/CardWrapper.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst ANNOUNCEMENT_TYPES = [\n    {\n        value: \"sublet\",\n        label: \"🏠 Sublet\",\n        color: \"bg-blue-100 text-blue-800\"\n    },\n    {\n        value: \"selling\",\n        label: \"💰 Selling\",\n        color: \"bg-green-100 text-green-800\"\n    },\n    {\n        value: \"free\",\n        label: \"🎁 Free Stuff\",\n        color: \"bg-purple-100 text-purple-800\"\n    },\n    {\n        value: \"wanted\",\n        label: \"🔍 Looking For\",\n        color: \"bg-orange-100 text-orange-800\"\n    },\n    {\n        value: \"event\",\n        label: \"🎉 Event\",\n        color: \"bg-pink-100 text-pink-800\"\n    },\n    {\n        value: \"general\",\n        label: \"📢 General\",\n        color: \"bg-gray-100 text-gray-800\"\n    }\n];\nfunction AnnouncementsCard() {\n    _s();\n    const { announcements, addAnnouncement, deleteAnnouncement } = (0,_src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [description, setDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [type, setType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"general\");\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showForm, setShowForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnnouncementsCard.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"AnnouncementsCard.useEffect\"], []);\n    const handlePostAnnouncement = async ()=>{\n        if (!title.trim() || !description.trim() || isSubmitting || !isClient) return;\n        setIsSubmitting(true);\n        const user = (0,_src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_3__.getUserDisplayName)();\n        const success = await addAnnouncement(user, title.trim(), description.trim(), type);\n        if (success) {\n            setTitle(\"\");\n            setDescription(\"\");\n            setType(\"general\");\n            setShowForm(false);\n        }\n        setIsSubmitting(false);\n    };\n    const handleCancel = ()=>{\n        setShowForm(false);\n        setTitle(\"\");\n        setDescription(\"\");\n        setType(\"general\");\n    };\n    const handleDelete = async (id)=>{\n        if (confirm(\"Are you sure you want to delete this announcement?\")) {\n            await deleteAnnouncement(id);\n        }\n    };\n    const getTypeInfo = (typeValue)=>{\n        return ANNOUNCEMENT_TYPES.find((t)=>t.value === typeValue) || ANNOUNCEMENT_TYPES[5];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_CardWrapper__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        id: \"community-board\",\n        color: \"bgDark\",\n        className: \"border-l-4 border-accent h-full\",\n        count: announcements.length,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-3 h-3 bg-accent rounded-full mr-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-primary\",\n                        children: \"\\uD83D\\uDCCB Community Board\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this),\n            !showForm ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setShowForm(true),\n                disabled: !isClient,\n                className: \"w-full p-3 rounded-lg mb-4 font-medium transition-all duration-200 \".concat(!isClient ? \"bg-gray-300 text-gray-500 cursor-not-allowed\" : \"bg-accent hover:bg-teal-600 text-white shadow-md hover:shadow-lg transform hover:scale-[1.02]\"),\n                children: !isClient ? \"Loading...\" : \"➕ Post Announcement\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                lineNumber: 73,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 bg-white p-4 rounded-lg border border-gray-200 shadow-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium text-primary block mb-1\",\n                                        children: \"Category:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: type,\n                                        onChange: (e)=>setType(e.target.value),\n                                        className: \"w-full border border-gray-300 rounded px-3 py-2 focus:ring-2 focus:ring-accent focus:border-accent\",\n                                        disabled: isSubmitting,\n                                        children: ANNOUNCEMENT_TYPES.map((typeOption)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: typeOption.value,\n                                                children: typeOption.label\n                                            }, typeOption.value, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium text-primary block mb-1\",\n                                        children: \"Title:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: title,\n                                        onChange: (e)=>setTitle(e.target.value),\n                                        placeholder: \"Enter a title...\",\n                                        className: \"w-full border border-gray-300 rounded px-3 py-2 focus:ring-2 focus:ring-accent focus:border-accent\",\n                                        disabled: isSubmitting,\n                                        maxLength: 100\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: [\n                                            title.length,\n                                            \"/100 characters\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium text-primary block mb-1\",\n                                        children: \"Description:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        value: description,\n                                        onChange: (e)=>setDescription(e.target.value),\n                                        placeholder: \"Provide details...\",\n                                        className: \"w-full border border-gray-300 rounded px-3 py-2 focus:ring-2 focus:ring-accent focus:border-accent h-20 resize-none\",\n                                        disabled: isSubmitting,\n                                        maxLength: 500\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: [\n                                            description.length,\n                                            \"/500 characters\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2 mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handlePostAnnouncement,\n                                disabled: isSubmitting || !title.trim() || !description.trim() || !isClient,\n                                className: \"flex-1 p-2 rounded-lg font-medium transition-colors \".concat(isSubmitting || !title.trim() || !description.trim() || !isClient ? \"bg-gray-300 text-gray-500 cursor-not-allowed\" : \"bg-accent hover:bg-teal-600 text-white\"),\n                                children: isSubmitting ? \"Posting...\" : \"Post\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCancel,\n                                disabled: isSubmitting,\n                                className: \"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\",\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                lineNumber: 85,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3 max-h-64 overflow-y-auto\",\n                children: [\n                    announcements.map((entry)=>{\n                        const typeInfo = getTypeInfo(entry.type);\n                        const canDelete = isClient && (0,_src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_3__.isCurrentUserPost)(entry.user);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white border border-gray-200 rounded-lg p-3 shadow-sm hover:shadow-md transition-shadow\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-start mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 flex-wrap\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs px-2 py-1 rounded-full \".concat(typeInfo.color),\n                                                    children: typeInfo.label\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 19\n                                                }, this),\n                                                canDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs bg-accent/10 text-accent px-2 py-1 rounded-full\",\n                                                    children: \"Your post\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 17\n                                        }, this),\n                                        canDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleDelete(entry.id),\n                                            className: \"text-warn text-xs hover:text-red-700 px-2 py-1 hover:bg-red-50 rounded transition-colors\",\n                                            title: \"Delete your post\",\n                                            children: \"Delete\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-medium text-primary mb-1\",\n                                    children: entry.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-700 mb-2\",\n                                    children: entry.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center text-xs text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: entry.user\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: entry.timestamp.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, entry.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 13\n                        }, this);\n                    }),\n                    announcements.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-gray-600 py-4\",\n                        children: \"No announcements yet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 40\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n_s(AnnouncementsCard, \"iAdoa4ZlLq2hG/Lyv2TyLfToS+4=\", false, function() {\n    return [\n        _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n    ];\n});\n_c = AnnouncementsCard;\nvar _c;\n$RefreshReg$(_c, \"AnnouncementsCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AnnouncementsCard.tsx\n"));

/***/ })

});