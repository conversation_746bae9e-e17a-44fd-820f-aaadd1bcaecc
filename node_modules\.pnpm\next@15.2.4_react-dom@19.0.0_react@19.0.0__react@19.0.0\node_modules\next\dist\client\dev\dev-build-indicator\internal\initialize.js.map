{"version": 3, "sources": ["../../../../../src/client/dev/dev-build-indicator/internal/initialize.ts"], "sourcesContent": ["/*\n * Singleton store to track whether the app is currently being built\n * Used by the dev tools indicator of the new overlay to show build status\n */\n\nimport { devBuildIndicator } from './dev-build-indicator'\nimport { useSyncExternalStore } from 'react'\n\nlet isVisible = false\nlet listeners: Array<() => void> = []\n\nconst subscribe = (listener: () => void) => {\n  listeners.push(listener)\n  return () => {\n    listeners = listeners.filter((l) => l !== listener)\n  }\n}\n\nconst getSnapshot = () => isVisible\n\nexport function useIsDevBuilding() {\n  return useSyncExternalStore(subscribe, getSnapshot)\n}\n\nexport function initialize() {\n  devBuildIndicator.show = () => {\n    isVisible = true\n    listeners.forEach((listener) => listener())\n  }\n\n  devBuildIndicator.hide = () => {\n    isVisible = false\n    listeners.forEach((listener) => listener())\n  }\n}\n"], "names": ["initialize", "useIsDevBuilding", "isVisible", "listeners", "subscribe", "listener", "push", "filter", "l", "getSnapshot", "useSyncExternalStore", "devBuildIndicator", "show", "for<PERSON>ach", "hide"], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;IAqBeA,UAAU;eAAVA;;IAJAC,gBAAgB;eAAhBA;;;mCAfkB;uBACG;AAErC,IAAIC,YAAY;AAChB,IAAIC,YAA+B,EAAE;AAErC,MAAMC,YAAY,CAACC;IACjBF,UAAUG,IAAI,CAACD;IACf,OAAO;QACLF,YAAYA,UAAUI,MAAM,CAAC,CAACC,IAAMA,MAAMH;IAC5C;AACF;AAEA,MAAMI,cAAc,IAAMP;AAEnB,SAASD;IACd,OAAOS,IAAAA,2BAAoB,EAACN,WAAWK;AACzC;AAEO,SAAST;IACdW,oCAAiB,CAACC,IAAI,GAAG;QACvBV,YAAY;QACZC,UAAUU,OAAO,CAAC,CAACR,WAAaA;IAClC;IAEAM,oCAAiB,CAACG,IAAI,GAAG;QACvBZ,YAAY;QACZC,UAAUU,OAAO,CAAC,CAACR,WAAaA;IAClC;AACF"}