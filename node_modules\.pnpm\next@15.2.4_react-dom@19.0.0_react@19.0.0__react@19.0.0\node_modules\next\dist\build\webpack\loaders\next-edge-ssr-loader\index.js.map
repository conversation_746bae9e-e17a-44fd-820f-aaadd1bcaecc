{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-edge-ssr-loader/index.ts"], "sourcesContent": ["import type webpack from 'webpack'\nimport type { SizeLimit } from '../../../../types'\nimport type { PagesRouteModuleOptions } from '../../../../server/route-modules/pages/module'\nimport type { MiddlewareConfig } from '../../../analysis/get-page-static-info'\n\nimport { getModuleBuildInfo } from '../get-module-build-info'\nimport { WEBPACK_RESOURCE_QUERIES } from '../../../../lib/constants'\nimport { RouteKind } from '../../../../server/route-kind'\nimport { normalizePagePath } from '../../../../shared/lib/page-path/normalize-page-path'\nimport { loadEntrypoint } from '../../../load-entrypoint'\nimport type { PAGE_TYPES } from '../../../../lib/page-types'\n\nexport type EdgeSSRLoaderQuery = {\n  absolute500Path: string\n  absoluteAppPath: string\n  absoluteDocumentPath: string\n  absoluteErrorPath: string\n  absolutePagePath: string\n  dev: boolean\n  isServerComponent: boolean\n  page: string\n  stringifiedConfig: string\n  appDirLoader?: string\n  pagesType: PAGE_TYPES\n  sriEnabled: boolean\n  cacheHandler?: string\n  cacheHandlers?: string\n  preferredRegion: string | string[] | undefined\n  middlewareConfig: string\n  serverActions?: {\n    bodySizeLimit?: SizeLimit\n    allowedOrigins?: string[]\n  }\n}\n\n/*\nFor pages SSR'd at the edge, we bundle them with the ESM version of Next in order to\nbenefit from the better tree-shaking and thus, smaller bundle sizes.\n\nThe absolute paths for _app, _error and _document, used in this loader, link to the regular CJS modules.\nThey are generated in `createPagesMapping` where we don't have access to `isEdgeRuntime`,\nso we have to do it here. It's not that bad because it keeps all references to ESM modules magic in this place.\n*/\nfunction swapDistFolderWithEsmDistFolder(path: string) {\n  return path.replace('next/dist/pages', 'next/dist/esm/pages')\n}\n\nfunction getRouteModuleOptions(page: string) {\n  const options: Omit<PagesRouteModuleOptions, 'userland' | 'components'> = {\n    definition: {\n      kind: RouteKind.PAGES,\n      page: normalizePagePath(page),\n      pathname: page,\n      // The following aren't used in production.\n      bundlePath: '',\n      filename: '',\n    },\n  }\n\n  return options\n}\n\nconst edgeSSRLoader: webpack.LoaderDefinitionFunction<EdgeSSRLoaderQuery> =\n  async function edgeSSRLoader(this) {\n    const {\n      dev,\n      page,\n      absolutePagePath,\n      absoluteAppPath,\n      absoluteDocumentPath,\n      absolute500Path,\n      absoluteErrorPath,\n      isServerComponent,\n      stringifiedConfig: stringifiedConfigBase64,\n      appDirLoader: appDirLoaderBase64,\n      pagesType,\n      sriEnabled,\n      cacheHandler,\n      cacheHandlers: cacheHandlersStringified,\n      preferredRegion,\n      middlewareConfig: middlewareConfigBase64,\n      serverActions,\n    } = this.getOptions()\n\n    const cacheHandlers = JSON.parse(cacheHandlersStringified || '{}')\n\n    if (!cacheHandlers.default) {\n      cacheHandlers.default = require.resolve(\n        '../../../../server/lib/cache-handlers/default'\n      )\n    }\n\n    const middlewareConfig: MiddlewareConfig = JSON.parse(\n      Buffer.from(middlewareConfigBase64, 'base64').toString()\n    )\n\n    const stringifiedConfig = Buffer.from(\n      stringifiedConfigBase64 || '',\n      'base64'\n    ).toString()\n    const appDirLoader = Buffer.from(\n      appDirLoaderBase64 || '',\n      'base64'\n    ).toString()\n    const isAppDir = pagesType === 'app'\n\n    const buildInfo = getModuleBuildInfo(this._module as any)\n    buildInfo.nextEdgeSSR = {\n      isServerComponent,\n      page: page,\n      isAppDir,\n    }\n    buildInfo.route = {\n      page,\n      absolutePagePath,\n      preferredRegion,\n      middlewareConfig,\n    }\n\n    const pagePath = this.utils.contextify(\n      this.context || this.rootContext,\n      absolutePagePath\n    )\n    const appPath = this.utils.contextify(\n      this.context || this.rootContext,\n      swapDistFolderWithEsmDistFolder(absoluteAppPath)\n    )\n    const errorPath = this.utils.contextify(\n      this.context || this.rootContext,\n      swapDistFolderWithEsmDistFolder(absoluteErrorPath)\n    )\n    const documentPath = this.utils.contextify(\n      this.context || this.rootContext,\n      swapDistFolderWithEsmDistFolder(absoluteDocumentPath)\n    )\n    const userland500Path = absolute500Path\n      ? this.utils.contextify(\n          this.context || this.rootContext,\n          swapDistFolderWithEsmDistFolder(absolute500Path)\n        )\n      : null\n\n    const stringifiedPagePath = JSON.stringify(pagePath)\n\n    const pageModPath = `${appDirLoader}${stringifiedPagePath.substring(\n      1,\n      stringifiedPagePath.length - 1\n    )}${isAppDir ? `?${WEBPACK_RESOURCE_QUERIES.edgeSSREntry}` : ''}`\n\n    if (isAppDir) {\n      return await loadEntrypoint(\n        'edge-ssr-app',\n        {\n          VAR_USERLAND: pageModPath,\n          VAR_PAGE: page,\n        },\n        {\n          sriEnabled: JSON.stringify(sriEnabled),\n          nextConfig: stringifiedConfig,\n          isServerComponent: JSON.stringify(isServerComponent),\n          dev: JSON.stringify(dev),\n          serverActions:\n            typeof serverActions === 'undefined'\n              ? 'undefined'\n              : JSON.stringify(serverActions),\n        },\n        {\n          incrementalCacheHandler: cacheHandler ?? null,\n        }\n      )\n    } else {\n      return await loadEntrypoint(\n        'edge-ssr',\n        {\n          VAR_USERLAND: pageModPath,\n          VAR_PAGE: page,\n          VAR_MODULE_DOCUMENT: documentPath,\n          VAR_MODULE_APP: appPath,\n          VAR_MODULE_GLOBAL_ERROR: errorPath,\n        },\n        {\n          pagesType: JSON.stringify(pagesType),\n          sriEnabled: JSON.stringify(sriEnabled),\n          nextConfig: stringifiedConfig,\n          dev: JSON.stringify(dev),\n          pageRouteModuleOptions: JSON.stringify(getRouteModuleOptions(page)),\n          errorRouteModuleOptions: JSON.stringify(\n            getRouteModuleOptions('/_error')\n          ),\n          user500RouteModuleOptions: JSON.stringify(\n            getRouteModuleOptions('/500')\n          ),\n        },\n        {\n          userland500Page: userland500Path,\n          incrementalCacheHandler: cacheHandler ?? null,\n        }\n      )\n    }\n  }\nexport default edgeSSRLoader\n"], "names": ["swapDistFolderWithEsmDistFolder", "path", "replace", "getRouteModuleOptions", "page", "options", "definition", "kind", "RouteKind", "PAGES", "normalizePagePath", "pathname", "bundlePath", "filename", "edgeSSRLoader", "dev", "absolutePagePath", "absoluteAppPath", "absoluteDocumentPath", "absolute500Path", "absoluteErrorPath", "isServerComponent", "stringifiedConfig", "stringifiedConfigBase64", "appDirLoader", "appDirLoaderBase64", "pagesType", "sriEnabled", "cache<PERSON><PERSON><PERSON>", "cacheHandlers", "cacheHandlersStringified", "preferredRegion", "middlewareConfig", "middlewareConfigBase64", "serverActions", "getOptions", "JSON", "parse", "default", "require", "resolve", "<PERSON><PERSON><PERSON>", "from", "toString", "isAppDir", "buildInfo", "getModuleBuildInfo", "_module", "nextEdgeSSR", "route", "pagePath", "utils", "contextify", "context", "rootContext", "appPath", "errorPath", "documentPath", "userland500Path", "stringifiedPagePath", "stringify", "pageModPath", "substring", "length", "WEBPACK_RESOURCE_QUERIES", "edgeSSREntry", "loadEntrypoint", "VAR_USERLAND", "VAR_PAGE", "nextConfig", "incremental<PERSON>ache<PERSON><PERSON><PERSON>", "VAR_MODULE_DOCUMENT", "VAR_MODULE_APP", "VAR_MODULE_GLOBAL_ERROR", "pageRouteModuleOptions", "errorRouteModuleOptions", "user500RouteModuleOptions", "userland500Page"], "mappings": ";;;;+BAwMA;;;eAAA;;;oCAnMmC;2BACM;2BACf;mCACQ;gCACH;AA0B/B;;;;;;;AAOA,GACA,SAASA,gCAAgCC,IAAY;IACnD,OAAOA,KAAKC,OAAO,CAAC,mBAAmB;AACzC;AAEA,SAASC,sBAAsBC,IAAY;IACzC,MAAMC,UAAoE;QACxEC,YAAY;YACVC,MAAMC,oBAAS,CAACC,KAAK;YACrBL,MAAMM,IAAAA,oCAAiB,EAACN;YACxBO,UAAUP;YACV,2CAA2C;YAC3CQ,YAAY;YACZC,UAAU;QACZ;IACF;IAEA,OAAOR;AACT;AAEA,MAAMS,gBACJ,eAAeA;IACb,MAAM,EACJC,GAAG,EACHX,IAAI,EACJY,gBAAgB,EAChBC,eAAe,EACfC,oBAAoB,EACpBC,eAAe,EACfC,iBAAiB,EACjBC,iBAAiB,EACjBC,mBAAmBC,uBAAuB,EAC1CC,cAAcC,kBAAkB,EAChCC,SAAS,EACTC,UAAU,EACVC,YAAY,EACZC,eAAeC,wBAAwB,EACvCC,eAAe,EACfC,kBAAkBC,sBAAsB,EACxCC,aAAa,EACd,GAAG,IAAI,CAACC,UAAU;IAEnB,MAAMN,gBAAgBO,KAAKC,KAAK,CAACP,4BAA4B;IAE7D,IAAI,CAACD,cAAcS,OAAO,EAAE;QAC1BT,cAAcS,OAAO,GAAGC,QAAQC,OAAO,CACrC;IAEJ;IAEA,MAAMR,mBAAqCI,KAAKC,KAAK,CACnDI,OAAOC,IAAI,CAACT,wBAAwB,UAAUU,QAAQ;IAGxD,MAAMrB,oBAAoBmB,OAAOC,IAAI,CACnCnB,2BAA2B,IAC3B,UACAoB,QAAQ;IACV,MAAMnB,eAAeiB,OAAOC,IAAI,CAC9BjB,sBAAsB,IACtB,UACAkB,QAAQ;IACV,MAAMC,WAAWlB,cAAc;IAE/B,MAAMmB,YAAYC,IAAAA,sCAAkB,EAAC,IAAI,CAACC,OAAO;IACjDF,UAAUG,WAAW,GAAG;QACtB3B;QACAjB,MAAMA;QACNwC;IACF;IACAC,UAAUI,KAAK,GAAG;QAChB7C;QACAY;QACAe;QACAC;IACF;IAEA,MAAMkB,WAAW,IAAI,CAACC,KAAK,CAACC,UAAU,CACpC,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,WAAW,EAChCtC;IAEF,MAAMuC,UAAU,IAAI,CAACJ,KAAK,CAACC,UAAU,CACnC,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,WAAW,EAChCtD,gCAAgCiB;IAElC,MAAMuC,YAAY,IAAI,CAACL,KAAK,CAACC,UAAU,CACrC,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,WAAW,EAChCtD,gCAAgCoB;IAElC,MAAMqC,eAAe,IAAI,CAACN,KAAK,CAACC,UAAU,CACxC,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,WAAW,EAChCtD,gCAAgCkB;IAElC,MAAMwC,kBAAkBvC,kBACpB,IAAI,CAACgC,KAAK,CAACC,UAAU,CACnB,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,WAAW,EAChCtD,gCAAgCmB,oBAElC;IAEJ,MAAMwC,sBAAsBvB,KAAKwB,SAAS,CAACV;IAE3C,MAAMW,cAAc,GAAGrC,eAAemC,oBAAoBG,SAAS,CACjE,GACAH,oBAAoBI,MAAM,GAAG,KAC3BnB,WAAW,CAAC,CAAC,EAAEoB,mCAAwB,CAACC,YAAY,EAAE,GAAG,IAAI;IAEjE,IAAIrB,UAAU;QACZ,OAAO,MAAMsB,IAAAA,8BAAc,EACzB,gBACA;YACEC,cAAcN;YACdO,UAAUhE;QACZ,GACA;YACEuB,YAAYS,KAAKwB,SAAS,CAACjC;YAC3B0C,YAAY/C;YACZD,mBAAmBe,KAAKwB,SAAS,CAACvC;YAClCN,KAAKqB,KAAKwB,SAAS,CAAC7C;YACpBmB,eACE,OAAOA,kBAAkB,cACrB,cACAE,KAAKwB,SAAS,CAAC1B;QACvB,GACA;YACEoC,yBAAyB1C,gBAAgB;QAC3C;IAEJ,OAAO;QACL,OAAO,MAAMsC,IAAAA,8BAAc,EACzB,YACA;YACEC,cAAcN;YACdO,UAAUhE;YACVmE,qBAAqBd;YACrBe,gBAAgBjB;YAChBkB,yBAAyBjB;QAC3B,GACA;YACE9B,WAAWU,KAAKwB,SAAS,CAAClC;YAC1BC,YAAYS,KAAKwB,SAAS,CAACjC;YAC3B0C,YAAY/C;YACZP,KAAKqB,KAAKwB,SAAS,CAAC7C;YACpB2D,wBAAwBtC,KAAKwB,SAAS,CAACzD,sBAAsBC;YAC7DuE,yBAAyBvC,KAAKwB,SAAS,CACrCzD,sBAAsB;YAExByE,2BAA2BxC,KAAKwB,SAAS,CACvCzD,sBAAsB;QAE1B,GACA;YACE0E,iBAAiBnB;YACjBY,yBAAyB1C,gBAAgB;QAC3C;IAEJ;AACF;MACF,WAAed"}