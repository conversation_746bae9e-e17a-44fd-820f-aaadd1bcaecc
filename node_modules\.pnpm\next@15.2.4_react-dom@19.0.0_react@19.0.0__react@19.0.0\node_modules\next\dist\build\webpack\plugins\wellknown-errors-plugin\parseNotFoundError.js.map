{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/parseNotFoundError.ts"], "sourcesContent": ["import { bold, cyan, green, red, yellow } from '../../../../lib/picocolors'\nimport { SimpleWebpackError } from './simpleWebpackError'\nimport {\n  createOriginalStackFrame,\n  getIgnoredSources,\n} from '../../../../client/components/react-dev-overlay/server/middleware-webpack'\nimport type { webpack } from 'next/dist/compiled/webpack/webpack'\n\n// Based on https://github.com/webpack/webpack/blob/fcdd04a833943394bbb0a9eeb54a962a24cc7e41/lib/stats/DefaultStatsFactoryPlugin.js#L422-L431\n/*\nCopyright JS Foundation and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n*/\nfunction getModuleTrace(input: any, compilation: any) {\n  const visitedModules = new Set()\n  const moduleTrace = []\n  let current = input.module\n  while (current) {\n    if (visitedModules.has(current)) break // circular (technically impossible, but who knows)\n    visitedModules.add(current)\n    const origin = compilation.moduleGraph.getIssuer(current)\n    if (!origin) break\n    moduleTrace.push({ origin, module: current })\n    current = origin\n  }\n\n  return moduleTrace\n}\n\nasync function getSourceFrame(\n  input: any,\n  fileName: any,\n  compilation: any\n): Promise<{ frame: string; lineNumber: string; column: string }> {\n  try {\n    const loc =\n      input.loc || input.dependencies.map((d: any) => d.loc).filter(Boolean)[0]\n    const module = input.module as webpack.Module\n    const originalSource = module.originalSource()\n    const sourceMap = originalSource?.map() ?? undefined\n\n    if (sourceMap) {\n      const moduleId = compilation.chunkGraph.getModuleId(module)\n\n      const result = await createOriginalStackFrame({\n        source: {\n          type: 'bundle',\n          sourceMap,\n          ignoredSources: getIgnoredSources(sourceMap),\n          compilation,\n          moduleId,\n          moduleURL: fileName,\n        },\n        rootDirectory: compilation.options.context!,\n        frame: {\n          arguments: [],\n          file: fileName,\n          methodName: '',\n          lineNumber: loc.start.line,\n          // loc is 0-based but columns in stack frames are 1-based.\n          column: (loc.start.column ?? 0) + 1,\n        },\n      })\n\n      return {\n        frame: result?.originalCodeFrame ?? '',\n        lineNumber: result?.originalStackFrame?.lineNumber?.toString() ?? '',\n        column: result?.originalStackFrame?.column?.toString() ?? '',\n      }\n    }\n  } catch {}\n\n  return { frame: '', lineNumber: '', column: '' }\n}\n\nfunction getFormattedFileName(\n  fileName: string,\n  module: any,\n  lineNumber?: string,\n  column?: string\n): string {\n  if (\n    module.loaders?.find((loader: any) =>\n      /next-font-loader[/\\\\]index.js/.test(loader.loader)\n    )\n  ) {\n    // Parse the query and get the path of the file where the font function was called.\n    // provided by next-swc next-transform-font\n    return JSON.parse(module.resourceResolveData.query.slice(1)).path\n  } else {\n    let formattedFileName: string = cyan(fileName)\n    if (lineNumber && column) {\n      formattedFileName += `:${yellow(lineNumber)}:${yellow(column)}`\n    }\n\n    return formattedFileName\n  }\n}\n\nexport async function getNotFoundError(\n  compilation: webpack.Compilation,\n  input: any,\n  fileName: string,\n  module: any\n) {\n  if (\n    input.name !== 'ModuleNotFoundError' &&\n    !(\n      input.name === 'ModuleBuildError' &&\n      /Error: Can't resolve '.+' in /.test(input.message)\n    )\n  ) {\n    return false\n  }\n\n  try {\n    const { frame, lineNumber, column } = await getSourceFrame(\n      input,\n      fileName,\n      compilation\n    )\n\n    const errorMessage = input.error.message\n      .replace(/ in '.*?'/, '')\n      .replace(/Can't resolve '(.*)'/, `Can't resolve '${green('$1')}'`)\n\n    const importTrace = () => {\n      const moduleTrace = getModuleTrace(input, compilation)\n        .map(({ origin }) =>\n          origin.readableIdentifier(compilation.requestShortener)\n        )\n        .filter(\n          (name) =>\n            name &&\n            !/next-(app|middleware|client-pages|route|flight-(client|server|client-entry))-loader/.test(\n              name\n            ) &&\n            !/css-loader.+\\.js/.test(name)\n        )\n      if (moduleTrace.length === 0) return ''\n\n      return `\\nImport trace for requested module:\\n${moduleTrace.join('\\n')}`\n    }\n\n    let message =\n      red(bold('Module not found')) +\n      `: ${errorMessage}` +\n      '\\n' +\n      frame +\n      (frame !== '' ? '\\n' : '') +\n      '\\nhttps://nextjs.org/docs/messages/module-not-found\\n' +\n      importTrace()\n\n    const formattedFileName = getFormattedFileName(\n      fileName,\n      module,\n      lineNumber,\n      column\n    )\n\n    return new SimpleWebpackError(formattedFileName, message)\n  } catch (err) {\n    // Don't fail on failure to resolve sourcemaps\n    return input\n  }\n}\n\nexport async function getImageError(\n  compilation: any,\n  input: any,\n  err: Error\n): Promise<SimpleWebpackError | false> {\n  if (err.name !== 'InvalidImageFormatError') {\n    return false\n  }\n\n  const moduleTrace = getModuleTrace(input, compilation)\n  const { origin, module } = moduleTrace[0] || {}\n  if (!origin || !module) {\n    return false\n  }\n  const page = origin.rawRequest.replace(/^private-next-pages/, './pages')\n  const importedFile = module.rawRequest\n  const source = origin.originalSource().buffer().toString('utf8') as string\n  let lineNumber = -1\n  source.split('\\n').some((line) => {\n    lineNumber++\n    return line.includes(importedFile)\n  })\n  return new SimpleWebpackError(\n    `${cyan(page)}:${yellow(lineNumber.toString())}`,\n    red(bold('Error')).concat(\n      `: Image import \"${importedFile}\" is not a valid image file. The image may be corrupted or an unsupported format.`\n    )\n  )\n}\n"], "names": ["getImageError", "getNotFoundError", "getModuleTrace", "input", "compilation", "visitedModules", "Set", "moduleTrace", "current", "module", "has", "add", "origin", "moduleGraph", "get<PERSON><PERSON><PERSON>", "push", "getSourceFrame", "fileName", "loc", "dependencies", "map", "d", "filter", "Boolean", "originalSource", "sourceMap", "undefined", "result", "moduleId", "chunkGraph", "getModuleId", "createOriginalStackFrame", "source", "type", "ignoredSources", "getIgnoredSources", "moduleURL", "rootDirectory", "options", "context", "frame", "arguments", "file", "methodName", "lineNumber", "start", "line", "column", "originalCodeFrame", "originalStackFrame", "toString", "getFormattedFileName", "loaders", "find", "loader", "test", "JSON", "parse", "resourceResolveData", "query", "slice", "path", "formattedFileName", "cyan", "yellow", "name", "message", "errorMessage", "error", "replace", "green", "importTrace", "readableIdentifier", "requestShortener", "length", "join", "red", "bold", "SimpleWebpackError", "err", "page", "rawRequest", "importedFile", "buffer", "split", "some", "includes", "concat"], "mappings": ";;;;;;;;;;;;;;;IA0LsBA,aAAa;eAAbA;;IApEAC,gBAAgB;eAAhBA;;;4BAtHyB;oCACZ;mCAI5B;AAGP,6IAA6I;AAC7I;;;;;;;;;;;;;;;;;;;;;;AAsBA,GACA,SAASC,eAAeC,KAAU,EAAEC,WAAgB;IAClD,MAAMC,iBAAiB,IAAIC;IAC3B,MAAMC,cAAc,EAAE;IACtB,IAAIC,UAAUL,MAAMM,MAAM;IAC1B,MAAOD,QAAS;QACd,IAAIH,eAAeK,GAAG,CAACF,UAAU,OAAM,mDAAmD;QAC1FH,eAAeM,GAAG,CAACH;QACnB,MAAMI,SAASR,YAAYS,WAAW,CAACC,SAAS,CAACN;QACjD,IAAI,CAACI,QAAQ;QACbL,YAAYQ,IAAI,CAAC;YAAEH;YAAQH,QAAQD;QAAQ;QAC3CA,UAAUI;IACZ;IAEA,OAAOL;AACT;AAEA,eAAeS,eACbb,KAAU,EACVc,QAAa,EACbb,WAAgB;IAEhB,IAAI;QACF,MAAMc,MACJf,MAAMe,GAAG,IAAIf,MAAMgB,YAAY,CAACC,GAAG,CAAC,CAACC,IAAWA,EAAEH,GAAG,EAAEI,MAAM,CAACC,QAAQ,CAAC,EAAE;QAC3E,MAAMd,UAASN,MAAMM,MAAM;QAC3B,MAAMe,iBAAiBf,QAAOe,cAAc;QAC5C,MAAMC,YAAYD,CAAAA,kCAAAA,eAAgBJ,GAAG,OAAMM;QAE3C,IAAID,WAAW;gBAyBCE,uCAAAA,4BACJA,mCAAAA;YAzBV,MAAMC,WAAWxB,YAAYyB,UAAU,CAACC,WAAW,CAACrB;YAEpD,MAAMkB,SAAS,MAAMI,IAAAA,2CAAwB,EAAC;gBAC5CC,QAAQ;oBACNC,MAAM;oBACNR;oBACAS,gBAAgBC,IAAAA,oCAAiB,EAACV;oBAClCrB;oBACAwB;oBACAQ,WAAWnB;gBACb;gBACAoB,eAAejC,YAAYkC,OAAO,CAACC,OAAO;gBAC1CC,OAAO;oBACLC,WAAW,EAAE;oBACbC,MAAMzB;oBACN0B,YAAY;oBACZC,YAAY1B,IAAI2B,KAAK,CAACC,IAAI;oBAC1B,0DAA0D;oBAC1DC,QAAQ,AAAC7B,CAAAA,IAAI2B,KAAK,CAACE,MAAM,IAAI,CAAA,IAAK;gBACpC;YACF;YAEA,OAAO;gBACLP,OAAOb,CAAAA,0BAAAA,OAAQqB,iBAAiB,KAAI;gBACpCJ,YAAYjB,CAAAA,2BAAAA,6BAAAA,OAAQsB,kBAAkB,sBAA1BtB,wCAAAA,2BAA4BiB,UAAU,qBAAtCjB,sCAAwCuB,QAAQ,OAAM;gBAClEH,QAAQpB,CAAAA,2BAAAA,8BAAAA,OAAQsB,kBAAkB,sBAA1BtB,oCAAAA,4BAA4BoB,MAAM,qBAAlCpB,kCAAoCuB,QAAQ,OAAM;YAC5D;QACF;IACF,EAAE,OAAM,CAAC;IAET,OAAO;QAAEV,OAAO;QAAII,YAAY;QAAIG,QAAQ;IAAG;AACjD;AAEA,SAASI,qBACPlC,QAAgB,EAChBR,OAAW,EACXmC,UAAmB,EACnBG,MAAe;QAGbtC;IADF,KACEA,kBAAAA,QAAO2C,OAAO,qBAAd3C,gBAAgB4C,IAAI,CAAC,CAACC,SACpB,gCAAgCC,IAAI,CAACD,OAAOA,MAAM,IAEpD;QACA,mFAAmF;QACnF,2CAA2C;QAC3C,OAAOE,KAAKC,KAAK,CAAChD,QAAOiD,mBAAmB,CAACC,KAAK,CAACC,KAAK,CAAC,IAAIC,IAAI;IACnE,OAAO;QACL,IAAIC,oBAA4BC,IAAAA,gBAAI,EAAC9C;QACrC,IAAI2B,cAAcG,QAAQ;YACxBe,qBAAqB,CAAC,CAAC,EAAEE,IAAAA,kBAAM,EAACpB,YAAY,CAAC,EAAEoB,IAAAA,kBAAM,EAACjB,SAAS;QACjE;QAEA,OAAOe;IACT;AACF;AAEO,eAAe7D,iBACpBG,WAAgC,EAChCD,KAAU,EACVc,QAAgB,EAChBR,OAAW;IAEX,IACEN,MAAM8D,IAAI,KAAK,yBACf,CACE9D,CAAAA,MAAM8D,IAAI,KAAK,sBACf,gCAAgCV,IAAI,CAACpD,MAAM+D,OAAO,CAAA,GAEpD;QACA,OAAO;IACT;IAEA,IAAI;QACF,MAAM,EAAE1B,KAAK,EAAEI,UAAU,EAAEG,MAAM,EAAE,GAAG,MAAM/B,eAC1Cb,OACAc,UACAb;QAGF,MAAM+D,eAAehE,MAAMiE,KAAK,CAACF,OAAO,CACrCG,OAAO,CAAC,aAAa,IACrBA,OAAO,CAAC,wBAAwB,CAAC,eAAe,EAAEC,IAAAA,iBAAK,EAAC,MAAM,CAAC,CAAC;QAEnE,MAAMC,cAAc;YAClB,MAAMhE,cAAcL,eAAeC,OAAOC,aACvCgB,GAAG,CAAC,CAAC,EAAER,MAAM,EAAE,GACdA,OAAO4D,kBAAkB,CAACpE,YAAYqE,gBAAgB,GAEvDnD,MAAM,CACL,CAAC2C,OACCA,QACA,CAAC,sFAAsFV,IAAI,CACzFU,SAEF,CAAC,mBAAmBV,IAAI,CAACU;YAE/B,IAAI1D,YAAYmE,MAAM,KAAK,GAAG,OAAO;YAErC,OAAO,CAAC,sCAAsC,EAAEnE,YAAYoE,IAAI,CAAC,OAAO;QAC1E;QAEA,IAAIT,UACFU,IAAAA,eAAG,EAACC,IAAAA,gBAAI,EAAC,uBACT,CAAC,EAAE,EAAEV,cAAc,GACnB,OACA3B,QACCA,CAAAA,UAAU,KAAK,OAAO,EAAC,IACxB,0DACA+B;QAEF,MAAMT,oBAAoBX,qBACxBlC,UACAR,SACAmC,YACAG;QAGF,OAAO,IAAI+B,sCAAkB,CAAChB,mBAAmBI;IACnD,EAAE,OAAOa,KAAK;QACZ,8CAA8C;QAC9C,OAAO5E;IACT;AACF;AAEO,eAAeH,cACpBI,WAAgB,EAChBD,KAAU,EACV4E,GAAU;IAEV,IAAIA,IAAId,IAAI,KAAK,2BAA2B;QAC1C,OAAO;IACT;IAEA,MAAM1D,cAAcL,eAAeC,OAAOC;IAC1C,MAAM,EAAEQ,MAAM,EAAEH,QAAAA,OAAM,EAAE,GAAGF,WAAW,CAAC,EAAE,IAAI,CAAC;IAC9C,IAAI,CAACK,UAAU,CAACH,SAAQ;QACtB,OAAO;IACT;IACA,MAAMuE,OAAOpE,OAAOqE,UAAU,CAACZ,OAAO,CAAC,uBAAuB;IAC9D,MAAMa,eAAezE,QAAOwE,UAAU;IACtC,MAAMjD,SAASpB,OAAOY,cAAc,GAAG2D,MAAM,GAAGjC,QAAQ,CAAC;IACzD,IAAIN,aAAa,CAAC;IAClBZ,OAAOoD,KAAK,CAAC,MAAMC,IAAI,CAAC,CAACvC;QACvBF;QACA,OAAOE,KAAKwC,QAAQ,CAACJ;IACvB;IACA,OAAO,IAAIJ,sCAAkB,CAC3B,GAAGf,IAAAA,gBAAI,EAACiB,MAAM,CAAC,EAAEhB,IAAAA,kBAAM,EAACpB,WAAWM,QAAQ,KAAK,EAChD0B,IAAAA,eAAG,EAACC,IAAAA,gBAAI,EAAC,UAAUU,MAAM,CACvB,CAAC,gBAAgB,EAAEL,aAAa,iFAAiF,CAAC;AAGxH"}