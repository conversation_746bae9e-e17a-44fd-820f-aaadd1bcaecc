{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-flight-loader/module-proxy.ts"], "sourcesContent": ["/* eslint-disable import/no-extraneous-dependencies */\nimport { createClientModuleProxy } from 'react-server-dom-webpack/server.edge'\n\n// Re-assign to make it typed.\nexport const createProxy: (moduleId: string) => any = createClientModuleProxy\n"], "names": ["createProxy", "createClientModuleProxy"], "mappings": "AAAA,oDAAoD;;;;+BAIvCA;;;eAAAA;;;4BAH2B;AAGjC,MAAMA,cAAyCC,mCAAuB"}