{"version": 3, "sources": ["../../../../src/build/babel/plugins/amp-attributes.ts"], "sourcesContent": ["import type { NodePath, types, PluginObj } from 'next/dist/compiled/babel/core'\n\nexport default function AmpAttributePatcher(): PluginObj {\n  return {\n    visitor: {\n      JSXOpeningElement(path: NodePath<types.JSXOpeningElement>) {\n        const openingElement = path.node\n\n        const { name, attributes } = openingElement\n        if (!(name && name.type === 'JSXIdentifier')) {\n          return\n        }\n\n        if (!name.name.startsWith('amp-')) {\n          return\n        }\n\n        for (const attribute of attributes) {\n          if (attribute.type !== 'JSXAttribute') {\n            continue\n          }\n\n          if (attribute.name.name === 'className') {\n            attribute.name.name = 'class'\n          }\n        }\n      },\n    },\n  }\n}\n"], "names": ["AmpAttributePatcher", "visitor", "JSXOpeningElement", "path", "openingElement", "node", "name", "attributes", "type", "startsWith", "attribute"], "mappings": ";;;;+BAEA;;;eAAwBA;;;AAAT,SAASA;IACtB,OAAO;QACLC,SAAS;YACPC,mBAAkBC,IAAuC;gBACvD,MAAMC,iBAAiBD,KAAKE,IAAI;gBAEhC,MAAM,EAAEC,IAAI,EAAEC,UAAU,EAAE,GAAGH;gBAC7B,IAAI,CAAEE,CAAAA,QAAQA,KAAKE,IAAI,KAAK,eAAc,GAAI;oBAC5C;gBACF;gBAEA,IAAI,CAACF,KAAKA,IAAI,CAACG,UAAU,CAAC,SAAS;oBACjC;gBACF;gBAEA,KAAK,MAAMC,aAAaH,WAAY;oBAClC,IAAIG,UAAUF,IAAI,KAAK,gBAAgB;wBACrC;oBACF;oBAEA,IAAIE,UAAUJ,IAAI,CAACA,IAAI,KAAK,aAAa;wBACvCI,UAAUJ,IAAI,CAACA,IAAI,GAAG;oBACxB;gBACF;YACF;QACF;IACF;AACF"}