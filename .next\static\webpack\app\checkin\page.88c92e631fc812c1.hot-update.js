"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/checkin/page",{

/***/ "(app-pages-browser)/./app/checkin/page.tsx":
/*!******************************!*\
  !*** ./app/checkin/page.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CheckInPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/hooks/useSupabaseData */ \"(app-pages-browser)/./src/hooks/useSupabaseData.tsx\");\n/* harmony import */ var _src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/src/hooks/useCountdown */ \"(app-pages-browser)/./src/hooks/useCountdown.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction CheckInPage() {\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const machineId = searchParams.get(\"machine\");\n    const { laundry, toggleMachineStatus, reserveMachine, isLoading, refreshData } = (0,_src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n    const [machine, setMachine] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [actionResult, setActionResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [customTime, setCustomTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"60\");\n    const [useCustomTime, setUseCustomTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const countdown = (0,_src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(machine === null || machine === void 0 ? void 0 : machine.endAt, machine === null || machine === void 0 ? void 0 : machine.graceEndAt);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CheckInPage.useEffect\": ()=>{\n            if (!machineId) return;\n            const foundMachine = laundry.find({\n                \"CheckInPage.useEffect.foundMachine\": (m)=>m.id === machineId\n            }[\"CheckInPage.useEffect.foundMachine\"]);\n            setMachine(foundMachine || null);\n        }\n    }[\"CheckInPage.useEffect\"], [\n        machineId,\n        laundry\n    ]);\n    // Reduced auto-refresh to every 10 seconds to prevent conflicts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CheckInPage.useEffect\": ()=>{\n            const interval = setInterval({\n                \"CheckInPage.useEffect.interval\": ()=>{\n                    refreshData();\n                }\n            }[\"CheckInPage.useEffect.interval\"], 10000);\n            return ({\n                \"CheckInPage.useEffect\": ()=>clearInterval(interval)\n            })[\"CheckInPage.useEffect\"];\n        }\n    }[\"CheckInPage.useEffect\"], [\n        refreshData\n    ]);\n    const handleToggle = async ()=>{\n        if (!machineId || isProcessing) return;\n        setIsProcessing(true);\n        setActionResult(\"\");\n        let success = false;\n        if ((machine === null || machine === void 0 ? void 0 : machine.status) === \"free\" && useCustomTime) {\n            // Use custom time\n            success = await reserveMachine(machineId, \"\".concat(customTime, \" minutes\"));\n        } else {\n            // Use default toggle\n            success = await toggleMachineStatus(machineId);\n        }\n        if (success) {\n            setActionResult(\"Status updated successfully!\");\n            // Single refresh after 2 seconds to confirm the change\n            setTimeout(()=>refreshData(), 2000);\n        } else {\n            setActionResult(\"Error updating machine status\");\n        }\n        setIsProcessing(false);\n    };\n    const getStatusDisplay = ()=>{\n        if (!machine) return \"\";\n        switch(machine.status){\n            case \"free\":\n                return \"🟢 Available\";\n            case \"running\":\n                const hours = Math.floor(countdown.secondsLeft / 3600);\n                const minutes = Math.floor(countdown.secondsLeft % 3600 / 60);\n                const seconds = countdown.secondsLeft % 60;\n                return \"\\uD83D\\uDD35 In Use - \".concat(hours, \":\").concat(minutes.toString().padStart(2, \"0\"), \":\").concat(seconds.toString().padStart(2, \"0\"), \" left\");\n            case \"finishedGrace\":\n                // Calculate grace period time remaining\n                let graceTimeDisplay = \"5:00\";\n                if (machine.graceEndAt) {\n                    const graceMinutes = Math.floor(countdown.graceSecondsLeft / 60);\n                    const graceSeconds = countdown.graceSecondsLeft % 60;\n                    graceTimeDisplay = \"\".concat(graceMinutes, \":\").concat(graceSeconds.toString().padStart(2, \"0\"));\n                }\n                return \"⚠️ Please collect items - \".concat(graceTimeDisplay, \" left\");\n            default:\n                return \"Unknown\";\n        }\n    };\n    const getButtonText = ()=>{\n        if (isProcessing) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 11\n                    }, this),\n                    \"Processing...\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 96,\n                columnNumber: 9\n            }, this);\n        }\n        switch(machine === null || machine === void 0 ? void 0 : machine.status){\n            case \"free\":\n                return useCustomTime ? \"▶️ Start Using Machine (\".concat(customTime, \" min)\") : \"▶️ Start Using Machine\";\n            case \"running\":\n                return \"⏹️ Stop Using Machine\";\n            case \"finishedGrace\":\n                return \"✅ I've Collected My Items\";\n            default:\n                return \"Update Status\";\n        }\n    };\n    const getButtonColor = ()=>{\n        if (isProcessing) return \"bg-gray-400 cursor-not-allowed\";\n        switch(machine === null || machine === void 0 ? void 0 : machine.status){\n            case \"free\":\n                return \"bg-blue-500 hover:bg-blue-600\";\n            case \"running\":\n                return \"bg-red-500 hover:bg-red-600\";\n            case \"finishedGrace\":\n                return \"bg-green-500 hover:bg-green-600\";\n            default:\n                return \"bg-gray-500\";\n        }\n    };\n    if (isLoading && !machine) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-8 rounded-lg shadow text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-lg\",\n                        children: \"Loading machine data...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 133,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 132,\n            columnNumber: 7\n        }, this);\n    }\n    if (!machineId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-8 rounded-lg shadow text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-600 text-lg mb-4\",\n                        children: \"Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600\",\n                        children: \"No machine ID provided.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 144,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 143,\n            columnNumber: 7\n        }, this);\n    }\n    if (!machine) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-8 rounded-lg shadow text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-600 text-lg mb-4\",\n                        children: \"Machine Not Found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"The requested machine could not be found.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: refreshData,\n                        className: \"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded\",\n                        children: \"Retry Loading\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 155,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 154,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white p-8 rounded-lg shadow text-center max-w-md w-full mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-4\",\n                    children: \"Machine Check-In\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-lg font-medium\",\n                            children: machine.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm mt-1 font-semibold \".concat(machine.status === \"free\" ? \"text-green-600\" : machine.status === \"running\" ? \"text-blue-600\" : machine.status === \"finishedGrace\" ? \"text-orange-600\" : \"text-red-600\", \" \").concat(machine.status === \"finishedGrace\" ? \"animate-pulse\" : \"\"),\n                            children: [\n                                \"Status: \",\n                                getStatusDisplay()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this),\n                        machine.status === \"running\" && machine.endAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500 mt-1\",\n                            children: [\n                                \"Ends at: \",\n                                machine.endAt.toLocaleTimeString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 13\n                        }, this),\n                        machine.status === \"finishedGrace\" && machine.graceEndAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-orange-500 mt-1\",\n                            children: [\n                                \"Grace period ends at: \",\n                                machine.graceEndAt.toLocaleTimeString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 9\n                }, this),\n                machine.status === \"free\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    id: \"useCustomTime\",\n                                    checked: useCustomTime,\n                                    onChange: (e)=>setUseCustomTime(e.target.checked),\n                                    className: \"mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"useCustomTime\",\n                                    className: \"text-sm\",\n                                    children: \"Set custom time\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 13\n                        }, this),\n                        useCustomTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"number\",\n                                    value: customTime,\n                                    onChange: (e)=>setCustomTime(e.target.value),\n                                    min: \"1\",\n                                    max: \"120\",\n                                    className: \"border rounded p-2 w-20 text-center mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"minutes\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleToggle,\n                    disabled: isProcessing,\n                    className: \"w-full p-3 rounded text-white font-medium mb-4 \".concat(getButtonColor()),\n                    children: getButtonText()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: refreshData,\n                    disabled: isLoading,\n                    className: \"w-full p-2 rounded border border-gray-300 text-gray-700 hover:bg-gray-50 mb-4\",\n                    children: \"Refresh Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 9\n                }, this),\n                actionResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm mb-4 p-2 rounded \".concat(actionResult.includes(\"Error\") ? \"bg-red-100 text-red-700\" : \"bg-green-100 text-green-700\"),\n                    children: actionResult\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 11\n                }, this),\n                machine.status === \"finishedGrace\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-orange-50 border border-orange-200 rounded p-3 text-sm text-orange-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium mb-1\",\n                            children: \"⚠️ Grace Period Active\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Please collect your items within the time limit. The machine will become available automatically when the grace period ends.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 168,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n        lineNumber: 167,\n        columnNumber: 5\n    }, this);\n}\n_s(CheckInPage, \"OH9vb5CWT/aUESbsBQ3iKyTpLvI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        _src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    ];\n});\n_c = CheckInPage;\nvar _c;\n$RefreshReg$(_c, \"CheckInPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/checkin/page.tsx\n"));

/***/ })

});