/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/checkin/page";
exports.ids = ["app/checkin/page"];
exports.modules = {

/***/ "(rsc)/./app/checkin/loading.tsx":
/*!*********************************!*\
  !*** ./app/checkin/loading.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Loading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white p-8 rounded-lg shadow text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-lg\",\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\loading.tsx\",\n                lineNumber: 5,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\loading.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\loading.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvY2hlY2tpbi9sb2FkaW5nLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWUsU0FBU0E7SUFDdEIscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNEO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOzBCQUFVOzs7Ozs7Ozs7Ozs7Ozs7O0FBSWpDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG1lbWV0XFxEZXNrdG9wXFxkb3JtXzIxXFxhcHBcXGNoZWNraW5cXGxvYWRpbmcudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExvYWRpbmcoKSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktMTAwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcC04IHJvdW5kZWQtbGcgc2hhZG93IHRleHQtY2VudGVyXCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWxnXCI+TG9hZGluZy4uLjwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvZGl2PlxyXG4gIClcclxufVxyXG4iXSwibmFtZXMiOlsiTG9hZGluZyIsImRpdiIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/checkin/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./app/checkin/page.tsx":
/*!******************************!*\
  !*** ./app/checkin/page.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\dorm_21\\app\\checkin\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"59e27c0181b7\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbWVtZXRcXERlc2t0b3BcXGRvcm1fMjFcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI1OWUyN2MwMTgxYjdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\nconst metadata = {\n    title: 'Dorm Dashboard',\n    description: 'Dorm 21 Management Dashboard',\n    generator: 'v0.dev'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"min-h-screen bg-bgLight text-gray-800 font-sans\",\n            suppressHydrationWarning: true,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"bg-white shadow-sm p-4 border-b border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl font-bold text-primary\",\n                            children: \"\\uD83C\\uDFE0 Dorm Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\layout.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\layout.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\layout.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"container mx-auto py-8\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\layout.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                    className: \"text-center text-sm text-gray-500 py-4 border-t border-gray-200 bg-white\",\n                    children: \"dorm21\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\layout.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fcheckin%2Fpage&page=%2Fcheckin%2Fpage&appPaths=%2Fcheckin%2Fpage&pagePath=private-next-app-dir%2Fcheckin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmemet%5CDesktop%5Cdorm_21%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmemet%5CDesktop%5Cdorm_21&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fcheckin%2Fpage&page=%2Fcheckin%2Fpage&appPaths=%2Fcheckin%2Fpage&pagePath=private-next-app-dir%2Fcheckin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmemet%5CDesktop%5Cdorm_21%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmemet%5CDesktop%5Cdorm_21&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/module.compiled.js?4602\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/checkin/loading.tsx */ \"(rsc)/./app/checkin/loading.tsx\"));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/checkin/page.tsx */ \"(rsc)/./app/checkin/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'checkin',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'loading': [module4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\loading.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/checkin/page\",\n        pathname: \"/checkin\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fcheckin%2Fpage&page=%2Fcheckin%2Fpage&appPaths=%2Fcheckin%2Fpage&pagePath=private-next-app-dir%2Fcheckin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmemet%5CDesktop%5Cdorm_21%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmemet%5CDesktop%5Cdorm_21&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Capp%5C%5Ccheckin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Capp%5C%5Ccheckin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/checkin/page.tsx */ \"(rsc)/./app/checkin/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4yLjRfcmVhY3QtZG9tQDE5LjAuMF9yZWFjdEAxOS4wLjBfX3JlYWN0QDE5LjAuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21lbWV0JTVDJTVDRGVza3RvcCU1QyU1Q2Rvcm1fMjElNUMlNUNhcHAlNUMlNUNjaGVja2luJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdKQUErRiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcbWVtZXRcXFxcRGVza3RvcFxcXFxkb3JtXzIxXFxcXGFwcFxcXFxjaGVja2luXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Capp%5C%5Ccheckin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/checkin/page.tsx":
/*!******************************!*\
  !*** ./app/checkin/page.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CheckInPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var qrcode_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! qrcode.react */ \"(ssr)/./node_modules/.pnpm/qrcode.react@4.2.0_react@19.0.0/node_modules/qrcode.react/lib/esm/index.js\");\n/* harmony import */ var _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/src/hooks/useSupabaseData */ \"(ssr)/./src/hooks/useSupabaseData.tsx\");\n/* harmony import */ var _src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/src/hooks/useCountdown */ \"(ssr)/./src/hooks/useCountdown.tsx\");\n/* harmony import */ var _src_utils_machineOwnership__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/src/utils/machineOwnership */ \"(ssr)/./src/utils/machineOwnership.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction CheckInPage() {\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const machineId = searchParams.get(\"machine\");\n    const { laundry, toggleMachineStatus, reserveMachine, adjustMachineTime, isLoading, refreshData } = (0,_src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    const [machine, setMachine] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [actionResult, setActionResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [customTime, setCustomTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"60\");\n    const [adjustTime, setAdjustTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isAdjusting, setIsAdjusting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [adjustResult, setAdjustResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const countdown = (0,_src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(machine?.endAt, machine?.graceEndAt);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CheckInPage.useEffect\": ()=>{\n            if (!machineId) return;\n            const foundMachine = laundry.find({\n                \"CheckInPage.useEffect.foundMachine\": (m)=>m.id === machineId\n            }[\"CheckInPage.useEffect.foundMachine\"]);\n            setMachine(foundMachine || null);\n        }\n    }[\"CheckInPage.useEffect\"], [\n        machineId,\n        laundry\n    ]);\n    // Reduced auto-refresh to every 10 seconds to prevent conflicts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CheckInPage.useEffect\": ()=>{\n            const interval = setInterval({\n                \"CheckInPage.useEffect.interval\": ()=>{\n                    refreshData();\n                }\n            }[\"CheckInPage.useEffect.interval\"], 10000);\n            return ({\n                \"CheckInPage.useEffect\": ()=>clearInterval(interval)\n            })[\"CheckInPage.useEffect\"];\n        }\n    }[\"CheckInPage.useEffect\"], [\n        refreshData\n    ]);\n    const handleToggle = async ()=>{\n        if (!machineId || isProcessing) return;\n        // Validate time input for free machines\n        if (machine?.status === \"free\") {\n            const timeNum = parseInt(customTime);\n            if (isNaN(timeNum) || timeNum < 15 || timeNum > 180) {\n                setActionResult(\"Please enter a valid time limit\");\n                return;\n            }\n        }\n        setIsProcessing(true);\n        setActionResult(\"\");\n        let success = false;\n        if (machine?.status === \"free\") {\n            // Always use custom time for starting machines\n            success = await reserveMachine(machineId, `${customTime} minutes`);\n        } else {\n            // Use default toggle for stopping/collecting\n            success = await toggleMachineStatus(machineId);\n        }\n        if (success) {\n            setActionResult(\"Status updated successfully!\");\n            // Single refresh after 2 seconds to confirm the change\n            setTimeout(()=>refreshData(), 2000);\n        } else {\n            setActionResult(\"Error updating machine status\");\n        }\n        setIsProcessing(false);\n    };\n    const getStatusDisplay = ()=>{\n        if (!machine) return \"\";\n        switch(machine.status){\n            case \"free\":\n                return \"🟢 Available\";\n            case \"running\":\n                const hours = Math.floor(countdown.secondsLeft / 3600);\n                const minutes = Math.floor(countdown.secondsLeft % 3600 / 60);\n                const seconds = countdown.secondsLeft % 60;\n                return `🔵 In Use - ${hours}:${minutes.toString().padStart(2, \"0\")}:${seconds.toString().padStart(2, \"0\")} left`;\n            case \"finishedGrace\":\n                // Calculate grace period time remaining\n                let graceTimeDisplay = \"5:00\";\n                if (machine.graceEndAt) {\n                    const graceMinutes = Math.floor(countdown.graceSecondsLeft / 60);\n                    const graceSeconds = countdown.graceSecondsLeft % 60;\n                    graceTimeDisplay = `${graceMinutes}:${graceSeconds.toString().padStart(2, \"0\")}`;\n                }\n                return `⚠️ Please collect items - ${graceTimeDisplay} left`;\n            default:\n                return \"Unknown\";\n        }\n    };\n    const getButtonText = ()=>{\n        if (isProcessing) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, this),\n                    \"Processing...\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 108,\n                columnNumber: 9\n            }, this);\n        }\n        switch(machine?.status){\n            case \"free\":\n                return `▶️ Start Using Machine (${customTime} min)`;\n            case \"running\":\n                return \"⏹️ Stop Using Machine\";\n            case \"finishedGrace\":\n                return \"✅ I've Collected My Items\";\n            default:\n                return \"Update Status\";\n        }\n    };\n    const getButtonColor = ()=>{\n        if (isProcessing) return \"bg-gray-400 cursor-not-allowed\";\n        switch(machine?.status){\n            case \"free\":\n                return \"bg-blue-500 hover:bg-blue-600\";\n            case \"running\":\n                return \"bg-red-500 hover:bg-red-600\";\n            case \"finishedGrace\":\n                return \"bg-green-500 hover:bg-green-600\";\n            default:\n                return \"bg-gray-500\";\n        }\n    };\n    const handleBackToHome = ()=>{\n        router.push(\"/\");\n    };\n    const handleAdjustTime = async ()=>{\n        if (!machineId || isAdjusting) return;\n        const minutesNum = parseInt(adjustTime);\n        if (isNaN(minutesNum) || minutesNum < 1 || minutesNum > 120) {\n            setAdjustResult(\"Please enter a number between 1-120 minutes\");\n            return;\n        }\n        setIsAdjusting(true);\n        setAdjustResult(\"\");\n        const result = await adjustMachineTime(machineId, minutesNum);\n        if (result.success) {\n            setAdjustResult(\"Timer updated successfully\");\n            setAdjustTime(\"\");\n            // Refresh data after successful adjustment\n            setTimeout(()=>refreshData(), 1000);\n        } else {\n            setAdjustResult(result.error || \"Failed to update timer\");\n        }\n        setIsAdjusting(false);\n    };\n    // Custom display names for machines (same logic as LaundryCard)\n    const getDisplayName = (machine)=>{\n        if (!machine) return \"\";\n        const isWasher = machine.name.toLowerCase().includes(\"washer\");\n        if (isWasher) {\n            // Extract number from washer name (e.g., \"Washer 1\" -> \"1\")\n            const numberMatch = machine.name.match(/\\d+/);\n            const number = numberMatch ? numberMatch[0] : \"\";\n            return `Washer ${number}`;\n        } else {\n            // For dryers, use \"Dryer 5\" through \"Dryer 8\" based on original number\n            const numberMatch = machine.name.match(/\\d+/);\n            const originalNumber = numberMatch ? Number.parseInt(numberMatch[0]) : 0;\n            const newNumber = originalNumber + 4 // Map 1->5, 2->6, 3->7, 4->8\n            ;\n            return `Dryer ${newNumber}`;\n        }\n    };\n    if (isLoading && !machine) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-8 rounded-lg shadow text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-lg\",\n                        children: \"Getting machine information...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 195,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 194,\n            columnNumber: 7\n        }, this);\n    }\n    if (!machineId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-8 rounded-lg shadow text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-600 text-lg mb-4\",\n                        children: \"Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600\",\n                        children: \"No machine ID provided.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 206,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 205,\n            columnNumber: 7\n        }, this);\n    }\n    if (!machine) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-8 rounded-lg shadow text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-600 text-lg mb-4\",\n                        children: \"Machine Not Found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"The requested machine could not be found.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: refreshData,\n                        className: \"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded\",\n                        children: \"Retry Loading\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 217,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 216,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white p-8 rounded-lg shadow text-center max-w-md w-full mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-4\",\n                    children: \"Machine Check-In\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-600 mb-3\",\n                            children: \"Scan to access this page\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white p-3 rounded-lg border-2 border-gray-200 shadow-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(qrcode_react__WEBPACK_IMPORTED_MODULE_3__.QRCodeSVG, {\n                                    value: `${ false ? 0 : \"\"}/checkin?machine=${machineId}`,\n                                    size: 80,\n                                    fgColor: \"#1A1F36\",\n                                    bgColor: \"#FFFFFF\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-lg font-medium\",\n                            children: getDisplayName(machine)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `text-sm mt-1 font-semibold ${machine.status === \"free\" ? \"text-green-600\" : machine.status === \"running\" ? \"text-blue-600\" : machine.status === \"finishedGrace\" ? \"text-orange-600\" : \"text-red-600\"} ${machine.status === \"finishedGrace\" ? \"animate-pulse\" : \"\"}`,\n                            children: [\n                                \"Status: \",\n                                getStatusDisplay()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, this),\n                        machine.status === \"running\" && machine.endAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500 mt-1\",\n                            children: [\n                                \"Ends at: \",\n                                machine.endAt.toLocaleTimeString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 13\n                        }, this),\n                        machine.status === \"finishedGrace\" && machine.graceEndAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-orange-500 mt-1\",\n                            children: [\n                                \"Grace period ends at: \",\n                                machine.graceEndAt.toLocaleTimeString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 13\n                        }, this),\n                        machine.startedByUserId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-600 mt-2\",\n                            children: (0,_src_utils_machineOwnership__WEBPACK_IMPORTED_MODULE_6__.getOwnershipDisplay)(machine)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 9\n                }, this),\n                machine.status === \"free\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium text-blue-800 mb-2\",\n                            children: \"Set Time Limit\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"number\",\n                                    value: customTime,\n                                    onChange: (e)=>setCustomTime(e.target.value),\n                                    placeholder: \"Enter minutes\",\n                                    className: \"border border-gray-300 rounded-md p-3 w-24 text-center text-lg font-medium focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-600 font-medium\",\n                                    children: \"minutes\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-blue-600 mt-2 text-center\",\n                            children: \"Add a time limit accordingly\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 11\n                }, this),\n                machine.status === \"running\" && (0,_src_utils_machineOwnership__WEBPACK_IMPORTED_MODULE_6__.isCurrentUserOwner)(machine) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: (0,_src_utils_machineOwnership__WEBPACK_IMPORTED_MODULE_6__.canAdjustTime)(machine) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm font-medium text-blue-800 mb-2\",\n                                children: \"Adjust Timer\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-blue-600 mb-3\",\n                                children: [\n                                    \"Current: \",\n                                    machine.endAt ? Math.max(0, Math.ceil((machine.endAt.getTime() - Date.now()) / (1000 * 60))) : 0,\n                                    \" minutes remaining\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"number\",\n                                        value: adjustTime,\n                                        onChange: (e)=>setAdjustTime(e.target.value),\n                                        placeholder: \"Minutes (1-120)\",\n                                        min: \"1\",\n                                        max: \"120\",\n                                        disabled: isAdjusting,\n                                        className: \"flex-1 border rounded p-2 text-center text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleAdjustTime,\n                                        disabled: isAdjusting || !adjustTime,\n                                        className: \"bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1\",\n                                        children: [\n                                            isAdjusting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 border border-white border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 23\n                                            }, this),\n                                            isAdjusting ? \"Updating...\" : \"Update Timer\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 17\n                            }, this),\n                            adjustResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `text-xs p-2 rounded ${adjustResult.includes(\"successfully\") ? \"bg-green-100 text-green-800 border border-green-200\" : \"bg-red-100 text-red-800 border border-red-200\"}`,\n                                children: adjustResult\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 19\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 15\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 p-4 bg-gray-50 border border-gray-200 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm font-medium text-gray-600 mb-2\",\n                                children: \"Timer Adjustment\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-500\",\n                                children: [\n                                    \"Available in \",\n                                    (0,_src_utils_machineOwnership__WEBPACK_IMPORTED_MODULE_6__.getTimeUntilAdjustmentAvailable)(machine),\n                                    \" minutes\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleToggle,\n                    disabled: isProcessing,\n                    className: `w-full p-3 rounded text-white font-medium mb-4 ${getButtonColor()}`,\n                    children: getButtonText()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 356,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleBackToHome,\n                    className: \"w-full p-3 rounded-lg bg-gray-800 hover:bg-gray-900 text-white font-medium mb-4 transition-colors duration-200 shadow-md\",\n                    children: \"← Back to Home\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 365,\n                    columnNumber: 9\n                }, this),\n                actionResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `text-sm mb-4 p-2 rounded ${actionResult.includes(\"Error\") ? \"bg-red-100 text-red-700\" : \"bg-green-100 text-green-700\"}`,\n                    children: actionResult\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 373,\n                    columnNumber: 11\n                }, this),\n                machine.status === \"finishedGrace\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-orange-50 border border-orange-200 rounded p-3 text-sm text-orange-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium mb-1\",\n                            children: \"⚠️ Grace Period Active\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 384,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Please collect your items within the time limit. The machine will become available automatically when the grace period ends.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 383,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 230,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n        lineNumber: 229,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/checkin/page.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \*************************************************************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Capp%5C%5Ccheckin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Capp%5C%5Ccheckin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/checkin/page.tsx */ \"(ssr)/./app/checkin/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4yLjRfcmVhY3QtZG9tQDE5LjAuMF9yZWFjdEAxOS4wLjBfX3JlYWN0QDE5LjAuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21lbWV0JTVDJTVDRGVza3RvcCU1QyU1Q2Rvcm1fMjElNUMlNUNhcHAlNUMlNUNjaGVja2luJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdKQUErRiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcbWVtZXRcXFxcRGVza3RvcFxcXFxkb3JtXzIxXFxcXGFwcFxcXFxjaGVja2luXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Capp%5C%5Ccheckin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/hooks/useCountdown.tsx":
/*!************************************!*\
  !*** ./src/hooks/useCountdown.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useCountdown)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction useCountdown(endAt, graceEndAt) {\n    const [secondsLeft, setSecondsLeft] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const [inGrace, setInGrace] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [graceSecondsLeft, setGraceSecondsLeft] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useCountdown.useEffect\": ()=>{\n            const updateCountdown = {\n                \"useCountdown.useEffect.updateCountdown\": ()=>{\n                    const now = new Date();\n                    if (endAt) {\n                        const timeLeft = Math.max(0, Math.floor((endAt.getTime() - now.getTime()) / 1000));\n                        setSecondsLeft(timeLeft);\n                        // Check if we're in grace period\n                        if (graceEndAt) {\n                            const graceLeft = Math.max(0, Math.floor((graceEndAt.getTime() - now.getTime()) / 1000));\n                            setGraceSecondsLeft(graceLeft);\n                            setInGrace(timeLeft === 0 && graceLeft > 0);\n                        } else if (timeLeft === 0) {\n                            // If no graceEndAt but we're at 0 time left and status is finishedGrace,\n                            // use a default 5 minute grace period\n                            const defaultGraceEnd = new Date(endAt.getTime() + 5 * 60 * 1000);\n                            const graceLeft = Math.max(0, Math.floor((defaultGraceEnd.getTime() - now.getTime()) / 1000));\n                            setGraceSecondsLeft(graceLeft);\n                            setInGrace(graceLeft > 0);\n                        } else {\n                            setInGrace(false);\n                            setGraceSecondsLeft(0);\n                        }\n                    } else {\n                        setSecondsLeft(0);\n                        setInGrace(false);\n                        setGraceSecondsLeft(0);\n                    }\n                }\n            }[\"useCountdown.useEffect.updateCountdown\"];\n            updateCountdown();\n            const interval = setInterval(updateCountdown, 1000);\n            return ({\n                \"useCountdown.useEffect\": ()=>clearInterval(interval)\n            })[\"useCountdown.useEffect\"];\n        }\n    }[\"useCountdown.useEffect\"], [\n        endAt,\n        graceEndAt\n    ]);\n    return {\n        secondsLeft,\n        inGrace,\n        graceSecondsLeft\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useCountdown.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useSupabaseData.tsx":
/*!***************************************!*\
  !*** ./src/hooks/useSupabaseData.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useSupabaseData)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/src/lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n/* harmony import */ var _src_lib_subscriptionManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/src/lib/subscriptionManager */ \"(ssr)/./src/lib/subscriptionManager.ts\");\n/* harmony import */ var _src_lib_machineStatusManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/lib/machineStatusManager */ \"(ssr)/./src/lib/machineStatusManager.ts\");\n/* harmony import */ var _src_utils_parseDuration__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/src/utils/parseDuration */ \"(ssr)/./src/utils/parseDuration.tsx\");\n/* harmony import */ var _src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/src/utils/userIdentification */ \"(ssr)/./src/utils/userIdentification.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n// Helper functions to convert between DB and app formats\nconst dbToMachine = (row)=>{\n    const graceEndAt = row.grace_end_at ? new Date(row.grace_end_at) : row.status === \"finishedGrace\" && row.end_at ? new Date(new Date(row.end_at).getTime() + 5 * 60 * 1000) : undefined;\n    return {\n        id: row.id,\n        name: row.name,\n        status: row.status,\n        startAt: row.start_at ? new Date(row.start_at) : undefined,\n        endAt: row.end_at ? new Date(row.end_at) : undefined,\n        graceEndAt: graceEndAt,\n        updatedAt: new Date(row.updated_at),\n        startedByUserId: row.started_by_user_id || undefined,\n        startedByDeviceFingerprint: row.started_by_device_fingerprint || undefined\n    };\n};\nconst dbToNoise = (row)=>({\n        id: row.id,\n        user: row.user_name,\n        description: row.description || \"Noise reported\",\n        timestamp: new Date(row.timestamp),\n        lastReported: new Date(row.last_reported)\n    });\nconst dbToAnnouncement = (row)=>({\n        id: row.id,\n        user: row.user_name,\n        title: row.title,\n        description: row.description,\n        type: row.announcement_type,\n        timestamp: new Date(row.timestamp)\n    });\nconst dbToSublet = (row)=>({\n        id: row.id,\n        user: row.user_name,\n        duration: row.duration,\n        timestamp: new Date(row.timestamp)\n    });\nconst dbToHelpMe = (row)=>({\n        id: row.id,\n        user: row.user_name,\n        description: row.description,\n        timestamp: new Date(row.timestamp)\n    });\nconst dbToIncident = (row)=>({\n        id: row.id,\n        machineId: row.machine_id,\n        timestamp: new Date(row.timestamp),\n        type: row.incident_type\n    });\nfunction useSupabaseData() {\n    const [laundry, setLaundry] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [noise, setNoise] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [announcements, setAnnouncements] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [helpMe, setHelpMe] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [incidents, setIncidents] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [hasGraceEndColumn, setHasGraceEndColumn] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    // Refs to prevent recursive calls and multiple setups\n    const isLoadingDataRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const lastLoadTimeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const subscriptionSetupRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const statusManagerSetupRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const isMountedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(true);\n    // Debounced data loading function with selective loading\n    const loadAllData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[loadAllData]\": async (specificTable, forceRefresh = false)=>{\n            const now = Date.now();\n            // Skip debounce for manual refresh (forceRefresh = true)\n            if (!forceRefresh && (isLoadingDataRef.current || now - lastLoadTimeRef.current < 1000)) {\n                console.log(\"⏭️ Skipping data load (too recent or in progress)\");\n                return;\n            }\n            if (!isMountedRef.current) {\n                console.log(\"⏭️ Skipping data load (component unmounted)\");\n                return;\n            }\n            isLoadingDataRef.current = true;\n            lastLoadTimeRef.current = now;\n            try {\n                console.log(`🔄 Loading data from Supabase${specificTable ? ` (${specificTable})` : \"\"}...`);\n                setError(null);\n                // If specific table is provided, only load that table\n                if (specificTable) {\n                    await loadSpecificTable(specificTable);\n                } else {\n                    // Load all data\n                    setIsLoading(true);\n                    await loadAllTables();\n                }\n                console.log(\"✅ Data loaded successfully\");\n            } catch (err) {\n                console.error(\"❌ Error loading data:\", err);\n                if (isMountedRef.current) {\n                    setError(err instanceof Error ? err.message : \"Failed to load data\");\n                }\n            } finally{\n                if (isMountedRef.current && !specificTable) {\n                    setIsLoading(false);\n                }\n                isLoadingDataRef.current = false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[loadAllData]\"], []);\n    // Helper function to load specific table\n    const loadSpecificTable = async (table)=>{\n        switch(table){\n            case \"machines\":\n                const machinesResult = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").select(\"*\").order(\"name\");\n                if (machinesResult.error) throw machinesResult.error;\n                setLaundry(machinesResult.data?.map(dbToMachine) || []);\n                break;\n            case \"noise_reports\":\n                const noiseResult = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"noise_reports\").select(\"*\").order(\"timestamp\", {\n                    ascending: false\n                }).limit(50);\n                if (noiseResult.error) throw noiseResult.error;\n                setNoise(noiseResult.data?.map(dbToNoise) || []);\n                break;\n            case \"announcements\":\n                const announcementsResult = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"announcements\").select(\"*\").order(\"timestamp\", {\n                    ascending: false\n                }).limit(50);\n                if (announcementsResult.error) throw announcementsResult.error;\n                setAnnouncements(announcementsResult.data?.map(dbToAnnouncement) || []);\n                break;\n            case \"help_requests\":\n                const helpResult = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"help_requests\").select(\"*\").gte(\"timestamp\", new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()).order(\"timestamp\", {\n                    ascending: false\n                }).limit(50);\n                if (helpResult.error) throw helpResult.error;\n                setHelpMe(helpResult.data?.map(dbToHelpMe) || []);\n                break;\n            case \"incidents\":\n                const incidentsResult = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"incidents\").select(\"*\").order(\"timestamp\", {\n                    ascending: false\n                }).limit(50);\n                if (incidentsResult.error) throw incidentsResult.error;\n                setIncidents(incidentsResult.data?.map(dbToIncident) || []);\n                break;\n        }\n    };\n    // Helper function to load all tables\n    const loadAllTables = async ()=>{\n        const timeoutPromise = new Promise((_, reject)=>setTimeout(()=>reject(new Error(\"Request timeout\")), 15000));\n        const dataPromise = Promise.all([\n            _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").select(\"*\").order(\"name\"),\n            _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"noise_reports\").select(\"*\").order(\"timestamp\", {\n                ascending: false\n            }).limit(50),\n            _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"announcements\").select(\"*\").order(\"timestamp\", {\n                ascending: false\n            }).limit(50),\n            _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"help_requests\").select(\"*\").gte(\"timestamp\", new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()).order(\"timestamp\", {\n                ascending: false\n            }).limit(50),\n            _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"incidents\").select(\"*\").order(\"timestamp\", {\n                ascending: false\n            }).limit(50)\n        ]);\n        const [machinesResult, noiseResult, announcementsResult, helpResult, incidentsResult] = await Promise.race([\n            dataPromise,\n            timeoutPromise\n        ]);\n        if (!isMountedRef.current) return;\n        if (machinesResult.error) throw new Error(`Failed to load machines: ${machinesResult.error.message}`);\n        if (noiseResult.error) throw new Error(`Failed to load noise reports: ${noiseResult.error.message}`);\n        if (announcementsResult.error) throw new Error(`Failed to load announcements: ${announcementsResult.error.message}`);\n        if (helpResult.error) throw new Error(`Failed to load help requests: ${helpResult.error.message}`);\n        if (incidentsResult.error) throw new Error(`Failed to load incidents: ${incidentsResult.error.message}`);\n        setLaundry(machinesResult.data?.map(dbToMachine) || []);\n        setNoise(noiseResult.data?.map(dbToNoise) || []);\n        setAnnouncements(announcementsResult.data?.map(dbToAnnouncement) || []);\n        setHelpMe(helpResult.data?.map(dbToHelpMe) || []);\n        setIncidents(incidentsResult.data?.map(dbToIncident) || []);\n    };\n    // Set up real-time subscriptions with table-specific updates\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseData.useEffect\": ()=>{\n            if (subscriptionSetupRef.current) {\n                return;\n            }\n            subscriptionSetupRef.current = true;\n            console.log(\"🔄 Setting up subscription manager...\");\n            const subscriptionManager = _src_lib_subscriptionManager__WEBPACK_IMPORTED_MODULE_2__[\"default\"].getInstance();\n            // Enhanced callback that handles specific table updates\n            const handleRealtimeUpdate = {\n                \"useSupabaseData.useEffect.handleRealtimeUpdate\": (table, event)=>{\n                    if (table && table !== \"polling\") {\n                        // Load only the specific table that changed for faster updates\n                        loadAllData(table);\n                    } else {\n                        // Fallback to loading all data\n                        loadAllData();\n                    }\n                }\n            }[\"useSupabaseData.useEffect.handleRealtimeUpdate\"];\n            subscriptionManager.addCallback(handleRealtimeUpdate);\n            return ({\n                \"useSupabaseData.useEffect\": ()=>{\n                    console.log(\"🧹 Cleaning up subscription manager...\");\n                    subscriptionManager.removeCallback(handleRealtimeUpdate);\n                    subscriptionSetupRef.current = false;\n                }\n            })[\"useSupabaseData.useEffect\"];\n        }\n    }[\"useSupabaseData.useEffect\"], [\n        loadAllData\n    ]);\n    // Set up machine status monitoring (only once)\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseData.useEffect\": ()=>{\n            if (statusManagerSetupRef.current) {\n                return;\n            }\n            statusManagerSetupRef.current = true;\n            console.log(\"🔄 Setting up machine status manager...\");\n            const statusManager = _src_lib_machineStatusManager__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getInstance();\n            statusManager.startStatusMonitoring();\n            return ({\n                \"useSupabaseData.useEffect\": ()=>{\n                    console.log(\"🧹 Cleaning up machine status manager...\");\n                    statusManager.stopStatusMonitoring();\n                    statusManagerSetupRef.current = false;\n                }\n            })[\"useSupabaseData.useEffect\"];\n        }\n    }[\"useSupabaseData.useEffect\"], []);\n    // Initial data load and cleanup\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseData.useEffect\": ()=>{\n            isMountedRef.current = true;\n            loadAllData();\n            return ({\n                \"useSupabaseData.useEffect\": ()=>{\n                    isMountedRef.current = false;\n                }\n            })[\"useSupabaseData.useEffect\"];\n        }\n    }[\"useSupabaseData.useEffect\"], []) // Only run once on mount\n    ;\n    // Machine operations with optimistic updates\n    const toggleMachineStatus = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[toggleMachineStatus]\": async (id)=>{\n            try {\n                const machine = laundry.find({\n                    \"useSupabaseData.useCallback[toggleMachineStatus].machine\": (m)=>m.id === id\n                }[\"useSupabaseData.useCallback[toggleMachineStatus].machine\"]);\n                if (!machine) {\n                    return false;\n                }\n                let optimisticUpdate;\n                let updateData;\n                if (machine.status === \"free\") {\n                    const startAt = new Date();\n                    const endAt = new Date(startAt.getTime() + 60 * 60 * 1000);\n                    const currentUserId = (0,_src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_5__.getDeviceUserId)();\n                    optimisticUpdate = {\n                        ...machine,\n                        status: \"running\",\n                        startAt,\n                        endAt,\n                        graceEndAt: undefined,\n                        updatedAt: new Date(),\n                        startedByUserId: currentUserId,\n                        startedByDeviceFingerprint: currentUserId\n                    };\n                    updateData = {\n                        status: \"running\",\n                        start_at: startAt.toISOString(),\n                        end_at: endAt.toISOString(),\n                        started_by_user_id: currentUserId,\n                        started_by_device_fingerprint: currentUserId\n                    };\n                    if (hasGraceEndColumn) {\n                        updateData.grace_end_at = null;\n                    }\n                } else {\n                    optimisticUpdate = {\n                        ...machine,\n                        status: \"free\",\n                        startAt: undefined,\n                        endAt: undefined,\n                        graceEndAt: undefined,\n                        updatedAt: new Date(),\n                        startedByUserId: undefined,\n                        startedByDeviceFingerprint: undefined\n                    };\n                    updateData = {\n                        status: \"free\",\n                        start_at: null,\n                        end_at: null,\n                        started_by_user_id: null,\n                        started_by_device_fingerprint: null\n                    };\n                    if (hasGraceEndColumn) {\n                        updateData.grace_end_at = null;\n                    }\n                }\n                // Apply optimistic update\n                setLaundry({\n                    \"useSupabaseData.useCallback[toggleMachineStatus]\": (prev)=>prev.map({\n                            \"useSupabaseData.useCallback[toggleMachineStatus]\": (m)=>m.id === id ? optimisticUpdate : m\n                        }[\"useSupabaseData.useCallback[toggleMachineStatus]\"])\n                }[\"useSupabaseData.useCallback[toggleMachineStatus]\"]);\n                // Send update to database\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").update(updateData).eq(\"id\", id);\n                if (error) {\n                    if (error.message && error.message.includes(\"grace_end_at\")) {\n                        setHasGraceEndColumn(false);\n                        delete updateData.grace_end_at;\n                        const { error: retryError } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").update(updateData).eq(\"id\", id);\n                        if (retryError) {\n                            setLaundry({\n                                \"useSupabaseData.useCallback[toggleMachineStatus]\": (prev)=>prev.map({\n                                        \"useSupabaseData.useCallback[toggleMachineStatus]\": (m)=>m.id === id ? machine : m\n                                    }[\"useSupabaseData.useCallback[toggleMachineStatus]\"])\n                            }[\"useSupabaseData.useCallback[toggleMachineStatus]\"]);\n                            throw retryError;\n                        }\n                    } else {\n                        setLaundry({\n                            \"useSupabaseData.useCallback[toggleMachineStatus]\": (prev)=>prev.map({\n                                    \"useSupabaseData.useCallback[toggleMachineStatus]\": (m)=>m.id === id ? machine : m\n                                }[\"useSupabaseData.useCallback[toggleMachineStatus]\"])\n                        }[\"useSupabaseData.useCallback[toggleMachineStatus]\"]);\n                        throw error;\n                    }\n                }\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to update machine\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[toggleMachineStatus]\"], [\n        laundry,\n        hasGraceEndColumn\n    ]);\n    const reserveMachine = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[reserveMachine]\": async (id, durationStr)=>{\n            try {\n                const machine = laundry.find({\n                    \"useSupabaseData.useCallback[reserveMachine].machine\": (m)=>m.id === id\n                }[\"useSupabaseData.useCallback[reserveMachine].machine\"]);\n                if (!machine) {\n                    return false;\n                }\n                const durationSeconds = (0,_src_utils_parseDuration__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(durationStr);\n                const startAt = new Date();\n                const endAt = new Date(startAt.getTime() + durationSeconds * 1000);\n                const currentUserId = (0,_src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_5__.getDeviceUserId)();\n                const optimisticUpdate = {\n                    ...machine,\n                    status: \"running\",\n                    startAt,\n                    endAt,\n                    graceEndAt: undefined,\n                    updatedAt: new Date(),\n                    startedByUserId: currentUserId,\n                    startedByDeviceFingerprint: currentUserId\n                };\n                setLaundry({\n                    \"useSupabaseData.useCallback[reserveMachine]\": (prev)=>prev.map({\n                            \"useSupabaseData.useCallback[reserveMachine]\": (m)=>m.id === id ? optimisticUpdate : m\n                        }[\"useSupabaseData.useCallback[reserveMachine]\"])\n                }[\"useSupabaseData.useCallback[reserveMachine]\"]);\n                const updateData = {\n                    status: \"running\",\n                    start_at: startAt.toISOString(),\n                    end_at: endAt.toISOString(),\n                    started_by_user_id: currentUserId,\n                    started_by_device_fingerprint: currentUserId\n                };\n                if (hasGraceEndColumn) {\n                    updateData.grace_end_at = null;\n                }\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").update(updateData).eq(\"id\", id);\n                if (error) {\n                    if (error.message && error.message.includes(\"grace_end_at\")) {\n                        setHasGraceEndColumn(false);\n                        delete updateData.grace_end_at;\n                        const { error: retryError } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").update(updateData).eq(\"id\", id);\n                        if (retryError) {\n                            setLaundry({\n                                \"useSupabaseData.useCallback[reserveMachine]\": (prev)=>prev.map({\n                                        \"useSupabaseData.useCallback[reserveMachine]\": (m)=>m.id === id ? machine : m\n                                    }[\"useSupabaseData.useCallback[reserveMachine]\"])\n                            }[\"useSupabaseData.useCallback[reserveMachine]\"]);\n                            throw retryError;\n                        }\n                    } else {\n                        setLaundry({\n                            \"useSupabaseData.useCallback[reserveMachine]\": (prev)=>prev.map({\n                                    \"useSupabaseData.useCallback[reserveMachine]\": (m)=>m.id === id ? machine : m\n                                }[\"useSupabaseData.useCallback[reserveMachine]\"])\n                        }[\"useSupabaseData.useCallback[reserveMachine]\"]);\n                        throw error;\n                    }\n                }\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to reserve machine\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[reserveMachine]\"], [\n        laundry,\n        hasGraceEndColumn\n    ]);\n    // New function to adjust machine time (only for machines you started)\n    const adjustMachineTime = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[adjustMachineTime]\": async (id, newMinutes)=>{\n            try {\n                const machine = laundry.find({\n                    \"useSupabaseData.useCallback[adjustMachineTime].machine\": (m)=>m.id === id\n                }[\"useSupabaseData.useCallback[adjustMachineTime].machine\"]);\n                if (!machine) {\n                    return {\n                        success: false,\n                        error: \"Machine not found\"\n                    };\n                }\n                const currentUserId = (0,_src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_5__.getDeviceUserId)();\n                // Check ownership - only the user who started the machine can adjust it\n                if (machine.startedByUserId !== currentUserId) {\n                    return {\n                        success: false,\n                        error: \"You can only adjust machines you started\"\n                    };\n                }\n                // Validate time range (1-120 minutes for adjustment)\n                if (newMinutes < 1 || newMinutes > 120) {\n                    return {\n                        success: false,\n                        error: \"Please enter a number between 1-120 minutes\"\n                    };\n                }\n                // Calculate new end time\n                const now = new Date();\n                const newEndAt = new Date(now.getTime() + newMinutes * 60 * 1000);\n                const optimisticUpdate = {\n                    ...machine,\n                    endAt: newEndAt,\n                    updatedAt: new Date()\n                };\n                // Apply optimistic update\n                setLaundry({\n                    \"useSupabaseData.useCallback[adjustMachineTime]\": (prev)=>prev.map({\n                            \"useSupabaseData.useCallback[adjustMachineTime]\": (m)=>m.id === id ? optimisticUpdate : m\n                        }[\"useSupabaseData.useCallback[adjustMachineTime]\"])\n                }[\"useSupabaseData.useCallback[adjustMachineTime]\"]);\n                // Send update to database\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").update({\n                    end_at: newEndAt.toISOString()\n                }).eq(\"id\", id);\n                if (error) {\n                    // Revert optimistic update on error\n                    setLaundry({\n                        \"useSupabaseData.useCallback[adjustMachineTime]\": (prev)=>prev.map({\n                                \"useSupabaseData.useCallback[adjustMachineTime]\": (m)=>m.id === id ? machine : m\n                            }[\"useSupabaseData.useCallback[adjustMachineTime]\"])\n                    }[\"useSupabaseData.useCallback[adjustMachineTime]\"]);\n                    return {\n                        success: false,\n                        error: \"Unable to update timer. Please try again.\"\n                    };\n                }\n                return {\n                    success: true,\n                    error: null\n                };\n            } catch (err) {\n                return {\n                    success: false,\n                    error: err instanceof Error ? err.message : \"Unable to update timer. Please try again.\"\n                };\n            }\n        }\n    }[\"useSupabaseData.useCallback[adjustMachineTime]\"], [\n        laundry\n    ]);\n    // Simplified CRUD operations\n    const addNoiseWithDescription = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[addNoiseWithDescription]\": async (user, description)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"noise_reports\").insert({\n                    id: Date.now().toString(),\n                    user_name: user,\n                    description: description\n                });\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to add noise report\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[addNoiseWithDescription]\"], []);\n    const deleteNoise = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[deleteNoise]\": async (id)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"noise_reports\").delete().eq(\"id\", id);\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to delete noise report\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[deleteNoise]\"], []);\n    const addAnnouncement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[addAnnouncement]\": async (user, title, description, type)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"announcements\").insert({\n                    id: Date.now().toString(),\n                    user_name: user,\n                    title: title,\n                    description: description,\n                    announcement_type: type\n                });\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to add announcement\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[addAnnouncement]\"], []);\n    const deleteAnnouncement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[deleteAnnouncement]\": async (id)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"announcements\").delete().eq(\"id\", id);\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to delete announcement\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[deleteAnnouncement]\"], []);\n    const addHelpRequest = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[addHelpRequest]\": async (user, description)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"help_requests\").insert({\n                    id: Date.now().toString(),\n                    user_name: user,\n                    description\n                });\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to add help request\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[addHelpRequest]\"], []);\n    const deleteHelpRequest = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[deleteHelpRequest]\": async (id)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"help_requests\").delete().eq(\"id\", id);\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to delete help request\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[deleteHelpRequest]\"], []);\n    const deleteIncident = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[deleteIncident]\": async (id)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"incidents\").delete().eq(\"id\", id);\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to delete incident\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[deleteIncident]\"], []);\n    return {\n        // Data\n        laundry,\n        noise,\n        announcements,\n        helpMe,\n        incidents,\n        // State\n        isLoading,\n        error,\n        // Operations\n        toggleMachineStatus,\n        reserveMachine,\n        adjustMachineTime,\n        addNoiseWithDescription,\n        deleteNoise,\n        addAnnouncement,\n        deleteAnnouncement,\n        addHelpRequest,\n        deleteHelpRequest,\n        deleteIncident,\n        refreshData: ()=>loadAllData(undefined, true)\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useSupabaseData.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/machineStatusManager.ts":
/*!*****************************************!*\
  !*** ./src/lib/machineStatusManager.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(ssr)/./src/lib/supabase.ts\");\n\nclass MachineStatusManager {\n    static getInstance() {\n        if (!MachineStatusManager.instance) {\n            MachineStatusManager.instance = new MachineStatusManager();\n        }\n        return MachineStatusManager.instance;\n    }\n    startStatusMonitoring() {\n        if (this.statusCheckInterval || this.isDestroyed) {\n            return;\n        }\n        console.log(\"🔄 Starting machine status monitoring...\");\n        // Check every 30 seconds (less aggressive)\n        this.statusCheckInterval = setInterval(()=>{\n            if (!this.isDestroyed) {\n                this.checkMachineStatuses();\n            }\n        }, 30000);\n        // Also check immediately\n        this.checkMachineStatuses();\n    }\n    stopStatusMonitoring() {\n        if (this.statusCheckInterval) {\n            clearInterval(this.statusCheckInterval);\n            this.statusCheckInterval = null;\n            console.log(\"⏹️ Stopped machine status monitoring\");\n        }\n    }\n    destroy() {\n        this.isDestroyed = true;\n        this.stopStatusMonitoring();\n    }\n    async checkMachineStatuses() {\n        if (this.isChecking || this.isDestroyed) {\n            return;\n        }\n        this.isChecking = true;\n        try {\n            const now = new Date();\n            const { data: machines, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"machines\").select(\"*\").in(\"status\", [\n                \"running\",\n                \"finishedGrace\"\n            ]);\n            if (error) {\n                console.error(\"❌ Error fetching machines for status check:\", error);\n                return;\n            }\n            if (!machines || machines.length === 0) {\n                return;\n            }\n            const updates = [];\n            for (const machine of machines){\n                if (this.isDestroyed) break;\n                const endAt = machine.end_at ? new Date(machine.end_at) : null;\n                let graceEndAt = null;\n                if (this.hasGraceEndColumn && machine.grace_end_at) {\n                    graceEndAt = new Date(machine.grace_end_at);\n                }\n                if (machine.status === \"running\" && endAt && now >= endAt) {\n                    const graceEnd = new Date(now.getTime() + 5 * 60 * 1000);\n                    const updateObj = {\n                        status: \"finishedGrace\"\n                    };\n                    if (this.hasGraceEndColumn) {\n                        updateObj.grace_end_at = graceEnd.toISOString();\n                    }\n                    updates.push({\n                        id: machine.id,\n                        updates: updateObj\n                    });\n                    console.log(`⚠️ Machine ${machine.name} transitioning to grace period`);\n                } else if (machine.status === \"finishedGrace\") {\n                    const defaultGraceEnd = endAt ? new Date(endAt.getTime() + 5 * 60 * 1000) : null;\n                    const effectiveGraceEnd = graceEndAt || defaultGraceEnd;\n                    if (effectiveGraceEnd && now >= effectiveGraceEnd) {\n                        updates.push({\n                            id: machine.id,\n                            updates: {\n                                status: \"free\",\n                                start_at: null,\n                                end_at: null,\n                                ...this.hasGraceEndColumn ? {\n                                    grace_end_at: null\n                                } : {}\n                            }\n                        });\n                        console.log(`✅ Machine ${machine.name} grace period ended, now available`);\n                    }\n                }\n            }\n            // Apply updates sequentially to avoid conflicts\n            for (const update of updates){\n                if (this.isDestroyed) break;\n                try {\n                    const { error: updateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"machines\").update(update.updates).eq(\"id\", update.id);\n                    if (updateError) {\n                        if (updateError.message && updateError.message.includes(\"grace_end_at\")) {\n                            console.log(\"⚠️ grace_end_at column not found, disabling this feature\");\n                            this.hasGraceEndColumn = false;\n                        }\n                        console.error(`❌ Error updating machine ${update.id}:`, updateError);\n                    }\n                } catch (err) {\n                    console.error(`❌ Error updating machine ${update.id}:`, err);\n                }\n                // Small delay between updates to prevent overwhelming the database\n                await new Promise((resolve)=>setTimeout(resolve, 100));\n            }\n            if (updates.length > 0) {\n                console.log(`🔄 Updated ${updates.length} machine statuses`);\n            }\n        } catch (error) {\n            console.error(\"❌ Error in machine status check:\", error);\n        } finally{\n            this.isChecking = false;\n        }\n    }\n    async triggerStatusCheck() {\n        if (!this.isDestroyed) {\n            await this.checkMachineStatuses();\n        }\n    }\n    constructor(){\n        this.statusCheckInterval = null;\n        this.isChecking = false;\n        this.hasGraceEndColumn = true;\n        this.isDestroyed = false;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MachineStatusManager);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/machineStatusManager.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/subscriptionManager.ts":
/*!****************************************!*\
  !*** ./src/lib/subscriptionManager.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(ssr)/./src/lib/supabase.ts\");\n\nclass SubscriptionManager {\n    static getInstance() {\n        if (!SubscriptionManager.instance) {\n            SubscriptionManager.instance = new SubscriptionManager();\n        }\n        return SubscriptionManager.instance;\n    }\n    addCallback(callback) {\n        if (this.isDestroyed) return;\n        this.callbacks.add(callback);\n        console.log(`📡 Added callback, total: ${this.callbacks.size}`);\n        if (this.callbacks.size === 1 && !this.isSubscribed) {\n            this.initializeSubscription();\n        }\n    }\n    removeCallback(callback) {\n        this.callbacks.delete(callback);\n        console.log(`📡 Removed callback, total: ${this.callbacks.size}`);\n        if (this.callbacks.size === 0) {\n            this.cleanup();\n        }\n    }\n    async initializeSubscription() {\n        if (this.isSubscribed || this.channel || this.isDestroyed) {\n            return;\n        }\n        console.log(\"🔄 Initializing Supabase subscription...\");\n        this.isSubscribed = true;\n        this.subscriptionId++;\n        try {\n            const channelName = `dorm-dashboard-${this.subscriptionId}-${Date.now()}`;\n            this.channel = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.channel(channelName, {\n                config: {\n                    broadcast: {\n                        self: false\n                    },\n                    presence: {\n                        key: \"\"\n                    }\n                }\n            }).on(\"postgres_changes\", {\n                event: \"*\",\n                schema: \"public\",\n                table: \"machines\"\n            }, (payload)=>{\n                this.handleChange(\"machines\", payload.eventType);\n            }).on(\"postgres_changes\", {\n                event: \"*\",\n                schema: \"public\",\n                table: \"noise_reports\"\n            }, (payload)=>{\n                this.handleChange(\"noise_reports\", payload.eventType);\n            }).on(\"postgres_changes\", {\n                event: \"*\",\n                schema: \"public\",\n                table: \"announcements\"\n            }, (payload)=>{\n                this.handleChange(\"announcements\", payload.eventType);\n            }).on(\"postgres_changes\", {\n                event: \"*\",\n                schema: \"public\",\n                table: \"help_requests\"\n            }, (payload)=>{\n                this.handleChange(\"help_requests\", payload.eventType);\n            }).on(\"postgres_changes\", {\n                event: \"*\",\n                schema: \"public\",\n                table: \"incidents\"\n            }, (payload)=>{\n                this.handleChange(\"incidents\", payload.eventType);\n            }).subscribe((status)=>{\n                console.log(`📡 Subscription status: ${status}`);\n                if (status === \"SUBSCRIBED\") {\n                    console.log(\"✅ Successfully subscribed to realtime updates\");\n                } else if (status === \"CHANNEL_ERROR\" || status === \"TIMED_OUT\" || status === \"CLOSED\") {\n                    console.log(\"❌ Subscription error, will retry...\");\n                    this.handleSubscriptionError();\n                }\n            });\n            // Reduced polling frequency\n            this.startPolling();\n        } catch (error) {\n            console.error(\"❌ Error initializing subscription:\", error);\n            this.handleSubscriptionError();\n        }\n    }\n    notifyCallbacks(table, event) {\n        if (this.isDestroyed) return;\n        // Use setTimeout to prevent stack overflow and allow for immediate UI updates\n        // Further reduced delay for even faster real-time updates\n        setTimeout(()=>{\n            if (this.isDestroyed) return;\n            this.callbacks.forEach((callback)=>{\n                try {\n                    callback(table, event);\n                } catch (error) {\n                    console.error(\"❌ Error in subscription callback:\", error);\n                }\n            });\n        }, 10) // Reduced from 50ms to 10ms for faster propagation\n        ;\n    }\n    startPolling() {\n        if (this.pollingInterval || this.isDestroyed) {\n            return;\n        }\n        // Polling every 60 seconds as fallback\n        this.pollingInterval = setInterval(()=>{\n            if (this.isDestroyed) return;\n            console.log(\"🔄 Polling for changes...\");\n            this.notifyCallbacks(\"polling\", \"fallback\");\n        }, 60000);\n    }\n    handleSubscriptionError() {\n        if (this.isDestroyed) return;\n        this.cleanup();\n        setTimeout(()=>{\n            if (this.callbacks.size > 0 && !this.isDestroyed) {\n                console.log(\"🔄 Retrying subscription...\");\n                this.initializeSubscription();\n            }\n        }, 3000);\n    }\n    cleanup() {\n        console.log(\"🧹 Cleaning up subscription manager...\");\n        this.isSubscribed = false;\n        if (this.channel) {\n            try {\n                this.channel.unsubscribe();\n                _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.removeChannel(this.channel);\n            } catch (error) {\n                console.warn(\"⚠️ Error during channel cleanup:\", error);\n            }\n            this.channel = null;\n        }\n        if (this.pollingInterval) {\n            clearInterval(this.pollingInterval);\n            this.pollingInterval = null;\n        }\n    }\n    forceCleanup() {\n        console.log(\"🧹 Force cleaning up subscription manager...\");\n        this.isDestroyed = true;\n        this.callbacks.clear();\n        this.cleanup();\n    }\n    triggerSync() {\n        if (this.isDestroyed) return;\n        this.notifyCallbacks(\"manual\", \"trigger\");\n    }\n    constructor(){\n        this.channel = null;\n        this.callbacks = new Set();\n        this.isSubscribed = false;\n        this.pollingInterval = null;\n        this.isDestroyed = false;\n        this.subscriptionId = 0;\n        this.lastChangeTime = 0;\n        this.handleChange = (table, event)=>{\n            if (this.isDestroyed) return;\n            const now = Date.now();\n            // Faster debouncing for better real-time experience\n            // Machines get priority with 100ms debounce, others use 300ms\n            const debounceTime = table === 'machines' ? 100 : 300;\n            if (now - this.lastChangeTime < debounceTime) {\n                return;\n            }\n            this.lastChangeTime = now;\n            console.log(`📡 Database change detected: ${table} - ${event}`);\n            this.notifyCallbacks(table, event);\n        };\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SubscriptionManager);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/subscriptionManager.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   testSupabaseConnection: () => (/* binding */ testSupabaseConnection)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.0/node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://aodjyjxsqfytythosrka.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFvZGp5anhzcWZ5dHl0aG9zcmthIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1MDYzOTYsImV4cCI6MjA2NTA4MjM5Nn0.V0K-uTAl8FDkupkjYZn9R6P4qIMhJ0kX1iE4LQFr_tg\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n    realtime: {\n        params: {\n            eventsPerSecond: 50\n        }\n    },\n    auth: {\n        persistSession: false\n    }\n});\n// Test connection function\nasync function testSupabaseConnection() {\n    try {\n        const { data, error } = await supabase.from(\"machines\").select(\"count\").single();\n        if (error) throw error;\n        return {\n            success: true,\n            data\n        };\n    } catch (error) {\n        return {\n            success: false,\n            error\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3N1cGFiYXNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFvRDtBQUVwRCxNQUFNQyxjQUFjO0FBQ3BCLE1BQU1DLGtCQUNKO0FBRUssTUFBTUMsV0FBV0gsbUVBQVlBLENBQUNDLGFBQWFDLGlCQUFpQjtJQUNqRUUsVUFBVTtRQUNSQyxRQUFRO1lBQ05DLGlCQUFpQjtRQUNuQjtJQUNGO0lBQ0FDLE1BQU07UUFDSkMsZ0JBQWdCO0lBQ2xCO0FBQ0YsR0FBRTtBQUVGLDJCQUEyQjtBQUNwQixlQUFlQztJQUNwQixJQUFJO1FBQ0YsTUFBTSxFQUFFQyxJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1SLFNBQVNTLElBQUksQ0FBQyxZQUFZQyxNQUFNLENBQUMsU0FBU0MsTUFBTTtRQUM5RSxJQUFJSCxPQUFPLE1BQU1BO1FBQ2pCLE9BQU87WUFBRUksU0FBUztZQUFNTDtRQUFLO0lBQy9CLEVBQUUsT0FBT0MsT0FBTztRQUNkLE9BQU87WUFBRUksU0FBUztZQUFPSjtRQUFNO0lBQ2pDO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbWVtZXRcXERlc2t0b3BcXGRvcm1fMjFcXHNyY1xcbGliXFxzdXBhYmFzZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVDbGllbnQgfSBmcm9tIFwiQHN1cGFiYXNlL3N1cGFiYXNlLWpzXCJcclxuXHJcbmNvbnN0IHN1cGFiYXNlVXJsID0gXCJodHRwczovL2FvZGp5anhzcWZ5dHl0aG9zcmthLnN1cGFiYXNlLmNvXCJcclxuY29uc3Qgc3VwYWJhc2VBbm9uS2V5ID1cclxuICBcImV5SmhiR2NpT2lKSVV6STFOaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpwYzNNaU9pSnpkWEJoWW1GelpTSXNJbkpsWmlJNkltRnZaR3A1YW5oemNXWjVkSGwwYUc5emNtdGhJaXdpY205c1pTSTZJbUZ1YjI0aUxDSnBZWFFpT2pFM05EazFNRFl6T1RZc0ltVjRjQ0k2TWpBMk5UQTRNak01Tm4wLlYwSy11VEFsOEZEa3Vwa2pZWm45UjZQNHFJTWhKMGtYMWlFNExRRnJfdGdcIlxyXG5cclxuZXhwb3J0IGNvbnN0IHN1cGFiYXNlID0gY3JlYXRlQ2xpZW50KHN1cGFiYXNlVXJsLCBzdXBhYmFzZUFub25LZXksIHtcclxuICByZWFsdGltZToge1xyXG4gICAgcGFyYW1zOiB7XHJcbiAgICAgIGV2ZW50c1BlclNlY29uZDogNTAsXHJcbiAgICB9LFxyXG4gIH0sXHJcbiAgYXV0aDoge1xyXG4gICAgcGVyc2lzdFNlc3Npb246IGZhbHNlLFxyXG4gIH0sXHJcbn0pXHJcblxyXG4vLyBUZXN0IGNvbm5lY3Rpb24gZnVuY3Rpb25cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHRlc3RTdXBhYmFzZUNvbm5lY3Rpb24oKSB7XHJcbiAgdHJ5IHtcclxuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlLmZyb20oXCJtYWNoaW5lc1wiKS5zZWxlY3QoXCJjb3VudFwiKS5zaW5nbGUoKVxyXG4gICAgaWYgKGVycm9yKSB0aHJvdyBlcnJvclxyXG4gICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSwgZGF0YSB9XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvciB9XHJcbiAgfVxyXG59XHJcblxyXG4vLyBEYXRhYmFzZSB0eXBlc1xyXG5leHBvcnQgaW50ZXJmYWNlIERhdGFiYXNlIHtcclxuICBwdWJsaWM6IHtcclxuICAgIFRhYmxlczoge1xyXG4gICAgICBtYWNoaW5lczoge1xyXG4gICAgICAgIFJvdzoge1xyXG4gICAgICAgICAgaWQ6IHN0cmluZ1xyXG4gICAgICAgICAgbmFtZTogc3RyaW5nXHJcbiAgICAgICAgICBzdGF0dXM6IFwiZnJlZVwiIHwgXCJydW5uaW5nXCIgfCBcImZpbmlzaGVkR3JhY2VcIlxyXG4gICAgICAgICAgc3RhcnRfYXQ6IHN0cmluZyB8IG51bGxcclxuICAgICAgICAgIGVuZF9hdDogc3RyaW5nIHwgbnVsbFxyXG4gICAgICAgICAgZ3JhY2VfZW5kX2F0OiBzdHJpbmcgfCBudWxsXHJcbiAgICAgICAgICB1cGRhdGVkX2F0OiBzdHJpbmdcclxuICAgICAgICB9XHJcbiAgICAgICAgSW5zZXJ0OiB7XHJcbiAgICAgICAgICBpZDogc3RyaW5nXHJcbiAgICAgICAgICBuYW1lOiBzdHJpbmdcclxuICAgICAgICAgIHN0YXR1cz86IFwiZnJlZVwiIHwgXCJydW5uaW5nXCIgfCBcImZpbmlzaGVkR3JhY2VcIlxyXG4gICAgICAgICAgc3RhcnRfYXQ/OiBzdHJpbmcgfCBudWxsXHJcbiAgICAgICAgICBlbmRfYXQ/OiBzdHJpbmcgfCBudWxsXHJcbiAgICAgICAgICBncmFjZV9lbmRfYXQ/OiBzdHJpbmcgfCBudWxsXHJcbiAgICAgICAgfVxyXG4gICAgICAgIFVwZGF0ZToge1xyXG4gICAgICAgICAgaWQ/OiBzdHJpbmdcclxuICAgICAgICAgIG5hbWU/OiBzdHJpbmdcclxuICAgICAgICAgIHN0YXR1cz86IFwiZnJlZVwiIHwgXCJydW5uaW5nXCIgfCBcImZpbmlzaGVkR3JhY2VcIlxyXG4gICAgICAgICAgc3RhcnRfYXQ/OiBzdHJpbmcgfCBudWxsXHJcbiAgICAgICAgICBlbmRfYXQ/OiBzdHJpbmcgfCBudWxsXHJcbiAgICAgICAgICBncmFjZV9lbmRfYXQ/OiBzdHJpbmcgfCBudWxsXHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICAgIG5vaXNlX3JlcG9ydHM6IHtcclxuICAgICAgICBSb3c6IHtcclxuICAgICAgICAgIGlkOiBzdHJpbmdcclxuICAgICAgICAgIHVzZXJfbmFtZTogc3RyaW5nXHJcbiAgICAgICAgICB0aW1lc3RhbXA6IHN0cmluZ1xyXG4gICAgICAgICAgbGFzdF9yZXBvcnRlZDogc3RyaW5nXHJcbiAgICAgICAgfVxyXG4gICAgICAgIEluc2VydDoge1xyXG4gICAgICAgICAgaWQ6IHN0cmluZ1xyXG4gICAgICAgICAgdXNlcl9uYW1lOiBzdHJpbmdcclxuICAgICAgICAgIHRpbWVzdGFtcD86IHN0cmluZ1xyXG4gICAgICAgICAgbGFzdF9yZXBvcnRlZD86IHN0cmluZ1xyXG4gICAgICAgIH1cclxuICAgICAgICBVcGRhdGU6IHtcclxuICAgICAgICAgIGlkPzogc3RyaW5nXHJcbiAgICAgICAgICB1c2VyX25hbWU/OiBzdHJpbmdcclxuICAgICAgICAgIHRpbWVzdGFtcD86IHN0cmluZ1xyXG4gICAgICAgICAgbGFzdF9yZXBvcnRlZD86IHN0cmluZ1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgICBzdWJsZXRzOiB7XHJcbiAgICAgICAgUm93OiB7XHJcbiAgICAgICAgICBpZDogc3RyaW5nXHJcbiAgICAgICAgICB1c2VyX25hbWU6IHN0cmluZ1xyXG4gICAgICAgICAgZHVyYXRpb246IHN0cmluZ1xyXG4gICAgICAgICAgdGltZXN0YW1wOiBzdHJpbmdcclxuICAgICAgICB9XHJcbiAgICAgICAgSW5zZXJ0OiB7XHJcbiAgICAgICAgICBpZDogc3RyaW5nXHJcbiAgICAgICAgICB1c2VyX25hbWU6IHN0cmluZ1xyXG4gICAgICAgICAgZHVyYXRpb246IHN0cmluZ1xyXG4gICAgICAgICAgdGltZXN0YW1wPzogc3RyaW5nXHJcbiAgICAgICAgfVxyXG4gICAgICAgIFVwZGF0ZToge1xyXG4gICAgICAgICAgaWQ/OiBzdHJpbmdcclxuICAgICAgICAgIHVzZXJfbmFtZT86IHN0cmluZ1xyXG4gICAgICAgICAgZHVyYXRpb24/OiBzdHJpbmdcclxuICAgICAgICAgIHRpbWVzdGFtcD86IHN0cmluZ1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgICBoZWxwX3JlcXVlc3RzOiB7XHJcbiAgICAgICAgUm93OiB7XHJcbiAgICAgICAgICBpZDogc3RyaW5nXHJcbiAgICAgICAgICB1c2VyX25hbWU6IHN0cmluZ1xyXG4gICAgICAgICAgZGVzY3JpcHRpb246IHN0cmluZ1xyXG4gICAgICAgICAgdGltZXN0YW1wOiBzdHJpbmdcclxuICAgICAgICB9XHJcbiAgICAgICAgSW5zZXJ0OiB7XHJcbiAgICAgICAgICBpZDogc3RyaW5nXHJcbiAgICAgICAgICB1c2VyX25hbWU6IHN0cmluZ1xyXG4gICAgICAgICAgZGVzY3JpcHRpb246IHN0cmluZ1xyXG4gICAgICAgICAgdGltZXN0YW1wPzogc3RyaW5nXHJcbiAgICAgICAgfVxyXG4gICAgICAgIFVwZGF0ZToge1xyXG4gICAgICAgICAgaWQ/OiBzdHJpbmdcclxuICAgICAgICAgIHVzZXJfbmFtZT86IHN0cmluZ1xyXG4gICAgICAgICAgZGVzY3JpcHRpb24/OiBzdHJpbmdcclxuICAgICAgICAgIHRpbWVzdGFtcD86IHN0cmluZ1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgICBpbmNpZGVudHM6IHtcclxuICAgICAgICBSb3c6IHtcclxuICAgICAgICAgIGlkOiBzdHJpbmdcclxuICAgICAgICAgIG1hY2hpbmVfaWQ6IHN0cmluZ1xyXG4gICAgICAgICAgaW5jaWRlbnRfdHlwZTogc3RyaW5nXHJcbiAgICAgICAgICB0aW1lc3RhbXA6IHN0cmluZ1xyXG4gICAgICAgIH1cclxuICAgICAgICBJbnNlcnQ6IHtcclxuICAgICAgICAgIGlkOiBzdHJpbmdcclxuICAgICAgICAgIG1hY2hpbmVfaWQ6IHN0cmluZ1xyXG4gICAgICAgICAgaW5jaWRlbnRfdHlwZT86IHN0cmluZ1xyXG4gICAgICAgICAgdGltZXN0YW1wPzogc3RyaW5nXHJcbiAgICAgICAgfVxyXG4gICAgICAgIFVwZGF0ZToge1xyXG4gICAgICAgICAgaWQ/OiBzdHJpbmdcclxuICAgICAgICAgIG1hY2hpbmVfaWQ/OiBzdHJpbmdcclxuICAgICAgICAgIGluY2lkZW50X3R5cGU/OiBzdHJpbmdcclxuICAgICAgICAgIHRpbWVzdGFtcD86IHN0cmluZ1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxufVxyXG4iXSwibmFtZXMiOlsiY3JlYXRlQ2xpZW50Iiwic3VwYWJhc2VVcmwiLCJzdXBhYmFzZUFub25LZXkiLCJzdXBhYmFzZSIsInJlYWx0aW1lIiwicGFyYW1zIiwiZXZlbnRzUGVyU2Vjb25kIiwiYXV0aCIsInBlcnNpc3RTZXNzaW9uIiwidGVzdFN1cGFiYXNlQ29ubmVjdGlvbiIsImRhdGEiLCJlcnJvciIsImZyb20iLCJzZWxlY3QiLCJzaW5nbGUiLCJzdWNjZXNzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/machineOwnership.ts":
/*!***************************************!*\
  !*** ./src/utils/machineOwnership.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   canAdjustTime: () => (/* binding */ canAdjustTime),\n/* harmony export */   getOwnershipBadgeClasses: () => (/* binding */ getOwnershipBadgeClasses),\n/* harmony export */   getOwnershipDisplay: () => (/* binding */ getOwnershipDisplay),\n/* harmony export */   getTimeUntilAdjustmentAvailable: () => (/* binding */ getTimeUntilAdjustmentAvailable),\n/* harmony export */   hasOwner: () => (/* binding */ hasOwner),\n/* harmony export */   isCurrentUserOwner: () => (/* binding */ isCurrentUserOwner)\n/* harmony export */ });\n/* harmony import */ var _userIdentification__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./userIdentification */ \"(ssr)/./src/utils/userIdentification.ts\");\n// Utility functions for machine ownership and user identification\n\n/**\n * Check if the current user owns/started a specific machine\n */ function isCurrentUserOwner(machine) {\n    const currentUserId = (0,_userIdentification__WEBPACK_IMPORTED_MODULE_0__.getDeviceUserId)();\n    return machine.startedByUserId === currentUserId;\n}\n/**\n * Check if a machine has an owner (was started by someone)\n */ function hasOwner(machine) {\n    return !!machine.startedByUserId;\n}\n/**\n * Get display text for machine ownership\n */ function getOwnershipDisplay(machine) {\n    if (!hasOwner(machine)) {\n        return \"\";\n    }\n    if (isCurrentUserOwner(machine)) {\n        return \"Your Machine\";\n    }\n    return `Started by ${machine.startedByUserId}`;\n}\n/**\n * Get ownership badge color classes\n */ function getOwnershipBadgeClasses(machine) {\n    if (!hasOwner(machine)) {\n        return \"\";\n    }\n    if (isCurrentUserOwner(machine)) {\n        return \"bg-green-100 text-green-800 border-green-200\";\n    }\n    return \"bg-blue-100 text-blue-800 border-blue-200\";\n}\n/**\n * Check if current user can adjust time for a machine\n * Only available after 10 minutes of machine running\n */ function canAdjustTime(machine) {\n    if (machine.status !== \"running\" || !isCurrentUserOwner(machine)) {\n        return false;\n    }\n    // Check if machine has been running for at least 10 minutes\n    if (machine.startAt) {\n        const now = new Date();\n        const timeSinceStart = now.getTime() - machine.startAt.getTime();\n        const tenMinutesInMs = 10 * 60 * 1000;\n        return timeSinceStart >= tenMinutesInMs;\n    }\n    return false;\n}\n/**\n * Get time remaining until adjustment is available (in minutes)\n * Returns 0 if adjustment is already available or not applicable\n */ function getTimeUntilAdjustmentAvailable(machine) {\n    if (machine.status !== \"running\" || !isCurrentUserOwner(machine) || !machine.startAt) {\n        return 0;\n    }\n    const now = new Date();\n    const timeSinceStart = now.getTime() - machine.startAt.getTime();\n    const tenMinutesInMs = 10 * 60 * 1000;\n    if (timeSinceStart >= tenMinutesInMs) {\n        return 0 // Already available\n        ;\n    }\n    const timeRemaining = tenMinutesInMs - timeSinceStart;\n    return Math.ceil(timeRemaining / (60 * 1000)) // Convert to minutes\n    ;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/machineOwnership.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/parseDuration.tsx":
/*!*************************************!*\
  !*** ./src/utils/parseDuration.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ parseDuration)\n/* harmony export */ });\nfunction parseDuration(durationStr) {\n    const trimmed = durationStr.trim().toLowerCase();\n    // Match patterns like \"1 hr 30 min\", \"2 hours\", \"45 minutes\", etc.\n    const hourMatch = trimmed.match(/(\\d+)\\s*(hr|hour|hours)/);\n    const minuteMatch = trimmed.match(/(\\d+)\\s*(min|minute|minutes)/);\n    let totalSeconds = 0;\n    if (hourMatch) {\n        totalSeconds += Number.parseInt(hourMatch[1]) * 3600;\n    }\n    if (minuteMatch) {\n        totalSeconds += Number.parseInt(minuteMatch[1]) * 60;\n    }\n    // If no valid time found, throw error\n    if (totalSeconds === 0) {\n        throw new Error(`Invalid duration format: ${durationStr}`);\n    }\n    return totalSeconds;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/parseDuration.tsx\n");

/***/ }),

/***/ "(ssr)/./src/utils/userIdentification.ts":
/*!*****************************************!*\
  !*** ./src/utils/userIdentification.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDeviceUserId: () => (/* binding */ getDeviceUserId),\n/* harmony export */   getUserDisplayName: () => (/* binding */ getUserDisplayName),\n/* harmony export */   isCurrentUserPost: () => (/* binding */ isCurrentUserPost),\n/* harmony export */   setUserDisplayName: () => (/* binding */ setUserDisplayName)\n/* harmony export */ });\n// Generate a consistent user ID for this device\nfunction getDeviceUserId() {\n    // Return a default value during SSR\n    if (true) return \"User1\";\n    try {\n        let userId = localStorage.getItem(\"dorm-dashboard-user-id\");\n        if (!userId) {\n            // Generate a more robust device fingerprint\n            const fingerprint = generateDeviceFingerprint();\n            const hash = hashString(fingerprint);\n            const userNumber = Math.abs(hash % 9999) + 1;\n            userId = `User${userNumber.toString().padStart(4, \"0\")}`;\n            localStorage.setItem(\"dorm-dashboard-user-id\", userId);\n        }\n        return userId;\n    } catch (error) {\n        return \"User1\";\n    }\n}\n// Generate a device fingerprint\nfunction generateDeviceFingerprint() {\n    const canvas = document.createElement(\"canvas\");\n    const ctx = canvas.getContext(\"2d\");\n    let fingerprint = \"\";\n    if (ctx) {\n        ctx.textBaseline = \"top\";\n        ctx.font = \"14px Arial\";\n        ctx.fillText(\"Device fingerprint\", 2, 2);\n        fingerprint += canvas.toDataURL();\n    }\n    // Add more device characteristics\n    fingerprint += navigator.userAgent;\n    fingerprint += navigator.language;\n    fingerprint += screen.width + \"x\" + screen.height;\n    fingerprint += new Date().getTimezoneOffset();\n    fingerprint += navigator.hardwareConcurrency || \"unknown\";\n    return fingerprint;\n}\n// Simple hash function\nfunction hashString(str) {\n    let hash = 0;\n    for(let i = 0; i < str.length; i++){\n        const char = str.charCodeAt(i);\n        hash = (hash << 5) - hash + char;\n        hash = hash & hash // Convert to 32-bit integer\n        ;\n    }\n    return hash;\n}\n// Get a display name for the user\nfunction getUserDisplayName() {\n    if (true) return \"User\";\n    try {\n        const displayName = localStorage.getItem(\"dorm-dashboard-display-name\");\n        if (displayName) {\n            return displayName;\n        }\n        return getDeviceUserId();\n    } catch (error) {\n        return \"User\";\n    }\n}\n// Set a custom display name\nfunction setUserDisplayName(name) {\n    if (true) return;\n    try {\n        // Sanitize the display name\n        const sanitized = name.trim().slice(0, 20) // Max 20 characters\n        ;\n        if (sanitized) {\n            localStorage.setItem(\"dorm-dashboard-display-name\", sanitized);\n        }\n    } catch (error) {\n        console.warn(\"Could not save display name:\", error);\n    }\n}\n// Check if current user owns a post\nfunction isCurrentUserPost(postUserId) {\n    if (true) return false;\n    const currentUserId = getDeviceUserId();\n    const currentDisplayName = getUserDisplayName();\n    // Check both user ID and display name for backwards compatibility\n    return postUserId === currentUserId || postUserId === currentDisplayName;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/userIdentification.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?8bfc":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?cf7b":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0","vendor-chunks/@swc+helpers@0.5.15","vendor-chunks/tr46@0.0.3","vendor-chunks/@supabase+auth-js@2.70.0","vendor-chunks/ws@8.18.2","vendor-chunks/@supabase+realtime-js@2.11.10","vendor-chunks/@supabase+postgrest-js@1.19.4","vendor-chunks/@supabase+node-fetch@2.6.15","vendor-chunks/whatwg-url@5.0.0","vendor-chunks/@supabase+storage-js@2.7.1","vendor-chunks/@supabase+supabase-js@2.50.0","vendor-chunks/@supabase+functions-js@2.4.4","vendor-chunks/webidl-conversions@3.0.1","vendor-chunks/qrcode.react@4.2.0_react@19.0.0"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fcheckin%2Fpage&page=%2Fcheckin%2Fpage&appPaths=%2Fcheckin%2Fpage&pagePath=private-next-app-dir%2Fcheckin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmemet%5CDesktop%5Cdorm_21%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmemet%5CDesktop%5Cdorm_21&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();