/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/checkin/page";
exports.ids = ["app/checkin/page"];
exports.modules = {

/***/ "(rsc)/./app/checkin/loading.tsx":
/*!*********************************!*\
  !*** ./app/checkin/loading.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Loading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white p-8 rounded-lg shadow text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-lg\",\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\loading.tsx\",\n                lineNumber: 5,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\loading.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\loading.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvY2hlY2tpbi9sb2FkaW5nLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWUsU0FBU0E7SUFDdEIscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNEO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOzBCQUFVOzs7Ozs7Ozs7Ozs7Ozs7O0FBSWpDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG1lbWV0XFxEZXNrdG9wXFxkb3JtXzIxXFxhcHBcXGNoZWNraW5cXGxvYWRpbmcudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExvYWRpbmcoKSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktMTAwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcC04IHJvdW5kZWQtbGcgc2hhZG93IHRleHQtY2VudGVyXCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWxnXCI+TG9hZGluZy4uLjwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvZGl2PlxyXG4gIClcclxufVxyXG4iXSwibmFtZXMiOlsiTG9hZGluZyIsImRpdiIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/checkin/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./app/checkin/page.tsx":
/*!******************************!*\
  !*** ./app/checkin/page.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\dorm_21\\app\\checkin\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"59e27c0181b7\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbWVtZXRcXERlc2t0b3BcXGRvcm1fMjFcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI1OWUyN2MwMTgxYjdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\nconst metadata = {\n    title: 'Dorm Dashboard',\n    description: 'Dorm 21 Management Dashboard',\n    generator: 'v0.dev'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"min-h-screen bg-bgLight text-gray-800 font-sans\",\n            suppressHydrationWarning: true,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"bg-white shadow-sm p-4 border-b border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl font-bold text-primary\",\n                            children: \"\\uD83C\\uDFE0 Dorm Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\layout.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\layout.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\layout.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"container mx-auto py-8\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\layout.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                    className: \"text-center text-sm text-gray-500 py-4 border-t border-gray-200 bg-white\",\n                    children: \"dorm21\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\layout.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fcheckin%2Fpage&page=%2Fcheckin%2Fpage&appPaths=%2Fcheckin%2Fpage&pagePath=private-next-app-dir%2Fcheckin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmemet%5CDesktop%5Cdorm_21%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmemet%5CDesktop%5Cdorm_21&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fcheckin%2Fpage&page=%2Fcheckin%2Fpage&appPaths=%2Fcheckin%2Fpage&pagePath=private-next-app-dir%2Fcheckin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmemet%5CDesktop%5Cdorm_21%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmemet%5CDesktop%5Cdorm_21&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/module.compiled.js?4602\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/checkin/loading.tsx */ \"(rsc)/./app/checkin/loading.tsx\"));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/checkin/page.tsx */ \"(rsc)/./app/checkin/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'checkin',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'loading': [module4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\loading.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/checkin/page\",\n        pathname: \"/checkin\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fcheckin%2Fpage&page=%2Fcheckin%2Fpage&appPaths=%2Fcheckin%2Fpage&pagePath=private-next-app-dir%2Fcheckin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmemet%5CDesktop%5Cdorm_21%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmemet%5CDesktop%5Cdorm_21&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Capp%5C%5Ccheckin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Capp%5C%5Ccheckin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/checkin/page.tsx */ \"(rsc)/./app/checkin/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4yLjRfcmVhY3QtZG9tQDE5LjAuMF9yZWFjdEAxOS4wLjBfX3JlYWN0QDE5LjAuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21lbWV0JTVDJTVDRGVza3RvcCU1QyU1Q2Rvcm1fMjElNUMlNUNhcHAlNUMlNUNjaGVja2luJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdKQUErRiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcbWVtZXRcXFxcRGVza3RvcFxcXFxkb3JtXzIxXFxcXGFwcFxcXFxjaGVja2luXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Capp%5C%5Ccheckin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4yLjRfcmVhY3QtZG9tQDE5LjAuMF9yZWFjdEAxOS4wLjBfX3JlYWN0QDE5LjAuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21lbWV0JTVDJTVDRGVza3RvcCU1QyU1Q2Rvcm1fMjElNUMlNUNub2RlX21vZHVsZXMlNUMlNUMucG5wbSU1QyU1Q25leHQlNDAxNS4yLjRfcmVhY3QtZG9tJTQwMTkuMC4wX3JlYWN0JTQwMTkuMC4wX19yZWFjdCU0MDE5LjAuMCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1wYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21lbWV0JTVDJTVDRGVza3RvcCU1QyU1Q2Rvcm1fMjElNUMlNUNub2RlX21vZHVsZXMlNUMlNUMucG5wbSU1QyU1Q25leHQlNDAxNS4yLjRfcmVhY3QtZG9tJTQwMTkuMC4wX3JlYWN0JTQwMTkuMC4wX19yZWFjdCU0MDE5LjAuMCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1zZWdtZW50LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21lbWV0JTVDJTVDRGVza3RvcCU1QyU1Q2Rvcm1fMjElNUMlNUNub2RlX21vZHVsZXMlNUMlNUMucG5wbSU1QyU1Q25leHQlNDAxNS4yLjRfcmVhY3QtZG9tJTQwMTkuMC4wX3JlYWN0JTQwMTkuMC4wX19yZWFjdCU0MDE5LjAuMCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21lbWV0JTVDJTVDRGVza3RvcCU1QyU1Q2Rvcm1fMjElNUMlNUNub2RlX21vZHVsZXMlNUMlNUMucG5wbSU1QyU1Q25leHQlNDAxNS4yLjRfcmVhY3QtZG9tJTQwMTkuMC4wX3JlYWN0JTQwMTkuMC4wX19yZWFjdCU0MDE5LjAuMCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2h0dHAtYWNjZXNzLWZhbGxiYWNrJTVDJTVDZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDbWVtZXQlNUMlNUNEZXNrdG9wJTVDJTVDZG9ybV8yMSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Qy5wbnBtJTVDJTVDbmV4dCU0MDE1LjIuNF9yZWFjdC1kb20lNDAxOS4wLjBfcmVhY3QlNDAxOS4wLjBfX3JlYWN0JTQwMTkuMC4wJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDbGF5b3V0LXJvdXRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNtZW1ldCU1QyU1Q0Rlc2t0b3AlNUMlNUNkb3JtXzIxJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDLnBucG0lNUMlNUNuZXh0JTQwMTUuMi40X3JlYWN0LWRvbSU0MDE5LjAuMF9yZWFjdCU0MDE5LjAuMF9fcmVhY3QlNDAxOS4wLjAlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21lbWV0JTVDJTVDRGVza3RvcCU1QyU1Q2Rvcm1fMjElNUMlNUNub2RlX21vZHVsZXMlNUMlNUMucG5wbSU1QyU1Q25leHQlNDAxNS4yLjRfcmVhY3QtZG9tJTQwMTkuMC4wX3JlYWN0JTQwMTkuMC4wX19yZWFjdCU0MDE5LjAuMCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q21ldGFkYXRhJTVDJTVDbWV0YWRhdGEtYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDbWVtZXQlNUMlNUNEZXNrdG9wJTVDJTVDZG9ybV8yMSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Qy5wbnBtJTVDJTVDbmV4dCU0MDE1LjIuNF9yZWFjdC1kb20lNDAxOS4wLjBfcmVhY3QlNDAxOS4wLjBfX3JlYWN0JTQwMTkuMC4wJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMFhBQW1OO0FBQ25OO0FBQ0EsZ1lBQXNOO0FBQ3ROO0FBQ0EsZ1lBQXNOO0FBQ3ROO0FBQ0EsMGFBQTRPO0FBQzVPO0FBQ0EsOFhBQXFOO0FBQ3JOO0FBQ0Esa1pBQWdPO0FBQ2hPO0FBQ0Esd1pBQW1PO0FBQ25PO0FBQ0EsNFpBQW9PIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxtZW1ldFxcXFxEZXNrdG9wXFxcXGRvcm1fMjFcXFxcbm9kZV9tb2R1bGVzXFxcXC5wbnBtXFxcXG5leHRAMTUuMi40X3JlYWN0LWRvbUAxOS4wLjBfcmVhY3RAMTkuMC4wX19yZWFjdEAxOS4wLjBcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtcGFnZS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcbWVtZXRcXFxcRGVza3RvcFxcXFxkb3JtXzIxXFxcXG5vZGVfbW9kdWxlc1xcXFwucG5wbVxcXFxuZXh0QDE1LjIuNF9yZWFjdC1kb21AMTkuMC4wX3JlYWN0QDE5LjAuMF9fcmVhY3RAMTkuMC4wXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcY2xpZW50LXNlZ21lbnQuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXG1lbWV0XFxcXERlc2t0b3BcXFxcZG9ybV8yMVxcXFxub2RlX21vZHVsZXNcXFxcLnBucG1cXFxcbmV4dEAxNS4yLjRfcmVhY3QtZG9tQDE5LjAuMF9yZWFjdEAxOS4wLjBfX3JlYWN0QDE5LjAuMFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxtZW1ldFxcXFxEZXNrdG9wXFxcXGRvcm1fMjFcXFxcbm9kZV9tb2R1bGVzXFxcXC5wbnBtXFxcXG5leHRAMTUuMi40X3JlYWN0LWRvbUAxOS4wLjBfcmVhY3RAMTkuMC4wX19yZWFjdEAxOS4wLjBcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxodHRwLWFjY2Vzcy1mYWxsYmFja1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcbWVtZXRcXFxcRGVza3RvcFxcXFxkb3JtXzIxXFxcXG5vZGVfbW9kdWxlc1xcXFwucG5wbVxcXFxuZXh0QDE1LjIuNF9yZWFjdC1kb21AMTkuMC4wX3JlYWN0QDE5LjAuMF9fcmVhY3RAMTkuMC4wXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbGF5b3V0LXJvdXRlci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcbWVtZXRcXFxcRGVza3RvcFxcXFxkb3JtXzIxXFxcXG5vZGVfbW9kdWxlc1xcXFwucG5wbVxcXFxuZXh0QDE1LjIuNF9yZWFjdC1kb21AMTkuMC4wX3JlYWN0QDE5LjAuMF9fcmVhY3RAMTkuMC4wXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbWV0YWRhdGFcXFxcYXN5bmMtbWV0YWRhdGEuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXG1lbWV0XFxcXERlc2t0b3BcXFxcZG9ybV8yMVxcXFxub2RlX21vZHVsZXNcXFxcLnBucG1cXFxcbmV4dEAxNS4yLjRfcmVhY3QtZG9tQDE5LjAuMF9yZWFjdEAxOS4wLjBfX3JlYWN0QDE5LjAuMFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXG1ldGFkYXRhXFxcXG1ldGFkYXRhLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxtZW1ldFxcXFxEZXNrdG9wXFxcXGRvcm1fMjFcXFxcbm9kZV9tb2R1bGVzXFxcXC5wbnBtXFxcXG5leHRAMTUuMi40X3JlYWN0LWRvbUAxOS4wLjBfcmVhY3RAMTkuMC4wX19yZWFjdEAxOS4wLjBcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/checkin/page.tsx":
/*!******************************!*\
  !*** ./app/checkin/page.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CheckInPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var qrcode_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! qrcode.react */ \"(ssr)/./node_modules/.pnpm/qrcode.react@4.2.0_react@19.0.0/node_modules/qrcode.react/lib/esm/index.js\");\n/* harmony import */ var _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/src/hooks/useSupabaseData */ \"(ssr)/./src/hooks/useSupabaseData.tsx\");\n/* harmony import */ var _src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/src/hooks/useCountdown */ \"(ssr)/./src/hooks/useCountdown.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction CheckInPage() {\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const machineId = searchParams.get(\"machine\");\n    const { laundry, toggleMachineStatus, reserveMachine, isLoading, refreshData } = (0,_src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    const [machine, setMachine] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [actionResult, setActionResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [customTime, setCustomTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"60\");\n    const [useCustomTime, setUseCustomTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const countdown = (0,_src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(machine?.endAt, machine?.graceEndAt);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CheckInPage.useEffect\": ()=>{\n            if (!machineId) return;\n            const foundMachine = laundry.find({\n                \"CheckInPage.useEffect.foundMachine\": (m)=>m.id === machineId\n            }[\"CheckInPage.useEffect.foundMachine\"]);\n            setMachine(foundMachine || null);\n        }\n    }[\"CheckInPage.useEffect\"], [\n        machineId,\n        laundry\n    ]);\n    // Reduced auto-refresh to every 10 seconds to prevent conflicts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CheckInPage.useEffect\": ()=>{\n            const interval = setInterval({\n                \"CheckInPage.useEffect.interval\": ()=>{\n                    refreshData();\n                }\n            }[\"CheckInPage.useEffect.interval\"], 10000);\n            return ({\n                \"CheckInPage.useEffect\": ()=>clearInterval(interval)\n            })[\"CheckInPage.useEffect\"];\n        }\n    }[\"CheckInPage.useEffect\"], [\n        refreshData\n    ]);\n    const handleToggle = async ()=>{\n        if (!machineId || isProcessing) return;\n        setIsProcessing(true);\n        setActionResult(\"\");\n        let success = false;\n        if (machine?.status === \"free\" && useCustomTime) {\n            // Use custom time\n            success = await reserveMachine(machineId, `${customTime} minutes`);\n        } else {\n            // Use default toggle\n            success = await toggleMachineStatus(machineId);\n        }\n        if (success) {\n            setActionResult(\"Status updated successfully!\");\n            // Single refresh after 2 seconds to confirm the change\n            setTimeout(()=>refreshData(), 2000);\n        } else {\n            setActionResult(\"Error updating machine status\");\n        }\n        setIsProcessing(false);\n    };\n    const getStatusDisplay = ()=>{\n        if (!machine) return \"\";\n        switch(machine.status){\n            case \"free\":\n                return \"🟢 Available\";\n            case \"running\":\n                const hours = Math.floor(countdown.secondsLeft / 3600);\n                const minutes = Math.floor(countdown.secondsLeft % 3600 / 60);\n                const seconds = countdown.secondsLeft % 60;\n                return `🔵 In Use - ${hours}:${minutes.toString().padStart(2, \"0\")}:${seconds.toString().padStart(2, \"0\")} left`;\n            case \"finishedGrace\":\n                // Calculate grace period time remaining\n                let graceTimeDisplay = \"5:00\";\n                if (machine.graceEndAt) {\n                    const graceMinutes = Math.floor(countdown.graceSecondsLeft / 60);\n                    const graceSeconds = countdown.graceSecondsLeft % 60;\n                    graceTimeDisplay = `${graceMinutes}:${graceSeconds.toString().padStart(2, \"0\")}`;\n                }\n                return `⚠️ Please collect items - ${graceTimeDisplay} left`;\n            default:\n                return \"Unknown\";\n        }\n    };\n    const getButtonText = ()=>{\n        if (isProcessing) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 11\n                    }, this),\n                    \"Processing...\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 96,\n                columnNumber: 9\n            }, this);\n        }\n        switch(machine?.status){\n            case \"free\":\n                return useCustomTime ? `▶️ Start Using Machine (${customTime} min)` : \"▶️ Start Using Machine\";\n            case \"running\":\n                return \"⏹️ Stop Using Machine\";\n            case \"finishedGrace\":\n                return \"✅ I've Collected My Items\";\n            default:\n                return \"Update Status\";\n        }\n    };\n    const getButtonColor = ()=>{\n        if (isProcessing) return \"bg-gray-400 cursor-not-allowed\";\n        switch(machine?.status){\n            case \"free\":\n                return \"bg-blue-500 hover:bg-blue-600\";\n            case \"running\":\n                return \"bg-red-500 hover:bg-red-600\";\n            case \"finishedGrace\":\n                return \"bg-green-500 hover:bg-green-600\";\n            default:\n                return \"bg-gray-500\";\n        }\n    };\n    const handleBackToHome = ()=>{\n        router.push(\"/\");\n    };\n    // Custom display names for machines (same logic as LaundryCard)\n    const getDisplayName = (machine)=>{\n        if (!machine) return \"\";\n        const isWasher = machine.name.toLowerCase().includes(\"washer\");\n        if (isWasher) {\n            // Extract number from washer name (e.g., \"Washer 1\" -> \"1\")\n            const numberMatch = machine.name.match(/\\d+/);\n            const number = numberMatch ? numberMatch[0] : \"\";\n            return `Washer ${number}`;\n        } else {\n            // For dryers, use \"Dryer 5\" through \"Dryer 8\" based on original number\n            const numberMatch = machine.name.match(/\\d+/);\n            const originalNumber = numberMatch ? Number.parseInt(numberMatch[0]) : 0;\n            const newNumber = originalNumber + 4 // Map 1->5, 2->6, 3->7, 4->8\n            ;\n            return `Dryer ${newNumber}`;\n        }\n    };\n    if (isLoading && !machine) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-8 rounded-lg shadow text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-lg\",\n                        children: \"Getting machine information...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 157,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 156,\n            columnNumber: 7\n        }, this);\n    }\n    if (!machineId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-8 rounded-lg shadow text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-600 text-lg mb-4\",\n                        children: \"Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600\",\n                        children: \"No machine ID provided.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 168,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 167,\n            columnNumber: 7\n        }, this);\n    }\n    if (!machine) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-8 rounded-lg shadow text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-600 text-lg mb-4\",\n                        children: \"Machine Not Found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"The requested machine could not be found.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: refreshData,\n                        className: \"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded\",\n                        children: \"Retry Loading\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 179,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 178,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white p-8 rounded-lg shadow text-center max-w-md w-full mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-4\",\n                    children: \"Machine Check-In\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 193,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-600 mb-3\",\n                            children: \"Scan to access this page\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white p-3 rounded-lg border-2 border-gray-200 shadow-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(qrcode_react__WEBPACK_IMPORTED_MODULE_3__.QRCodeSVG, {\n                                    value: `${ false ? 0 : \"\"}/checkin?machine=${machineId}`,\n                                    size: 80,\n                                    fgColor: \"#1A1F36\",\n                                    bgColor: \"#FFFFFF\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-lg font-medium\",\n                            children: getDisplayName(machine)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `text-sm mt-1 font-semibold ${machine.status === \"free\" ? \"text-green-600\" : machine.status === \"running\" ? \"text-blue-600\" : machine.status === \"finishedGrace\" ? \"text-orange-600\" : \"text-red-600\"} ${machine.status === \"finishedGrace\" ? \"animate-pulse\" : \"\"}`,\n                            children: [\n                                \"Status: \",\n                                getStatusDisplay()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this),\n                        machine.status === \"running\" && machine.endAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500 mt-1\",\n                            children: [\n                                \"Ends at: \",\n                                machine.endAt.toLocaleTimeString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 13\n                        }, this),\n                        machine.status === \"finishedGrace\" && machine.graceEndAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-orange-500 mt-1\",\n                            children: [\n                                \"Grace period ends at: \",\n                                machine.graceEndAt.toLocaleTimeString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 210,\n                    columnNumber: 9\n                }, this),\n                machine.status === \"free\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    id: \"useCustomTime\",\n                                    checked: useCustomTime,\n                                    onChange: (e)=>setUseCustomTime(e.target.checked),\n                                    className: \"mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"useCustomTime\",\n                                    className: \"text-sm\",\n                                    children: \"Set custom time\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 13\n                        }, this),\n                        useCustomTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"number\",\n                                    value: customTime,\n                                    onChange: (e)=>setCustomTime(e.target.value),\n                                    min: \"1\",\n                                    max: \"120\",\n                                    className: \"border rounded p-2 w-20 text-center mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"minutes\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleToggle,\n                    disabled: isProcessing,\n                    className: `w-full p-3 rounded text-white font-medium mb-4 ${getButtonColor()}`,\n                    children: getButtonText()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 269,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: refreshData,\n                    disabled: isLoading,\n                    className: \"w-full p-2 rounded border border-gray-300 text-gray-700 hover:bg-gray-50 mb-4\",\n                    children: \"Refresh Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleBackToHome,\n                    className: \"w-full p-2 rounded bg-accent hover:bg-teal-600 text-white font-medium mb-4 transition-colors duration-200\",\n                    children: \"← Back to Home\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 286,\n                    columnNumber: 9\n                }, this),\n                actionResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `text-sm mb-4 p-2 rounded ${actionResult.includes(\"Error\") ? \"bg-red-100 text-red-700\" : \"bg-green-100 text-green-700\"}`,\n                    children: actionResult\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 11\n                }, this),\n                machine.status === \"finishedGrace\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-orange-50 border border-orange-200 rounded p-3 text-sm text-orange-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium mb-1\",\n                            children: \"⚠️ Grace Period Active\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Please collect your items within the time limit. The machine will become available automatically when the grace period ends.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 192,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n        lineNumber: 191,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/checkin/page.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \*************************************************************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Capp%5C%5Ccheckin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Capp%5C%5Ccheckin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/checkin/page.tsx */ \"(ssr)/./app/checkin/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4yLjRfcmVhY3QtZG9tQDE5LjAuMF9yZWFjdEAxOS4wLjBfX3JlYWN0QDE5LjAuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21lbWV0JTVDJTVDRGVza3RvcCU1QyU1Q2Rvcm1fMjElNUMlNUNhcHAlNUMlNUNjaGVja2luJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdKQUErRiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcbWVtZXRcXFxcRGVza3RvcFxcXFxkb3JtXzIxXFxcXGFwcFxcXFxjaGVja2luXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Capp%5C%5Ccheckin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/hooks/useCountdown.tsx":
/*!************************************!*\
  !*** ./src/hooks/useCountdown.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useCountdown)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction useCountdown(endAt, graceEndAt) {\n    const [secondsLeft, setSecondsLeft] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const [inGrace, setInGrace] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [graceSecondsLeft, setGraceSecondsLeft] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useCountdown.useEffect\": ()=>{\n            const updateCountdown = {\n                \"useCountdown.useEffect.updateCountdown\": ()=>{\n                    const now = new Date();\n                    if (endAt) {\n                        const timeLeft = Math.max(0, Math.floor((endAt.getTime() - now.getTime()) / 1000));\n                        setSecondsLeft(timeLeft);\n                        // Check if we're in grace period\n                        if (graceEndAt) {\n                            const graceLeft = Math.max(0, Math.floor((graceEndAt.getTime() - now.getTime()) / 1000));\n                            setGraceSecondsLeft(graceLeft);\n                            setInGrace(timeLeft === 0 && graceLeft > 0);\n                        } else if (timeLeft === 0) {\n                            // If no graceEndAt but we're at 0 time left and status is finishedGrace,\n                            // use a default 5 minute grace period\n                            const defaultGraceEnd = new Date(endAt.getTime() + 5 * 60 * 1000);\n                            const graceLeft = Math.max(0, Math.floor((defaultGraceEnd.getTime() - now.getTime()) / 1000));\n                            setGraceSecondsLeft(graceLeft);\n                            setInGrace(graceLeft > 0);\n                        } else {\n                            setInGrace(false);\n                            setGraceSecondsLeft(0);\n                        }\n                    } else {\n                        setSecondsLeft(0);\n                        setInGrace(false);\n                        setGraceSecondsLeft(0);\n                    }\n                }\n            }[\"useCountdown.useEffect.updateCountdown\"];\n            updateCountdown();\n            const interval = setInterval(updateCountdown, 1000);\n            return ({\n                \"useCountdown.useEffect\": ()=>clearInterval(interval)\n            })[\"useCountdown.useEffect\"];\n        }\n    }[\"useCountdown.useEffect\"], [\n        endAt,\n        graceEndAt\n    ]);\n    return {\n        secondsLeft,\n        inGrace,\n        graceSecondsLeft\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useCountdown.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useSupabaseData.tsx":
/*!***************************************!*\
  !*** ./src/hooks/useSupabaseData.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useSupabaseData)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/src/lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n/* harmony import */ var _src_lib_subscriptionManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/src/lib/subscriptionManager */ \"(ssr)/./src/lib/subscriptionManager.ts\");\n/* harmony import */ var _src_lib_machineStatusManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/lib/machineStatusManager */ \"(ssr)/./src/lib/machineStatusManager.ts\");\n/* harmony import */ var _src_utils_parseDuration__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/src/utils/parseDuration */ \"(ssr)/./src/utils/parseDuration.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n// Helper functions to convert between DB and app formats\nconst dbToMachine = (row)=>{\n    const graceEndAt = row.grace_end_at ? new Date(row.grace_end_at) : row.status === \"finishedGrace\" && row.end_at ? new Date(new Date(row.end_at).getTime() + 5 * 60 * 1000) : undefined;\n    return {\n        id: row.id,\n        name: row.name,\n        status: row.status,\n        startAt: row.start_at ? new Date(row.start_at) : undefined,\n        endAt: row.end_at ? new Date(row.end_at) : undefined,\n        graceEndAt: graceEndAt,\n        updatedAt: new Date(row.updated_at)\n    };\n};\nconst dbToNoise = (row)=>({\n        id: row.id,\n        user: row.user_name,\n        description: row.description || \"Noise reported\",\n        timestamp: new Date(row.timestamp),\n        lastReported: new Date(row.last_reported)\n    });\nconst dbToAnnouncement = (row)=>({\n        id: row.id,\n        user: row.user_name,\n        title: row.title,\n        description: row.description,\n        type: row.announcement_type,\n        timestamp: new Date(row.timestamp)\n    });\nconst dbToSublet = (row)=>({\n        id: row.id,\n        user: row.user_name,\n        duration: row.duration,\n        timestamp: new Date(row.timestamp)\n    });\nconst dbToHelpMe = (row)=>({\n        id: row.id,\n        user: row.user_name,\n        description: row.description,\n        timestamp: new Date(row.timestamp)\n    });\nconst dbToIncident = (row)=>({\n        id: row.id,\n        machineId: row.machine_id,\n        timestamp: new Date(row.timestamp),\n        type: row.incident_type\n    });\nfunction useSupabaseData() {\n    const [laundry, setLaundry] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [noise, setNoise] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [announcements, setAnnouncements] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [helpMe, setHelpMe] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [incidents, setIncidents] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [hasGraceEndColumn, setHasGraceEndColumn] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    // Refs to prevent recursive calls and multiple setups\n    const isLoadingDataRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const lastLoadTimeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const subscriptionSetupRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const statusManagerSetupRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const isMountedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(true);\n    // Debounced data loading function with selective loading\n    const loadAllData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[loadAllData]\": async (specificTable)=>{\n            const now = Date.now();\n            if (isLoadingDataRef.current || now - lastLoadTimeRef.current < 1000) {\n                console.log(\"⏭️ Skipping data load (too recent or in progress)\");\n                return;\n            }\n            if (!isMountedRef.current) {\n                console.log(\"⏭️ Skipping data load (component unmounted)\");\n                return;\n            }\n            isLoadingDataRef.current = true;\n            lastLoadTimeRef.current = now;\n            try {\n                console.log(`🔄 Loading data from Supabase${specificTable ? ` (${specificTable})` : \"\"}...`);\n                setError(null);\n                // If specific table is provided, only load that table\n                if (specificTable) {\n                    await loadSpecificTable(specificTable);\n                } else {\n                    // Load all data\n                    setIsLoading(true);\n                    await loadAllTables();\n                }\n                console.log(\"✅ Data loaded successfully\");\n            } catch (err) {\n                console.error(\"❌ Error loading data:\", err);\n                if (isMountedRef.current) {\n                    setError(err instanceof Error ? err.message : \"Failed to load data\");\n                }\n            } finally{\n                if (isMountedRef.current && !specificTable) {\n                    setIsLoading(false);\n                }\n                isLoadingDataRef.current = false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[loadAllData]\"], []);\n    // Helper function to load specific table\n    const loadSpecificTable = async (table)=>{\n        switch(table){\n            case \"machines\":\n                const machinesResult = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").select(\"*\").order(\"name\");\n                if (machinesResult.error) throw machinesResult.error;\n                setLaundry(machinesResult.data?.map(dbToMachine) || []);\n                break;\n            case \"noise_reports\":\n                const noiseResult = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"noise_reports\").select(\"*\").order(\"timestamp\", {\n                    ascending: false\n                }).limit(50);\n                if (noiseResult.error) throw noiseResult.error;\n                setNoise(noiseResult.data?.map(dbToNoise) || []);\n                break;\n            case \"announcements\":\n                const announcementsResult = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"announcements\").select(\"*\").order(\"timestamp\", {\n                    ascending: false\n                }).limit(50);\n                if (announcementsResult.error) throw announcementsResult.error;\n                setAnnouncements(announcementsResult.data?.map(dbToAnnouncement) || []);\n                break;\n            case \"help_requests\":\n                const helpResult = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"help_requests\").select(\"*\").gte(\"timestamp\", new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()).order(\"timestamp\", {\n                    ascending: false\n                }).limit(50);\n                if (helpResult.error) throw helpResult.error;\n                setHelpMe(helpResult.data?.map(dbToHelpMe) || []);\n                break;\n            case \"incidents\":\n                const incidentsResult = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"incidents\").select(\"*\").order(\"timestamp\", {\n                    ascending: false\n                }).limit(50);\n                if (incidentsResult.error) throw incidentsResult.error;\n                setIncidents(incidentsResult.data?.map(dbToIncident) || []);\n                break;\n        }\n    };\n    // Helper function to load all tables\n    const loadAllTables = async ()=>{\n        const timeoutPromise = new Promise((_, reject)=>setTimeout(()=>reject(new Error(\"Request timeout\")), 15000));\n        const dataPromise = Promise.all([\n            _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").select(\"*\").order(\"name\"),\n            _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"noise_reports\").select(\"*\").order(\"timestamp\", {\n                ascending: false\n            }).limit(50),\n            _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"announcements\").select(\"*\").order(\"timestamp\", {\n                ascending: false\n            }).limit(50),\n            _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"help_requests\").select(\"*\").gte(\"timestamp\", new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()).order(\"timestamp\", {\n                ascending: false\n            }).limit(50),\n            _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"incidents\").select(\"*\").order(\"timestamp\", {\n                ascending: false\n            }).limit(50)\n        ]);\n        const [machinesResult, noiseResult, announcementsResult, helpResult, incidentsResult] = await Promise.race([\n            dataPromise,\n            timeoutPromise\n        ]);\n        if (!isMountedRef.current) return;\n        if (machinesResult.error) throw new Error(`Failed to load machines: ${machinesResult.error.message}`);\n        if (noiseResult.error) throw new Error(`Failed to load noise reports: ${noiseResult.error.message}`);\n        if (announcementsResult.error) throw new Error(`Failed to load announcements: ${announcementsResult.error.message}`);\n        if (helpResult.error) throw new Error(`Failed to load help requests: ${helpResult.error.message}`);\n        if (incidentsResult.error) throw new Error(`Failed to load incidents: ${incidentsResult.error.message}`);\n        setLaundry(machinesResult.data?.map(dbToMachine) || []);\n        setNoise(noiseResult.data?.map(dbToNoise) || []);\n        setAnnouncements(announcementsResult.data?.map(dbToAnnouncement) || []);\n        setHelpMe(helpResult.data?.map(dbToHelpMe) || []);\n        setIncidents(incidentsResult.data?.map(dbToIncident) || []);\n    };\n    // Set up real-time subscriptions with table-specific updates\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseData.useEffect\": ()=>{\n            if (subscriptionSetupRef.current) {\n                return;\n            }\n            subscriptionSetupRef.current = true;\n            console.log(\"🔄 Setting up subscription manager...\");\n            const subscriptionManager = _src_lib_subscriptionManager__WEBPACK_IMPORTED_MODULE_2__[\"default\"].getInstance();\n            // Enhanced callback that handles specific table updates\n            const handleRealtimeUpdate = {\n                \"useSupabaseData.useEffect.handleRealtimeUpdate\": (table, event)=>{\n                    if (table && table !== \"polling\") {\n                        // Load only the specific table that changed for faster updates\n                        loadAllData(table);\n                    } else {\n                        // Fallback to loading all data\n                        loadAllData();\n                    }\n                }\n            }[\"useSupabaseData.useEffect.handleRealtimeUpdate\"];\n            subscriptionManager.addCallback(handleRealtimeUpdate);\n            return ({\n                \"useSupabaseData.useEffect\": ()=>{\n                    console.log(\"🧹 Cleaning up subscription manager...\");\n                    subscriptionManager.removeCallback(handleRealtimeUpdate);\n                    subscriptionSetupRef.current = false;\n                }\n            })[\"useSupabaseData.useEffect\"];\n        }\n    }[\"useSupabaseData.useEffect\"], [\n        loadAllData\n    ]);\n    // Set up machine status monitoring (only once)\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseData.useEffect\": ()=>{\n            if (statusManagerSetupRef.current) {\n                return;\n            }\n            statusManagerSetupRef.current = true;\n            console.log(\"🔄 Setting up machine status manager...\");\n            const statusManager = _src_lib_machineStatusManager__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getInstance();\n            statusManager.startStatusMonitoring();\n            return ({\n                \"useSupabaseData.useEffect\": ()=>{\n                    console.log(\"🧹 Cleaning up machine status manager...\");\n                    statusManager.stopStatusMonitoring();\n                    statusManagerSetupRef.current = false;\n                }\n            })[\"useSupabaseData.useEffect\"];\n        }\n    }[\"useSupabaseData.useEffect\"], []);\n    // Initial data load and cleanup\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseData.useEffect\": ()=>{\n            isMountedRef.current = true;\n            loadAllData();\n            return ({\n                \"useSupabaseData.useEffect\": ()=>{\n                    isMountedRef.current = false;\n                }\n            })[\"useSupabaseData.useEffect\"];\n        }\n    }[\"useSupabaseData.useEffect\"], []) // Only run once on mount\n    ;\n    // Machine operations with optimistic updates\n    const toggleMachineStatus = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[toggleMachineStatus]\": async (id)=>{\n            try {\n                const machine = laundry.find({\n                    \"useSupabaseData.useCallback[toggleMachineStatus].machine\": (m)=>m.id === id\n                }[\"useSupabaseData.useCallback[toggleMachineStatus].machine\"]);\n                if (!machine) {\n                    return false;\n                }\n                let optimisticUpdate;\n                let updateData;\n                if (machine.status === \"free\") {\n                    const startAt = new Date();\n                    const endAt = new Date(startAt.getTime() + 60 * 60 * 1000);\n                    optimisticUpdate = {\n                        ...machine,\n                        status: \"running\",\n                        startAt,\n                        endAt,\n                        graceEndAt: undefined,\n                        updatedAt: new Date()\n                    };\n                    updateData = {\n                        status: \"running\",\n                        start_at: startAt.toISOString(),\n                        end_at: endAt.toISOString()\n                    };\n                    if (hasGraceEndColumn) {\n                        updateData.grace_end_at = null;\n                    }\n                } else {\n                    optimisticUpdate = {\n                        ...machine,\n                        status: \"free\",\n                        startAt: undefined,\n                        endAt: undefined,\n                        graceEndAt: undefined,\n                        updatedAt: new Date()\n                    };\n                    updateData = {\n                        status: \"free\",\n                        start_at: null,\n                        end_at: null\n                    };\n                    if (hasGraceEndColumn) {\n                        updateData.grace_end_at = null;\n                    }\n                }\n                // Apply optimistic update\n                setLaundry({\n                    \"useSupabaseData.useCallback[toggleMachineStatus]\": (prev)=>prev.map({\n                            \"useSupabaseData.useCallback[toggleMachineStatus]\": (m)=>m.id === id ? optimisticUpdate : m\n                        }[\"useSupabaseData.useCallback[toggleMachineStatus]\"])\n                }[\"useSupabaseData.useCallback[toggleMachineStatus]\"]);\n                // Send update to database\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").update(updateData).eq(\"id\", id);\n                if (error) {\n                    if (error.message && error.message.includes(\"grace_end_at\")) {\n                        setHasGraceEndColumn(false);\n                        delete updateData.grace_end_at;\n                        const { error: retryError } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").update(updateData).eq(\"id\", id);\n                        if (retryError) {\n                            setLaundry({\n                                \"useSupabaseData.useCallback[toggleMachineStatus]\": (prev)=>prev.map({\n                                        \"useSupabaseData.useCallback[toggleMachineStatus]\": (m)=>m.id === id ? machine : m\n                                    }[\"useSupabaseData.useCallback[toggleMachineStatus]\"])\n                            }[\"useSupabaseData.useCallback[toggleMachineStatus]\"]);\n                            throw retryError;\n                        }\n                    } else {\n                        setLaundry({\n                            \"useSupabaseData.useCallback[toggleMachineStatus]\": (prev)=>prev.map({\n                                    \"useSupabaseData.useCallback[toggleMachineStatus]\": (m)=>m.id === id ? machine : m\n                                }[\"useSupabaseData.useCallback[toggleMachineStatus]\"])\n                        }[\"useSupabaseData.useCallback[toggleMachineStatus]\"]);\n                        throw error;\n                    }\n                }\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to update machine\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[toggleMachineStatus]\"], [\n        laundry,\n        hasGraceEndColumn\n    ]);\n    const reserveMachine = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[reserveMachine]\": async (id, durationStr)=>{\n            try {\n                const machine = laundry.find({\n                    \"useSupabaseData.useCallback[reserveMachine].machine\": (m)=>m.id === id\n                }[\"useSupabaseData.useCallback[reserveMachine].machine\"]);\n                if (!machine) {\n                    return false;\n                }\n                const durationSeconds = (0,_src_utils_parseDuration__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(durationStr);\n                const startAt = new Date();\n                const endAt = new Date(startAt.getTime() + durationSeconds * 1000);\n                const optimisticUpdate = {\n                    ...machine,\n                    status: \"running\",\n                    startAt,\n                    endAt,\n                    graceEndAt: undefined,\n                    updatedAt: new Date()\n                };\n                setLaundry({\n                    \"useSupabaseData.useCallback[reserveMachine]\": (prev)=>prev.map({\n                            \"useSupabaseData.useCallback[reserveMachine]\": (m)=>m.id === id ? optimisticUpdate : m\n                        }[\"useSupabaseData.useCallback[reserveMachine]\"])\n                }[\"useSupabaseData.useCallback[reserveMachine]\"]);\n                const updateData = {\n                    status: \"running\",\n                    start_at: startAt.toISOString(),\n                    end_at: endAt.toISOString()\n                };\n                if (hasGraceEndColumn) {\n                    updateData.grace_end_at = null;\n                }\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").update(updateData).eq(\"id\", id);\n                if (error) {\n                    if (error.message && error.message.includes(\"grace_end_at\")) {\n                        setHasGraceEndColumn(false);\n                        delete updateData.grace_end_at;\n                        const { error: retryError } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").update(updateData).eq(\"id\", id);\n                        if (retryError) {\n                            setLaundry({\n                                \"useSupabaseData.useCallback[reserveMachine]\": (prev)=>prev.map({\n                                        \"useSupabaseData.useCallback[reserveMachine]\": (m)=>m.id === id ? machine : m\n                                    }[\"useSupabaseData.useCallback[reserveMachine]\"])\n                            }[\"useSupabaseData.useCallback[reserveMachine]\"]);\n                            throw retryError;\n                        }\n                    } else {\n                        setLaundry({\n                            \"useSupabaseData.useCallback[reserveMachine]\": (prev)=>prev.map({\n                                    \"useSupabaseData.useCallback[reserveMachine]\": (m)=>m.id === id ? machine : m\n                                }[\"useSupabaseData.useCallback[reserveMachine]\"])\n                        }[\"useSupabaseData.useCallback[reserveMachine]\"]);\n                        throw error;\n                    }\n                }\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to reserve machine\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[reserveMachine]\"], [\n        laundry,\n        hasGraceEndColumn\n    ]);\n    // Simplified CRUD operations\n    const addNoiseWithDescription = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[addNoiseWithDescription]\": async (user, description)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"noise_reports\").insert({\n                    id: Date.now().toString(),\n                    user_name: user,\n                    description: description\n                });\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to add noise report\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[addNoiseWithDescription]\"], []);\n    const deleteNoise = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[deleteNoise]\": async (id)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"noise_reports\").delete().eq(\"id\", id);\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to delete noise report\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[deleteNoise]\"], []);\n    const addAnnouncement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[addAnnouncement]\": async (user, title, description, type)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"announcements\").insert({\n                    id: Date.now().toString(),\n                    user_name: user,\n                    title: title,\n                    description: description,\n                    announcement_type: type\n                });\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to add announcement\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[addAnnouncement]\"], []);\n    const deleteAnnouncement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[deleteAnnouncement]\": async (id)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"announcements\").delete().eq(\"id\", id);\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to delete announcement\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[deleteAnnouncement]\"], []);\n    const addHelpRequest = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[addHelpRequest]\": async (user, description)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"help_requests\").insert({\n                    id: Date.now().toString(),\n                    user_name: user,\n                    description\n                });\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to add help request\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[addHelpRequest]\"], []);\n    const deleteHelpRequest = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[deleteHelpRequest]\": async (id)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"help_requests\").delete().eq(\"id\", id);\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to delete help request\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[deleteHelpRequest]\"], []);\n    const deleteIncident = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[deleteIncident]\": async (id)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"incidents\").delete().eq(\"id\", id);\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to delete incident\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[deleteIncident]\"], []);\n    return {\n        // Data\n        laundry,\n        noise,\n        announcements,\n        helpMe,\n        incidents,\n        // State\n        isLoading,\n        error,\n        // Operations\n        toggleMachineStatus,\n        reserveMachine,\n        addNoiseWithDescription,\n        deleteNoise,\n        addAnnouncement,\n        deleteAnnouncement,\n        addHelpRequest,\n        deleteHelpRequest,\n        deleteIncident,\n        refreshData: loadAllData\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useSupabaseData.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/machineStatusManager.ts":
/*!*****************************************!*\
  !*** ./src/lib/machineStatusManager.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(ssr)/./src/lib/supabase.ts\");\n\nclass MachineStatusManager {\n    static getInstance() {\n        if (!MachineStatusManager.instance) {\n            MachineStatusManager.instance = new MachineStatusManager();\n        }\n        return MachineStatusManager.instance;\n    }\n    startStatusMonitoring() {\n        if (this.statusCheckInterval || this.isDestroyed) {\n            return;\n        }\n        console.log(\"🔄 Starting machine status monitoring...\");\n        // Check every 30 seconds (less aggressive)\n        this.statusCheckInterval = setInterval(()=>{\n            if (!this.isDestroyed) {\n                this.checkMachineStatuses();\n            }\n        }, 30000);\n        // Also check immediately\n        this.checkMachineStatuses();\n    }\n    stopStatusMonitoring() {\n        if (this.statusCheckInterval) {\n            clearInterval(this.statusCheckInterval);\n            this.statusCheckInterval = null;\n            console.log(\"⏹️ Stopped machine status monitoring\");\n        }\n    }\n    destroy() {\n        this.isDestroyed = true;\n        this.stopStatusMonitoring();\n    }\n    async checkMachineStatuses() {\n        if (this.isChecking || this.isDestroyed) {\n            return;\n        }\n        this.isChecking = true;\n        try {\n            const now = new Date();\n            const { data: machines, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"machines\").select(\"*\").in(\"status\", [\n                \"running\",\n                \"finishedGrace\"\n            ]);\n            if (error) {\n                console.error(\"❌ Error fetching machines for status check:\", error);\n                return;\n            }\n            if (!machines || machines.length === 0) {\n                return;\n            }\n            const updates = [];\n            for (const machine of machines){\n                if (this.isDestroyed) break;\n                const endAt = machine.end_at ? new Date(machine.end_at) : null;\n                let graceEndAt = null;\n                if (this.hasGraceEndColumn && machine.grace_end_at) {\n                    graceEndAt = new Date(machine.grace_end_at);\n                }\n                if (machine.status === \"running\" && endAt && now >= endAt) {\n                    const graceEnd = new Date(now.getTime() + 5 * 60 * 1000);\n                    const updateObj = {\n                        status: \"finishedGrace\"\n                    };\n                    if (this.hasGraceEndColumn) {\n                        updateObj.grace_end_at = graceEnd.toISOString();\n                    }\n                    updates.push({\n                        id: machine.id,\n                        updates: updateObj\n                    });\n                    console.log(`⚠️ Machine ${machine.name} transitioning to grace period`);\n                } else if (machine.status === \"finishedGrace\") {\n                    const defaultGraceEnd = endAt ? new Date(endAt.getTime() + 5 * 60 * 1000) : null;\n                    const effectiveGraceEnd = graceEndAt || defaultGraceEnd;\n                    if (effectiveGraceEnd && now >= effectiveGraceEnd) {\n                        updates.push({\n                            id: machine.id,\n                            updates: {\n                                status: \"free\",\n                                start_at: null,\n                                end_at: null,\n                                ...this.hasGraceEndColumn ? {\n                                    grace_end_at: null\n                                } : {}\n                            }\n                        });\n                        console.log(`✅ Machine ${machine.name} grace period ended, now available`);\n                    }\n                }\n            }\n            // Apply updates sequentially to avoid conflicts\n            for (const update of updates){\n                if (this.isDestroyed) break;\n                try {\n                    const { error: updateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"machines\").update(update.updates).eq(\"id\", update.id);\n                    if (updateError) {\n                        if (updateError.message && updateError.message.includes(\"grace_end_at\")) {\n                            console.log(\"⚠️ grace_end_at column not found, disabling this feature\");\n                            this.hasGraceEndColumn = false;\n                        }\n                        console.error(`❌ Error updating machine ${update.id}:`, updateError);\n                    }\n                } catch (err) {\n                    console.error(`❌ Error updating machine ${update.id}:`, err);\n                }\n                // Small delay between updates to prevent overwhelming the database\n                await new Promise((resolve)=>setTimeout(resolve, 100));\n            }\n            if (updates.length > 0) {\n                console.log(`🔄 Updated ${updates.length} machine statuses`);\n            }\n        } catch (error) {\n            console.error(\"❌ Error in machine status check:\", error);\n        } finally{\n            this.isChecking = false;\n        }\n    }\n    async triggerStatusCheck() {\n        if (!this.isDestroyed) {\n            await this.checkMachineStatuses();\n        }\n    }\n    constructor(){\n        this.statusCheckInterval = null;\n        this.isChecking = false;\n        this.hasGraceEndColumn = true;\n        this.isDestroyed = false;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MachineStatusManager);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/machineStatusManager.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/subscriptionManager.ts":
/*!****************************************!*\
  !*** ./src/lib/subscriptionManager.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(ssr)/./src/lib/supabase.ts\");\n\nclass SubscriptionManager {\n    static getInstance() {\n        if (!SubscriptionManager.instance) {\n            SubscriptionManager.instance = new SubscriptionManager();\n        }\n        return SubscriptionManager.instance;\n    }\n    addCallback(callback) {\n        if (this.isDestroyed) return;\n        this.callbacks.add(callback);\n        console.log(`📡 Added callback, total: ${this.callbacks.size}`);\n        if (this.callbacks.size === 1 && !this.isSubscribed) {\n            this.initializeSubscription();\n        }\n    }\n    removeCallback(callback) {\n        this.callbacks.delete(callback);\n        console.log(`📡 Removed callback, total: ${this.callbacks.size}`);\n        if (this.callbacks.size === 0) {\n            this.cleanup();\n        }\n    }\n    async initializeSubscription() {\n        if (this.isSubscribed || this.channel || this.isDestroyed) {\n            return;\n        }\n        console.log(\"🔄 Initializing Supabase subscription...\");\n        this.isSubscribed = true;\n        this.subscriptionId++;\n        try {\n            const channelName = `dorm-dashboard-${this.subscriptionId}-${Date.now()}`;\n            this.channel = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.channel(channelName, {\n                config: {\n                    broadcast: {\n                        self: false\n                    },\n                    presence: {\n                        key: \"\"\n                    }\n                }\n            }).on(\"postgres_changes\", {\n                event: \"*\",\n                schema: \"public\",\n                table: \"machines\"\n            }, (payload)=>{\n                this.handleChange(\"machines\", payload.eventType);\n            }).on(\"postgres_changes\", {\n                event: \"*\",\n                schema: \"public\",\n                table: \"noise_reports\"\n            }, (payload)=>{\n                this.handleChange(\"noise_reports\", payload.eventType);\n            }).on(\"postgres_changes\", {\n                event: \"*\",\n                schema: \"public\",\n                table: \"announcements\"\n            }, (payload)=>{\n                this.handleChange(\"announcements\", payload.eventType);\n            }).on(\"postgres_changes\", {\n                event: \"*\",\n                schema: \"public\",\n                table: \"help_requests\"\n            }, (payload)=>{\n                this.handleChange(\"help_requests\", payload.eventType);\n            }).on(\"postgres_changes\", {\n                event: \"*\",\n                schema: \"public\",\n                table: \"incidents\"\n            }, (payload)=>{\n                this.handleChange(\"incidents\", payload.eventType);\n            }).subscribe((status)=>{\n                console.log(`📡 Subscription status: ${status}`);\n                if (status === \"SUBSCRIBED\") {\n                    console.log(\"✅ Successfully subscribed to realtime updates\");\n                } else if (status === \"CHANNEL_ERROR\" || status === \"TIMED_OUT\" || status === \"CLOSED\") {\n                    console.log(\"❌ Subscription error, will retry...\");\n                    this.handleSubscriptionError();\n                }\n            });\n            // Reduced polling frequency\n            this.startPolling();\n        } catch (error) {\n            console.error(\"❌ Error initializing subscription:\", error);\n            this.handleSubscriptionError();\n        }\n    }\n    notifyCallbacks(table, event) {\n        if (this.isDestroyed) return;\n        // Use setTimeout to prevent stack overflow and allow for immediate UI updates\n        setTimeout(()=>{\n            if (this.isDestroyed) return;\n            this.callbacks.forEach((callback)=>{\n                try {\n                    callback(table, event);\n                } catch (error) {\n                    console.error(\"❌ Error in subscription callback:\", error);\n                }\n            });\n        }, 50) // Reduced delay for faster updates\n        ;\n    }\n    startPolling() {\n        if (this.pollingInterval || this.isDestroyed) {\n            return;\n        }\n        // Polling every 60 seconds as fallback\n        this.pollingInterval = setInterval(()=>{\n            if (this.isDestroyed) return;\n            console.log(\"🔄 Polling for changes...\");\n            this.notifyCallbacks(\"polling\", \"fallback\");\n        }, 60000);\n    }\n    handleSubscriptionError() {\n        if (this.isDestroyed) return;\n        this.cleanup();\n        setTimeout(()=>{\n            if (this.callbacks.size > 0 && !this.isDestroyed) {\n                console.log(\"🔄 Retrying subscription...\");\n                this.initializeSubscription();\n            }\n        }, 3000);\n    }\n    cleanup() {\n        console.log(\"🧹 Cleaning up subscription manager...\");\n        this.isSubscribed = false;\n        if (this.channel) {\n            try {\n                this.channel.unsubscribe();\n                _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.removeChannel(this.channel);\n            } catch (error) {\n                console.warn(\"⚠️ Error during channel cleanup:\", error);\n            }\n            this.channel = null;\n        }\n        if (this.pollingInterval) {\n            clearInterval(this.pollingInterval);\n            this.pollingInterval = null;\n        }\n    }\n    forceCleanup() {\n        console.log(\"🧹 Force cleaning up subscription manager...\");\n        this.isDestroyed = true;\n        this.callbacks.clear();\n        this.cleanup();\n    }\n    triggerSync() {\n        if (this.isDestroyed) return;\n        this.notifyCallbacks(\"manual\", \"trigger\");\n    }\n    constructor(){\n        this.channel = null;\n        this.callbacks = new Set();\n        this.isSubscribed = false;\n        this.pollingInterval = null;\n        this.isDestroyed = false;\n        this.subscriptionId = 0;\n        this.lastChangeTime = 0;\n        this.handleChange = (table, event)=>{\n            if (this.isDestroyed) return;\n            const now = Date.now();\n            // Debounce rapid changes (max once per 500ms)\n            if (now - this.lastChangeTime < 500) {\n                return;\n            }\n            this.lastChangeTime = now;\n            console.log(`📡 Database change detected: ${table} - ${event}`);\n            this.notifyCallbacks(table, event);\n        };\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SubscriptionManager);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/subscriptionManager.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   testSupabaseConnection: () => (/* binding */ testSupabaseConnection)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.0/node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://aodjyjxsqfytythosrka.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFvZGp5anhzcWZ5dHl0aG9zcmthIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1MDYzOTYsImV4cCI6MjA2NTA4MjM5Nn0.V0K-uTAl8FDkupkjYZn9R6P4qIMhJ0kX1iE4LQFr_tg\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n    realtime: {\n        params: {\n            eventsPerSecond: 50\n        }\n    },\n    auth: {\n        persistSession: false\n    }\n});\n// Test connection function\nasync function testSupabaseConnection() {\n    try {\n        const { data, error } = await supabase.from(\"machines\").select(\"count\").single();\n        if (error) throw error;\n        return {\n            success: true,\n            data\n        };\n    } catch (error) {\n        return {\n            success: false,\n            error\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/parseDuration.tsx":
/*!*************************************!*\
  !*** ./src/utils/parseDuration.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ parseDuration)\n/* harmony export */ });\nfunction parseDuration(durationStr) {\n    const trimmed = durationStr.trim().toLowerCase();\n    // Match patterns like \"1 hr 30 min\", \"2 hours\", \"45 minutes\", etc.\n    const hourMatch = trimmed.match(/(\\d+)\\s*(hr|hour|hours)/);\n    const minuteMatch = trimmed.match(/(\\d+)\\s*(min|minute|minutes)/);\n    let totalSeconds = 0;\n    if (hourMatch) {\n        totalSeconds += Number.parseInt(hourMatch[1]) * 3600;\n    }\n    if (minuteMatch) {\n        totalSeconds += Number.parseInt(minuteMatch[1]) * 60;\n    }\n    // If no valid time found, throw error\n    if (totalSeconds === 0) {\n        throw new Error(`Invalid duration format: ${durationStr}`);\n    }\n    return totalSeconds;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/parseDuration.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?8bfc":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?cf7b":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0","vendor-chunks/tr46@0.0.3","vendor-chunks/@supabase+auth-js@2.70.0","vendor-chunks/ws@8.18.2","vendor-chunks/@supabase+realtime-js@2.11.10","vendor-chunks/@supabase+postgrest-js@1.19.4","vendor-chunks/@supabase+node-fetch@2.6.15","vendor-chunks/whatwg-url@5.0.0","vendor-chunks/@supabase+storage-js@2.7.1","vendor-chunks/@supabase+supabase-js@2.50.0","vendor-chunks/@supabase+functions-js@2.4.4","vendor-chunks/webidl-conversions@3.0.1","vendor-chunks/@swc+helpers@0.5.15","vendor-chunks/qrcode.react@4.2.0_react@19.0.0"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fcheckin%2Fpage&page=%2Fcheckin%2Fpage&appPaths=%2Fcheckin%2Fpage&pagePath=private-next-app-dir%2Fcheckin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmemet%5CDesktop%5Cdorm_21%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmemet%5CDesktop%5Cdorm_21&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();