/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"59e27c0181b7\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbWVtZXRcXERlc2t0b3BcXGRvcm1fMjFcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI1OWUyN2MwMTgxYjdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\nconst metadata = {\n    title: 'Dorm Dashboard',\n    description: 'Dorm 21 Management Dashboard',\n    generator: 'v0.dev'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"min-h-screen bg-bgLight text-gray-800 font-sans\",\n            suppressHydrationWarning: true,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"bg-white shadow-sm p-4 border-b border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl font-bold text-primary\",\n                            children: \"\\uD83C\\uDFE0 Dorm Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\layout.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\layout.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\layout.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"container mx-auto py-8\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\layout.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                    className: \"text-center text-sm text-gray-500 py-4 border-t border-gray-200 bg-white\",\n                    children: \"dorm21\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\layout.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\dorm_21\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmemet%5CDesktop%5Cdorm_21%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmemet%5CDesktop%5Cdorm_21&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmemet%5CDesktop%5Cdorm_21%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmemet%5CDesktop%5Cdorm_21&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/module.compiled.js?4602\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmemet%5CDesktop%5Cdorm_21%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmemet%5CDesktop%5Cdorm_21&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4yLjRfcmVhY3QtZG9tQDE5LjAuMF9yZWFjdEAxOS4wLjBfX3JlYWN0QDE5LjAuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21lbWV0JTVDJTVDRGVza3RvcCU1QyU1Q2Rvcm1fMjElNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0lBQXNGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxtZW1ldFxcXFxEZXNrdG9wXFxcXGRvcm1fMjFcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Page)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _src_components_DashboardGrid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/src/components/DashboardGrid */ \"(ssr)/./src/components/DashboardGrid.tsx\");\n/* harmony import */ var _src_components_LaundryCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/components/LaundryCard */ \"(ssr)/./src/components/LaundryCard.tsx\");\n/* harmony import */ var _src_components_NoiseCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/src/components/NoiseCard */ \"(ssr)/./src/components/NoiseCard.tsx\");\n/* harmony import */ var _src_components_AnnouncementsCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/src/components/AnnouncementsCard */ \"(ssr)/./src/components/AnnouncementsCard.tsx\");\n/* harmony import */ var _src_components_HelpMeCard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/src/components/HelpMeCard */ \"(ssr)/./src/components/HelpMeCard.tsx\");\n/* harmony import */ var _src_components_ConnectionTest__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/src/components/ConnectionTest */ \"(ssr)/./src/components/ConnectionTest.tsx\");\n/* harmony import */ var _src_components_DebugPanel__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/src/components/DebugPanel */ \"(ssr)/./src/components/DebugPanel.tsx\");\n/* harmony import */ var _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/src/hooks/useSupabaseData */ \"(ssr)/./src/hooks/useSupabaseData.tsx\");\n/* harmony import */ var _src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/src/utils/userIdentification */ \"(ssr)/./src/utils/userIdentification.ts\");\n/* harmony import */ var _src_components_UserSettings__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/src/components/UserSettings */ \"(ssr)/./src/components/UserSettings.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\nfunction Page() {\n    const { refreshData, isLoading, error } = (0,_src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_9__[\"default\"])();\n    const [userName, setUserName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"User\");\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showUserSettings, setShowUserSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Handle client-side hydration\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Page.useEffect\": ()=>{\n            setIsClient(true);\n            setUserName((0,_src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_10__.getUserDisplayName)());\n        }\n    }[\"Page.useEffect\"], []);\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-bgLight flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-8 rounded-lg shadow-lg text-center max-w-md border border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-warn text-lg mb-4 font-semibold\",\n                        children: \"Connection Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"Unable to connect to the database. Please try again later.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: refreshData,\n                        className: \"bg-accent hover:bg-teal-600 text-white px-6 py-2 rounded-lg font-medium transition-colors\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                lineNumber: 30,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8 px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 text-lg\",\n                                children: [\n                                    \"Welcome back, \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-semibold text-accent\",\n                                        children: userName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 31\n                                    }, this),\n                                    \"!\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-4 h-4 border-2 border-accent border-t-transparent rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"Updating...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowUserSettings(true),\n                                    className: \"px-4 py-2 rounded-lg text-sm text-primary border border-gray-300 hover:bg-gray-50 shadow-sm transition-colors\",\n                                    children: \"⚙️ Settings\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: refreshData,\n                                    disabled: isLoading,\n                                    className: `px-4 py-2 rounded-lg text-sm text-white font-medium transition-all ${isLoading ? \"bg-gray-400 cursor-not-allowed\" : \"bg-accent hover:bg-teal-600 shadow-md hover:shadow-lg\"}`,\n                                    children: \"\\uD83D\\uDD04 Refresh\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_ConnectionTest__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_DashboardGrid__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_LaundryCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, \"laundry-machines\", false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_HelpMeCard__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, \"help-requests\", false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_AnnouncementsCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, \"community-board\", false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_NoiseCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, \"noise-reports\", false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_UserSettings__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: showUserSettings,\n                onClose: ()=>setShowUserSettings(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n             true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_DebugPanel__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n                lineNumber: 96,\n                columnNumber: 50\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\page.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \*************************************************************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4yLjRfcmVhY3QtZG9tQDE5LjAuMF9yZWFjdEAxOS4wLjBfX3JlYWN0QDE5LjAuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21lbWV0JTVDJTVDRGVza3RvcCU1QyU1Q2Rvcm1fMjElNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0lBQXNGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxtZW1ldFxcXFxEZXNrdG9wXFxcXGRvcm1fMjFcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmemet%5C%5CDesktop%5C%5Cdorm_21%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/AnnouncementsCard.tsx":
/*!**********************************************!*\
  !*** ./src/components/AnnouncementsCard.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnnouncementsCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/src/hooks/useSupabaseData */ \"(ssr)/./src/hooks/useSupabaseData.tsx\");\n/* harmony import */ var _src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/utils/userIdentification */ \"(ssr)/./src/utils/userIdentification.ts\");\n/* harmony import */ var _ui_CardWrapper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/CardWrapper */ \"(ssr)/./src/components/ui/CardWrapper.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst ANNOUNCEMENT_TYPES = [\n    {\n        value: \"sublet\",\n        label: \"🏠 Sublet\",\n        color: \"bg-blue-100 text-blue-800\"\n    },\n    {\n        value: \"selling\",\n        label: \"💰 Selling\",\n        color: \"bg-green-100 text-green-800\"\n    },\n    {\n        value: \"free\",\n        label: \"🎁 Free Stuff\",\n        color: \"bg-purple-100 text-purple-800\"\n    },\n    {\n        value: \"wanted\",\n        label: \"🔍 Looking For\",\n        color: \"bg-orange-100 text-orange-800\"\n    },\n    {\n        value: \"event\",\n        label: \"🎉 Event\",\n        color: \"bg-pink-100 text-pink-800\"\n    },\n    {\n        value: \"general\",\n        label: \"📢 General\",\n        color: \"bg-gray-100 text-gray-800\"\n    }\n];\nfunction AnnouncementsCard() {\n    const { announcements, addAnnouncement, deleteAnnouncement } = (0,_src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [description, setDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [type, setType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"general\");\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showForm, setShowForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnnouncementsCard.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"AnnouncementsCard.useEffect\"], []);\n    const handlePostAnnouncement = async ()=>{\n        if (!title.trim() || !description.trim() || isSubmitting || !isClient) return;\n        setIsSubmitting(true);\n        const user = (0,_src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_3__.getUserDisplayName)();\n        const success = await addAnnouncement(user, title.trim(), description.trim(), type);\n        if (success) {\n            setTitle(\"\");\n            setDescription(\"\");\n            setType(\"general\");\n            setShowForm(false);\n        }\n        setIsSubmitting(false);\n    };\n    const handleCancel = ()=>{\n        setShowForm(false);\n        setTitle(\"\");\n        setDescription(\"\");\n        setType(\"general\");\n    };\n    const handleDelete = async (id)=>{\n        if (confirm(\"Are you sure you want to delete this announcement?\")) {\n            await deleteAnnouncement(id);\n        }\n    };\n    const getTypeInfo = (typeValue)=>{\n        return ANNOUNCEMENT_TYPES.find((t)=>t.value === typeValue) || ANNOUNCEMENT_TYPES[5];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_CardWrapper__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        color: \"bgDark\",\n        className: \"border-l-4 border-accent h-full\",\n        count: announcements.length,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-3 h-3 bg-accent rounded-full mr-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-primary\",\n                        children: \"\\uD83D\\uDCCB Community Board\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this),\n            !showForm ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setShowForm(true),\n                disabled: !isClient,\n                className: `w-full p-3 rounded-lg mb-4 font-medium transition-all duration-200 ${!isClient ? \"bg-gray-300 text-gray-500 cursor-not-allowed\" : \"bg-accent hover:bg-teal-600 text-white shadow-md hover:shadow-lg transform hover:scale-[1.02]\"}`,\n                children: !isClient ? \"Loading...\" : \"➕ Post Announcement\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                lineNumber: 73,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 bg-white p-4 rounded-lg border border-gray-200 shadow-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium text-primary block mb-1\",\n                                        children: \"Category:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: type,\n                                        onChange: (e)=>setType(e.target.value),\n                                        className: \"w-full border border-gray-300 rounded px-3 py-2 focus:ring-2 focus:ring-accent focus:border-accent\",\n                                        disabled: isSubmitting,\n                                        children: ANNOUNCEMENT_TYPES.map((typeOption)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: typeOption.value,\n                                                children: typeOption.label\n                                            }, typeOption.value, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium text-primary block mb-1\",\n                                        children: \"Title:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: title,\n                                        onChange: (e)=>setTitle(e.target.value),\n                                        placeholder: \"Enter a title...\",\n                                        className: \"w-full border border-gray-300 rounded px-3 py-2 focus:ring-2 focus:ring-accent focus:border-accent\",\n                                        disabled: isSubmitting,\n                                        maxLength: 100\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: [\n                                            title.length,\n                                            \"/100 characters\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium text-primary block mb-1\",\n                                        children: \"Description:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        value: description,\n                                        onChange: (e)=>setDescription(e.target.value),\n                                        placeholder: \"Provide details...\",\n                                        className: \"w-full border border-gray-300 rounded px-3 py-2 focus:ring-2 focus:ring-accent focus:border-accent h-20 resize-none\",\n                                        disabled: isSubmitting,\n                                        maxLength: 500\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: [\n                                            description.length,\n                                            \"/500 characters\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2 mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handlePostAnnouncement,\n                                disabled: isSubmitting || !title.trim() || !description.trim() || !isClient,\n                                className: `flex-1 p-2 rounded-lg font-medium transition-colors ${isSubmitting || !title.trim() || !description.trim() || !isClient ? \"bg-gray-300 text-gray-500 cursor-not-allowed\" : \"bg-accent hover:bg-teal-600 text-white\"}`,\n                                children: isSubmitting ? \"Posting...\" : \"Post\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCancel,\n                                disabled: isSubmitting,\n                                className: \"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\",\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                lineNumber: 85,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3 max-h-64 overflow-y-auto\",\n                children: [\n                    announcements.map((entry)=>{\n                        const typeInfo = getTypeInfo(entry.type);\n                        const canDelete = isClient && (0,_src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_3__.isCurrentUserPost)(entry.user);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white border border-gray-200 rounded-lg p-3 shadow-sm hover:shadow-md transition-shadow\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-start mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 flex-wrap\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `text-xs px-2 py-1 rounded-full ${typeInfo.color}`,\n                                                    children: typeInfo.label\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 19\n                                                }, this),\n                                                canDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs bg-accent/10 text-accent px-2 py-1 rounded-full\",\n                                                    children: \"Your post\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 17\n                                        }, this),\n                                        canDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleDelete(entry.id),\n                                            className: \"text-warn text-xs hover:text-red-700 px-2 py-1 hover:bg-red-50 rounded transition-colors\",\n                                            title: \"Delete your post\",\n                                            children: \"Delete\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-medium text-primary mb-1\",\n                                    children: entry.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-700 mb-2\",\n                                    children: entry.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center text-xs text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: entry.user\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: entry.timestamp.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, entry.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 13\n                        }, this);\n                    }),\n                    announcements.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-gray-600 py-4\",\n                        children: \"No announcements yet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 40\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\AnnouncementsCard.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AnnouncementsCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ConnectionTest.tsx":
/*!*******************************************!*\
  !*** ./src/components/ConnectionTest.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ConnectionTest)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _src_lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/src/lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ConnectionTest() {\n    const [connectionStatus, setConnectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"testing\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConnectionTest.useEffect\": ()=>{\n            let isMounted = true;\n            const testConnection = {\n                \"ConnectionTest.useEffect.testConnection\": async ()=>{\n                    try {\n                        // Test basic connection by fetching machines\n                        const { data, error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"machines\").select(\"*\").limit(10);\n                        if (error) {\n                            throw error;\n                        }\n                        if (!isMounted) return;\n                        setConnectionStatus(\"connected\");\n                    } catch (err) {\n                        if (!isMounted) return;\n                        setError(err instanceof Error ? err.message : \"Unknown error\");\n                        setConnectionStatus(\"error\");\n                    }\n                }\n            }[\"ConnectionTest.useEffect.testConnection\"];\n            testConnection();\n            return ({\n                \"ConnectionTest.useEffect\": ()=>{\n                    isMounted = false;\n                }\n            })[\"ConnectionTest.useEffect\"];\n        }\n    }[\"ConnectionTest.useEffect\"], []);\n    if (connectionStatus === \"testing\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\ConnectionTest.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-blue-800\",\n                        children: \"Connecting to database...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\ConnectionTest.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\ConnectionTest.tsx\",\n                lineNumber: 41,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\ConnectionTest.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, this);\n    }\n    if (connectionStatus === \"error\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-red-800\",\n                children: \"Unable to connect to database. Please try again later.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\ConnectionTest.tsx\",\n                lineNumber: 52,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\ConnectionTest.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this);\n    }\n    // If connected, don't show anything to end users\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ConnectionTest.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/DashboardGrid.tsx":
/*!******************************************!*\
  !*** ./src/components/DashboardGrid.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardGrid)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction DashboardGrid({ children }) {\n    const childrenArray = react__WEBPACK_IMPORTED_MODULE_1___default().Children.toArray(children);\n    // Debug: Let's see what we're working with\n    console.log(\"All children:\", childrenArray.map((child)=>({\n            type: child.type?.name,\n            displayName: child.type?.displayName,\n            props: child.props\n        })));\n    // More robust filtering - check multiple ways to identify LaundryCard\n    const laundryCard = childrenArray.find((child)=>{\n        const isLaundryCard = child.type?.name === \"LaundryCard\" || child.type?.displayName === \"LaundryCard\" || typeof child.type === \"function\" && child.type.name === \"LaundryCard\" || child.key && child.key.includes(\"laundry\") || child.props && Object.keys(child.props).length === 0 && child.type?.name?.includes(\"Laundry\");\n        console.log(\"Checking child for laundry:\", {\n            typeName: child.type?.name,\n            displayName: child.type?.displayName,\n            isFunction: typeof child.type === \"function\",\n            functionName: typeof child.type === \"function\" ? child.type.name : null,\n            key: child.key,\n            isLaundryCard\n        });\n        return isLaundryCard;\n    });\n    const otherCards = childrenArray.filter((child)=>{\n        const isNotLaundryCard = !(child.type?.name === \"LaundryCard\" || child.type?.displayName === \"LaundryCard\" || typeof child.type === \"function\" && child.type.name === \"LaundryCard\" || child.key && child.key.includes(\"laundry\") || child.props && Object.keys(child.props).length === 0 && child.type?.name?.includes(\"Laundry\"));\n        return isNotLaundryCard;\n    });\n    console.log(\"Filtered results:\", {\n        laundryCard: !!laundryCard,\n        otherCardsCount: otherCards.length\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full max-w-7xl mx-auto px-4\",\n        children: [\n            laundryCard ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full mb-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-br from-bgLight to-white rounded-2xl p-2 shadow-sm\",\n                    children: laundryCard\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\DashboardGrid.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\DashboardGrid.tsx\",\n                lineNumber: 61,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full mb-12 p-8 bg-red-100 border border-red-300 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-700\",\n                    children: \"⚠️ LaundryCard not found! Check component filtering logic.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\DashboardGrid.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\DashboardGrid.tsx\",\n                lineNumber: 65,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                children: otherCards\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\DashboardGrid.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\DashboardGrid.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/DashboardGrid.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/DebugPanel.tsx":
/*!***************************************!*\
  !*** ./src/components/DebugPanel.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DebugPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _src_lib_subscriptionManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/src/lib/subscriptionManager */ \"(ssr)/./src/lib/subscriptionManager.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction DebugPanel() {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleForceCleanup = ()=>{\n        console.log(\"🧹 Force cleaning up subscriptions...\");\n        const manager = _src_lib_subscriptionManager__WEBPACK_IMPORTED_MODULE_2__[\"default\"].getInstance();\n        manager.forceCleanup();\n        alert(\"Subscriptions cleaned up! Refresh the page to restart.\");\n    };\n    if (!isOpen) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: ()=>setIsOpen(true),\n            className: \"fixed bottom-4 right-4 bg-gray-800 text-white px-3 py-2 rounded text-xs\",\n            children: \"Debug\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\DebugPanel.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-4 right-4 bg-white border rounded-lg shadow-lg p-4 max-w-xs\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-medium text-sm\",\n                        children: \"Debug Panel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\DebugPanel.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setIsOpen(false),\n                        className: \"text-gray-500 hover:text-gray-700\",\n                        children: \"\\xd7\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\DebugPanel.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\DebugPanel.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2 text-xs\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleForceCleanup,\n                        className: \"w-full bg-red-500 text-white px-2 py-1 rounded hover:bg-red-600\",\n                        children: \"Force Cleanup Subscriptions\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\DebugPanel.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.reload(),\n                        className: \"w-full bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600\",\n                        children: \"Reload Page\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\DebugPanel.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600\",\n                        children: \"Check console (F12) for detailed logs\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\DebugPanel.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\DebugPanel.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\DebugPanel.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/DebugPanel.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/HelpMeCard.tsx":
/*!***************************************!*\
  !*** ./src/components/HelpMeCard.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HelpMeCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/src/hooks/useSupabaseData */ \"(ssr)/./src/hooks/useSupabaseData.tsx\");\n/* harmony import */ var _src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/utils/userIdentification */ \"(ssr)/./src/utils/userIdentification.ts\");\n/* harmony import */ var _ui_CardWrapper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/CardWrapper */ \"(ssr)/./src/components/ui/CardWrapper.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction HelpMeCard() {\n    const { helpMe, addHelpRequest, deleteHelpRequest } = (0,_src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const [description, setDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HelpMeCard.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"HelpMeCard.useEffect\"], []);\n    const handleRequestHelp = async ()=>{\n        if (!description.trim() || isSubmitting || !isClient) return;\n        setIsSubmitting(true);\n        const user = (0,_src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_3__.getUserDisplayName)();\n        const success = await addHelpRequest(user, description.trim());\n        if (success) {\n            setDescription(\"\");\n        }\n        setIsSubmitting(false);\n    };\n    const handleDelete = async (id)=>{\n        if (confirm(\"Are you sure you want to delete this help request?\")) {\n            await deleteHelpRequest(id);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_CardWrapper__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        color: \"bgDark\",\n        className: \"border-l-4 border-purple-500 h-full\",\n        count: helpMe.length,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-3 h-3 bg-purple-500 rounded-full mr-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-primary\",\n                        children: \"\\uD83C\\uDD98 Help Requests\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 bg-white p-4 rounded-lg border border-gray-200 shadow-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                        value: description,\n                        onChange: (e)=>setDescription(e.target.value),\n                        placeholder: \"Describe what you need help with...\",\n                        className: \"w-full border border-gray-300 rounded px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 h-20 resize-none mb-3\",\n                        disabled: isSubmitting || !isClient,\n                        maxLength: 500\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-500 mb-3\",\n                        children: [\n                            description.length,\n                            \"/500 characters\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleRequestHelp,\n                        disabled: isSubmitting || !description.trim() || !isClient,\n                        className: `w-full p-3 rounded-lg text-white font-medium transition-all duration-200 ${isSubmitting || !description.trim() || !isClient ? \"bg-gray-400 cursor-not-allowed\" : \"bg-purple-500 hover:bg-purple-600 shadow-md hover:shadow-lg transform hover:scale-[1.02]\"}`,\n                        children: !isClient ? \"Loading...\" : isSubmitting ? \"Requesting...\" : \"🙋‍♂️ Request Help\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3 max-h-64 overflow-y-auto\",\n                children: [\n                    helpMe.map((entry)=>{\n                        const canDelete = isClient && (0,_src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_3__.isCurrentUserPost)(entry.user);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white border border-gray-200 rounded-lg p-3 shadow-sm hover:shadow-md transition-shadow\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-start\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-700 mb-2\",\n                                                children: entry.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center text-xs text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-primary\",\n                                                                children: entry.user\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                                                                lineNumber: 83,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            canDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-accent/10 text-accent px-2 py-1 rounded-full\",\n                                                                children: \"Your post\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                                                                lineNumber: 84,\n                                                                columnNumber: 37\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                                                        lineNumber: 82,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: entry.timestamp.toLocaleString()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                                                        lineNumber: 86,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 17\n                                    }, this),\n                                    canDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleDelete(entry.id),\n                                        className: \"text-warn text-xs hover:text-red-700 ml-2 px-2 py-1 hover:bg-red-50 rounded transition-colors\",\n                                        title: \"Delete your post\",\n                                        children: \"Delete\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 15\n                            }, this)\n                        }, entry.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 13\n                        }, this);\n                    }),\n                    helpMe.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-gray-600 py-4\",\n                        children: \"No help requests yet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 33\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\HelpMeCard.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9IZWxwTWVDYXJkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFMkM7QUFDYztBQUM2QjtBQUM1QztBQUUzQixTQUFTTTtJQUN0QixNQUFNLEVBQUVDLE1BQU0sRUFBRUMsY0FBYyxFQUFFQyxpQkFBaUIsRUFBRSxHQUFHUCxzRUFBZUE7SUFDckUsTUFBTSxDQUFDUSxhQUFhQyxlQUFlLEdBQUdYLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ1ksY0FBY0MsZ0JBQWdCLEdBQUdiLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ2MsVUFBVUMsWUFBWSxHQUFHZiwrQ0FBUUEsQ0FBQztJQUV6Q0MsZ0RBQVNBO2dDQUFDO1lBQ1JjLFlBQVk7UUFDZDsrQkFBRyxFQUFFO0lBRUwsTUFBTUMsb0JBQW9CO1FBQ3hCLElBQUksQ0FBQ04sWUFBWU8sSUFBSSxNQUFNTCxnQkFBZ0IsQ0FBQ0UsVUFBVTtRQUV0REQsZ0JBQWdCO1FBQ2hCLE1BQU1LLE9BQU9mLGlGQUFrQkE7UUFFL0IsTUFBTWdCLFVBQVUsTUFBTVgsZUFBZVUsTUFBTVIsWUFBWU8sSUFBSTtRQUUzRCxJQUFJRSxTQUFTO1lBQ1hSLGVBQWU7UUFDakI7UUFFQUUsZ0JBQWdCO0lBQ2xCO0lBRUEsTUFBTU8sZUFBZSxPQUFPQztRQUMxQixJQUFJQyxRQUFRLHVEQUF1RDtZQUNqRSxNQUFNYixrQkFBa0JZO1FBQzFCO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ2hCLHVEQUFXQTtRQUFDa0IsT0FBTTtRQUFTQyxXQUFVO1FBQXNDQyxPQUFPbEIsT0FBT21CLE1BQU07OzBCQUM5Riw4REFBQ0M7Z0JBQUlILFdBQVU7O2tDQUNiLDhEQUFDRzt3QkFBSUgsV0FBVTs7Ozs7O2tDQUNmLDhEQUFDSTt3QkFBR0osV0FBVTtrQ0FBaUM7Ozs7Ozs7Ozs7OzswQkFHakQsOERBQUNHO2dCQUFJSCxXQUFVOztrQ0FDYiw4REFBQ0s7d0JBQ0NDLE9BQU9wQjt3QkFDUHFCLFVBQVUsQ0FBQ0MsSUFBTXJCLGVBQWVxQixFQUFFQyxNQUFNLENBQUNILEtBQUs7d0JBQzlDSSxhQUFZO3dCQUNaVixXQUFVO3dCQUNWVyxVQUFVdkIsZ0JBQWdCLENBQUNFO3dCQUMzQnNCLFdBQVc7Ozs7OztrQ0FFYiw4REFBQ1Q7d0JBQUlILFdBQVU7OzRCQUE4QmQsWUFBWWdCLE1BQU07NEJBQUM7Ozs7Ozs7a0NBQ2hFLDhEQUFDVzt3QkFDQ0MsU0FBU3RCO3dCQUNUbUIsVUFBVXZCLGdCQUFnQixDQUFDRixZQUFZTyxJQUFJLE1BQU0sQ0FBQ0g7d0JBQ2xEVSxXQUFXLENBQUMseUVBQXlFLEVBQ25GWixnQkFBZ0IsQ0FBQ0YsWUFBWU8sSUFBSSxNQUFNLENBQUNILFdBQ3BDLG1DQUNBLDRGQUNKO2tDQUVELENBQUNBLFdBQVcsZUFBZUYsZUFBZSxrQkFBa0I7Ozs7Ozs7Ozs7OzswQkFJakUsOERBQUNlO2dCQUFJSCxXQUFVOztvQkFDWmpCLE9BQU9nQyxHQUFHLENBQUMsQ0FBQ0M7d0JBQ1gsTUFBTUMsWUFBWTNCLFlBQVlWLGdGQUFpQkEsQ0FBQ29DLE1BQU10QixJQUFJO3dCQUUxRCxxQkFDRSw4REFBQ1M7NEJBRUNILFdBQVU7c0NBRVYsNEVBQUNHO2dDQUFJSCxXQUFVOztrREFDYiw4REFBQ0c7d0NBQUlILFdBQVU7OzBEQUNiLDhEQUFDRztnREFBSUgsV0FBVTswREFBOEJnQixNQUFNOUIsV0FBVzs7Ozs7OzBEQUM5RCw4REFBQ2lCO2dEQUFJSCxXQUFVOztrRUFDYiw4REFBQ0c7d0RBQUlILFdBQVU7OzBFQUNiLDhEQUFDa0I7Z0VBQUtsQixXQUFVOzBFQUE0QmdCLE1BQU10QixJQUFJOzs7Ozs7NERBQ3JEdUIsMkJBQWEsOERBQUNDO2dFQUFLbEIsV0FBVTswRUFBa0Q7Ozs7Ozs7Ozs7OztrRUFFbEYsOERBQUNrQjtrRUFBTUYsTUFBTUcsU0FBUyxDQUFDQyxjQUFjOzs7Ozs7Ozs7Ozs7Ozs7Ozs7b0NBR3hDSCwyQkFDQyw4REFBQ0o7d0NBQ0NDLFNBQVMsSUFBTWxCLGFBQWFvQixNQUFNbkIsRUFBRTt3Q0FDcENHLFdBQVU7d0NBQ1ZxQixPQUFNO2tEQUNQOzs7Ozs7Ozs7Ozs7MkJBbkJBTCxNQUFNbkIsRUFBRTs7Ozs7b0JBMEJuQjtvQkFDQ2QsT0FBT21CLE1BQU0sS0FBSyxtQkFBSyw4REFBQ0M7d0JBQUlILFdBQVU7a0NBQWlDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJaEYiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbWVtZXRcXERlc2t0b3BcXGRvcm1fMjFcXHNyY1xcY29tcG9uZW50c1xcSGVscE1lQ2FyZC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcclxuXHJcbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tIFwicmVhY3RcIlxyXG5pbXBvcnQgdXNlU3VwYWJhc2VEYXRhIGZyb20gXCJAL3NyYy9ob29rcy91c2VTdXBhYmFzZURhdGFcIlxyXG5pbXBvcnQgeyBnZXRVc2VyRGlzcGxheU5hbWUsIGlzQ3VycmVudFVzZXJQb3N0IH0gZnJvbSBcIkAvc3JjL3V0aWxzL3VzZXJJZGVudGlmaWNhdGlvblwiXHJcbmltcG9ydCBDYXJkV3JhcHBlciBmcm9tIFwiLi91aS9DYXJkV3JhcHBlclwiXHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIZWxwTWVDYXJkKCkge1xyXG4gIGNvbnN0IHsgaGVscE1lLCBhZGRIZWxwUmVxdWVzdCwgZGVsZXRlSGVscFJlcXVlc3QgfSA9IHVzZVN1cGFiYXNlRGF0YSgpXHJcbiAgY29uc3QgW2Rlc2NyaXB0aW9uLCBzZXREZXNjcmlwdGlvbl0gPSB1c2VTdGF0ZShcIlwiKVxyXG4gIGNvbnN0IFtpc1N1Ym1pdHRpbmcsIHNldElzU3VibWl0dGluZ10gPSB1c2VTdGF0ZShmYWxzZSlcclxuICBjb25zdCBbaXNDbGllbnQsIHNldElzQ2xpZW50XSA9IHVzZVN0YXRlKGZhbHNlKVxyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgc2V0SXNDbGllbnQodHJ1ZSlcclxuICB9LCBbXSlcclxuXHJcbiAgY29uc3QgaGFuZGxlUmVxdWVzdEhlbHAgPSBhc3luYyAoKSA9PiB7XHJcbiAgICBpZiAoIWRlc2NyaXB0aW9uLnRyaW0oKSB8fCBpc1N1Ym1pdHRpbmcgfHwgIWlzQ2xpZW50KSByZXR1cm5cclxuXHJcbiAgICBzZXRJc1N1Ym1pdHRpbmcodHJ1ZSlcclxuICAgIGNvbnN0IHVzZXIgPSBnZXRVc2VyRGlzcGxheU5hbWUoKVxyXG5cclxuICAgIGNvbnN0IHN1Y2Nlc3MgPSBhd2FpdCBhZGRIZWxwUmVxdWVzdCh1c2VyLCBkZXNjcmlwdGlvbi50cmltKCkpXHJcblxyXG4gICAgaWYgKHN1Y2Nlc3MpIHtcclxuICAgICAgc2V0RGVzY3JpcHRpb24oXCJcIilcclxuICAgIH1cclxuXHJcbiAgICBzZXRJc1N1Ym1pdHRpbmcoZmFsc2UpXHJcbiAgfVxyXG5cclxuICBjb25zdCBoYW5kbGVEZWxldGUgPSBhc3luYyAoaWQ6IHN0cmluZykgPT4ge1xyXG4gICAgaWYgKGNvbmZpcm0oXCJBcmUgeW91IHN1cmUgeW91IHdhbnQgdG8gZGVsZXRlIHRoaXMgaGVscCByZXF1ZXN0P1wiKSkge1xyXG4gICAgICBhd2FpdCBkZWxldGVIZWxwUmVxdWVzdChpZClcclxuICAgIH1cclxuICB9XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8Q2FyZFdyYXBwZXIgY29sb3I9XCJiZ0RhcmtcIiBjbGFzc05hbWU9XCJib3JkZXItbC00IGJvcmRlci1wdXJwbGUtNTAwIGgtZnVsbFwiIGNvdW50PXtoZWxwTWUubGVuZ3RofT5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBtYi02XCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTMgaC0zIGJnLXB1cnBsZS01MDAgcm91bmRlZC1mdWxsIG1yLTNcIj48L2Rpdj5cclxuICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgdGV4dC1wcmltYXJ5XCI+8J+GmCBIZWxwIFJlcXVlc3RzPC9oMj5cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTQgYmctd2hpdGUgcC00IHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBzaGFkb3ctc21cIj5cclxuICAgICAgICA8dGV4dGFyZWFcclxuICAgICAgICAgIHZhbHVlPXtkZXNjcmlwdGlvbn1cclxuICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0RGVzY3JpcHRpb24oZS50YXJnZXQudmFsdWUpfVxyXG4gICAgICAgICAgcGxhY2Vob2xkZXI9XCJEZXNjcmliZSB3aGF0IHlvdSBuZWVkIGhlbHAgd2l0aC4uLlwiXHJcbiAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkIHB4LTMgcHktMiBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1wdXJwbGUtNTAwIGZvY3VzOmJvcmRlci1wdXJwbGUtNTAwIGgtMjAgcmVzaXplLW5vbmUgbWItM1wiXHJcbiAgICAgICAgICBkaXNhYmxlZD17aXNTdWJtaXR0aW5nIHx8ICFpc0NsaWVudH1cclxuICAgICAgICAgIG1heExlbmd0aD17NTAwfVxyXG4gICAgICAgIC8+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgbWItM1wiPntkZXNjcmlwdGlvbi5sZW5ndGh9LzUwMCBjaGFyYWN0ZXJzPC9kaXY+XHJcbiAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgb25DbGljaz17aGFuZGxlUmVxdWVzdEhlbHB9XHJcbiAgICAgICAgICBkaXNhYmxlZD17aXNTdWJtaXR0aW5nIHx8ICFkZXNjcmlwdGlvbi50cmltKCkgfHwgIWlzQ2xpZW50fVxyXG4gICAgICAgICAgY2xhc3NOYW1lPXtgdy1mdWxsIHAtMyByb3VuZGVkLWxnIHRleHQtd2hpdGUgZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwICR7XHJcbiAgICAgICAgICAgIGlzU3VibWl0dGluZyB8fCAhZGVzY3JpcHRpb24udHJpbSgpIHx8ICFpc0NsaWVudFxyXG4gICAgICAgICAgICAgID8gXCJiZy1ncmF5LTQwMCBjdXJzb3Itbm90LWFsbG93ZWRcIlxyXG4gICAgICAgICAgICAgIDogXCJiZy1wdXJwbGUtNTAwIGhvdmVyOmJnLXB1cnBsZS02MDAgc2hhZG93LW1kIGhvdmVyOnNoYWRvdy1sZyB0cmFuc2Zvcm0gaG92ZXI6c2NhbGUtWzEuMDJdXCJcclxuICAgICAgICAgIH1gfVxyXG4gICAgICAgID5cclxuICAgICAgICAgIHshaXNDbGllbnQgPyBcIkxvYWRpbmcuLi5cIiA6IGlzU3VibWl0dGluZyA/IFwiUmVxdWVzdGluZy4uLlwiIDogXCLwn5mL4oCN4pmC77iPIFJlcXVlc3QgSGVscFwifVxyXG4gICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICA8L2Rpdj5cclxuXHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zIG1heC1oLTY0IG92ZXJmbG93LXktYXV0b1wiPlxyXG4gICAgICAgIHtoZWxwTWUubWFwKChlbnRyeSkgPT4ge1xyXG4gICAgICAgICAgY29uc3QgY2FuRGVsZXRlID0gaXNDbGllbnQgJiYgaXNDdXJyZW50VXNlclBvc3QoZW50cnkudXNlcilcclxuXHJcbiAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAga2V5PXtlbnRyeS5pZH1cclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy13aGl0ZSBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHJvdW5kZWQtbGcgcC0zIHNoYWRvdy1zbSBob3ZlcjpzaGFkb3ctbWQgdHJhbnNpdGlvbi1zaGFkb3dcIlxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1zdGFydFwiPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS03MDAgbWItMlwiPntlbnRyeS5kZXNjcmlwdGlvbn08L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgdGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1wcmltYXJ5XCI+e2VudHJ5LnVzZXJ9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAge2NhbkRlbGV0ZSAmJiA8c3BhbiBjbGFzc05hbWU9XCJiZy1hY2NlbnQvMTAgdGV4dC1hY2NlbnQgcHgtMiBweS0xIHJvdW5kZWQtZnVsbFwiPllvdXIgcG9zdDwvc3Bhbj59XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPHNwYW4+e2VudHJ5LnRpbWVzdGFtcC50b0xvY2FsZVN0cmluZygpfTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIHtjYW5EZWxldGUgJiYgKFxyXG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlRGVsZXRlKGVudHJ5LmlkKX1cclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXdhcm4gdGV4dC14cyBob3Zlcjp0ZXh0LXJlZC03MDAgbWwtMiBweC0yIHB5LTEgaG92ZXI6YmctcmVkLTUwIHJvdW5kZWQgdHJhbnNpdGlvbi1jb2xvcnNcIlxyXG4gICAgICAgICAgICAgICAgICAgIHRpdGxlPVwiRGVsZXRlIHlvdXIgcG9zdFwiXHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICBEZWxldGVcclxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIClcclxuICAgICAgICB9KX1cclxuICAgICAgICB7aGVscE1lLmxlbmd0aCA9PT0gMCAmJiA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHRleHQtZ3JheS02MDAgcHktNFwiPk5vIGhlbHAgcmVxdWVzdHMgeWV0PC9kaXY+fVxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvQ2FyZFdyYXBwZXI+XHJcbiAgKVxyXG59XHJcbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVN1cGFiYXNlRGF0YSIsImdldFVzZXJEaXNwbGF5TmFtZSIsImlzQ3VycmVudFVzZXJQb3N0IiwiQ2FyZFdyYXBwZXIiLCJIZWxwTWVDYXJkIiwiaGVscE1lIiwiYWRkSGVscFJlcXVlc3QiLCJkZWxldGVIZWxwUmVxdWVzdCIsImRlc2NyaXB0aW9uIiwic2V0RGVzY3JpcHRpb24iLCJpc1N1Ym1pdHRpbmciLCJzZXRJc1N1Ym1pdHRpbmciLCJpc0NsaWVudCIsInNldElzQ2xpZW50IiwiaGFuZGxlUmVxdWVzdEhlbHAiLCJ0cmltIiwidXNlciIsInN1Y2Nlc3MiLCJoYW5kbGVEZWxldGUiLCJpZCIsImNvbmZpcm0iLCJjb2xvciIsImNsYXNzTmFtZSIsImNvdW50IiwibGVuZ3RoIiwiZGl2IiwiaDIiLCJ0ZXh0YXJlYSIsInZhbHVlIiwib25DaGFuZ2UiLCJlIiwidGFyZ2V0IiwicGxhY2Vob2xkZXIiLCJkaXNhYmxlZCIsIm1heExlbmd0aCIsImJ1dHRvbiIsIm9uQ2xpY2siLCJtYXAiLCJlbnRyeSIsImNhbkRlbGV0ZSIsInNwYW4iLCJ0aW1lc3RhbXAiLCJ0b0xvY2FsZVN0cmluZyIsInRpdGxlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/HelpMeCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/IncidentBadge.tsx":
/*!******************************************!*\
  !*** ./src/components/IncidentBadge.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ IncidentBadge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction IncidentBadge({ incidents, onDelete }) {\n    const [showDropdown, setShowDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    if (incidents.length === 0) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setShowDropdown(!showDropdown),\n                className: `bg-warn text-white text-xs px-2 py-1 rounded-full font-medium hover:bg-red-600 transition-colors ${incidents.length > 0 ? \"animate-pulse-slow\" : \"\"}`,\n                children: incidents.length\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\IncidentBadge.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            showDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 top-8 bg-white border border-gray-200 rounded-lg shadow-lg p-3 min-w-48 z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm font-medium mb-2 text-primary\",\n                        children: \"Incidents\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\IncidentBadge.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, this),\n                    incidents.map((incident)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-600\",\n                                    children: [\n                                        incident.type,\n                                        \" - \",\n                                        incident.timestamp.toLocaleTimeString()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\IncidentBadge.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        onDelete(incident.id);\n                                        setShowDropdown(false);\n                                    },\n                                    className: \"text-warn text-xs hover:text-red-700 px-2 py-1 rounded hover:bg-red-50 transition-colors\",\n                                    children: \"Delete\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\IncidentBadge.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, incident.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\IncidentBadge.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 13\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\IncidentBadge.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\IncidentBadge.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/IncidentBadge.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/LaundryCard.tsx":
/*!****************************************!*\
  !*** ./src/components/LaundryCard.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LaundryCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/src/hooks/useSupabaseData */ \"(ssr)/./src/hooks/useSupabaseData.tsx\");\n/* harmony import */ var _src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/src/hooks/useCountdown */ \"(ssr)/./src/hooks/useCountdown.tsx\");\n/* harmony import */ var _IncidentBadge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./IncidentBadge */ \"(ssr)/./src/components/IncidentBadge.tsx\");\n/* harmony import */ var _ui_CardWrapper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/CardWrapper */ \"(ssr)/./src/components/ui/CardWrapper.tsx\");\n/* harmony import */ var _ui_WasherSVG__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/WasherSVG */ \"(ssr)/./src/components/ui/WasherSVG.tsx\");\n/* harmony import */ var _ui_DryerOutlineSVG__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/DryerOutlineSVG */ \"(ssr)/./src/components/ui/DryerOutlineSVG.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction MachineStatus({ machine }) {\n    const countdown = (0,_src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(machine.endAt, machine.graceEndAt);\n    if (machine.status === \"free\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-green-100 text-green-800 border-2 border-green-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"w-2 h-2 bg-green-500 rounded-full mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 11\n                    }, this),\n                    \"Available\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                lineNumber: 17,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this);\n    }\n    if (machine.status === \"running\" && machine.endAt) {\n        const hours = Math.floor(countdown.secondsLeft / 3600);\n        const minutes = Math.floor(countdown.secondsLeft % 3600 / 60);\n        const seconds = countdown.secondsLeft % 60;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-blue-100 text-blue-800 border-2 border-blue-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"w-2 h-2 bg-blue-500 rounded-full mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 11\n                    }, this),\n                    hours,\n                    \":\",\n                    minutes.toString().padStart(2, \"0\"),\n                    \":\",\n                    seconds.toString().padStart(2, \"0\"),\n                    \" left\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this);\n    }\n    if (machine.status === \"finishedGrace\") {\n        let graceTimeDisplay = \"5:00\";\n        if (machine.graceEndAt) {\n            const minutes = Math.floor(countdown.graceSecondsLeft / 60);\n            const seconds = countdown.graceSecondsLeft % 60;\n            graceTimeDisplay = `${minutes}:${seconds.toString().padStart(2, \"0\")}`;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-orange-100 text-orange-800 border-2 border-orange-200 animate-pulse-slow\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"w-2 h-2 bg-orange-500 rounded-full mr-2 animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, this),\n                    \"Collect items - \",\n                    graceTimeDisplay\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-gray-100 text-gray-800 border-2 border-gray-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"w-2 h-2 bg-gray-500 rounded-full mr-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this),\n                \"Unknown\"\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n            lineNumber: 61,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\nfunction LaundryCard() {\n    const { laundry, incidents, deleteIncident } = (0,_src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n    // Count total incidents for the badge\n    const totalIncidents = incidents.length;\n    // Custom display names for machines\n    const getDisplayName = (machine)=>{\n        const isWasher = machine.name.toLowerCase().includes(\"washer\");\n        if (isWasher) {\n            // Extract number from washer name (e.g., \"Washer 1\" -> \"1\")\n            const numberMatch = machine.name.match(/\\d+/);\n            const number = numberMatch ? numberMatch[0] : \"\";\n            return `Washer ${number}`;\n        } else {\n            // For dryers, use \"Dryer 5\" through \"Dryer 8\" based on original number\n            const numberMatch = machine.name.match(/\\d+/);\n            const originalNumber = numberMatch ? Number.parseInt(numberMatch[0]) : 0;\n            const newNumber = originalNumber + 4 // Map 1->5, 2->6, 3->7, 4->8\n            ;\n            return `Dryer ${newNumber}`;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_CardWrapper__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        color: \"white\",\n        className: \"border-l-4 border-accent shadow-lg\",\n        count: totalIncidents,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-4 h-4 bg-accent rounded-full mr-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-primary\",\n                                children: \"\\uD83D\\uDC55 Laundry Machines\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-600 bg-bgLight px-3 py-1 rounded-full\",\n                        children: [\n                            laundry.filter((m)=>m.status === \"free\").length,\n                            \" of \",\n                            laundry.length,\n                            \" available\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\",\n                children: laundry.map((machine)=>{\n                    const machineIncidents = incidents.filter((inc)=>inc.machineId === machine.id);\n                    const isWasher = machine.name.toLowerCase().includes(\"washer\");\n                    const displayName = getDisplayName(machine);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border-2 border-gray-100 rounded-xl p-6 shadow-sm hover:shadow-lg transition-all duration-300 hover:border-accent/30 relative group\",\n                        children: [\n                            machineIncidents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-3 -right-3 z-10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_IncidentBadge__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    incidents: machineIncidents,\n                                    onDelete: deleteIncident\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 bg-bgLight rounded-xl group-hover:bg-accent/10 transition-colors duration-300\",\n                                    children: isWasher ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_WasherSVG__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-20 h-20\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 31\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_DryerOutlineSVG__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-20 h-20\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 69\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-bold text-primary text-xl mb-1\",\n                                    children: displayName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MachineStatus, {\n                                        machine: machine\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 17\n                                    }, this),\n                                    machine.status === \"finishedGrace\" && machine.graceEndAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-orange-600 text-center mt-2 bg-orange-50 py-2 px-3 rounded-lg\",\n                                        children: [\n                                            \"Grace ends: \",\n                                            machine.graceEndAt?.toLocaleTimeString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 19\n                                    }, this),\n                                    machine.status === \"running\" && machine.endAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-blue-600 text-center mt-2 bg-blue-50 py-2 px-3 rounded-lg\",\n                                        children: [\n                                            \"Cycle ends: \",\n                                            machine.endAt?.toLocaleTimeString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, machine.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 pt-6 border-t border-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap justify-center gap-4 text-sm text-gray-600\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Available\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"In Use\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"w-2 h-2 bg-orange-500 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Please Collect\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/LaundryCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/NoiseCard.tsx":
/*!**************************************!*\
  !*** ./src/components/NoiseCard.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NoiseCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/src/hooks/useSupabaseData */ \"(ssr)/./src/hooks/useSupabaseData.tsx\");\n/* harmony import */ var _src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/utils/userIdentification */ \"(ssr)/./src/utils/userIdentification.ts\");\n/* harmony import */ var _ui_CardWrapper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/CardWrapper */ \"(ssr)/./src/components/ui/CardWrapper.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction NoiseCard() {\n    const { noise, addNoiseWithDescription, deleteNoise } = (0,_src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const [canReport, setCanReport] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [description, setDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showForm, setShowForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NoiseCard.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"NoiseCard.useEffect\"], []);\n    const handleReportNoise = async ()=>{\n        if (!canReport || isSubmitting || !description.trim() || !isClient) return;\n        setIsSubmitting(true);\n        const user = (0,_src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_3__.getUserDisplayName)();\n        const success = await addNoiseWithDescription(user, description.trim());\n        if (success) {\n            setDescription(\"\");\n            setShowForm(false);\n            setCanReport(false);\n            setTimeout(()=>setCanReport(true), 2 * 60 * 1000);\n        }\n        setIsSubmitting(false);\n    };\n    const handleShowForm = ()=>{\n        setShowForm(true);\n        setDescription(\"\");\n    };\n    const handleCancel = ()=>{\n        setShowForm(false);\n        setDescription(\"\");\n    };\n    const handleDelete = async (id)=>{\n        if (confirm(\"Are you sure you want to delete this noise report?\")) {\n            await deleteNoise(id);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_CardWrapper__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        color: \"bgDark\",\n        className: \"border-l-4 border-warn h-full\",\n        count: noise.length,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-3 h-3 bg-warn rounded-full mr-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\NoiseCard.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-primary\",\n                        children: \"\\uD83D\\uDD0A Noise Reports\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\NoiseCard.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\NoiseCard.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            !showForm ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleShowForm,\n                disabled: !canReport || !isClient,\n                className: `w-full p-3 rounded-lg mb-4 font-medium transition-all duration-200 ${!canReport || !isClient ? \"bg-gray-300 text-gray-500 cursor-not-allowed\" : \"bg-warn hover:bg-red-600 text-white shadow-md hover:shadow-lg transform hover:scale-[1.02]\"}`,\n                children: !isClient ? \"Loading...\" : canReport ? \"📢 Report Noise Issue\" : \"Wait 2 minutes\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\NoiseCard.tsx\",\n                lineNumber: 62,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 bg-white p-4 rounded-lg border border-gray-200 shadow-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm font-medium text-primary mb-2\",\n                        children: \"Describe the noise issue:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\NoiseCard.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                        value: description,\n                        onChange: (e)=>setDescription(e.target.value),\n                        placeholder: \"Please describe the noise issue (required)...\",\n                        className: \"w-full border border-gray-300 rounded px-3 py-2 focus:ring-2 focus:ring-accent focus:border-accent h-20 resize-none\",\n                        disabled: isSubmitting,\n                        maxLength: 500\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\NoiseCard.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-500 mt-1\",\n                        children: [\n                            description.length,\n                            \"/500 characters\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\NoiseCard.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2 mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleReportNoise,\n                                disabled: isSubmitting || !description.trim() || !isClient,\n                                className: `flex-1 p-2 rounded-lg font-medium transition-colors ${isSubmitting || !description.trim() || !isClient ? \"bg-gray-300 text-gray-500 cursor-not-allowed\" : \"bg-warn hover:bg-red-600 text-white\"}`,\n                                children: isSubmitting ? \"Reporting...\" : \"Submit Report\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\NoiseCard.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCancel,\n                                disabled: isSubmitting,\n                                className: \"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\",\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\NoiseCard.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\NoiseCard.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\NoiseCard.tsx\",\n                lineNumber: 74,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3 max-h-64 overflow-y-auto\",\n                children: [\n                    noise.map((entry)=>{\n                        const canDelete = isClient && (0,_src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_3__.isCurrentUserPost)(entry.user);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white border border-gray-200 rounded-lg p-3 shadow-sm hover:shadow-md transition-shadow\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-start\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 mb-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-primary\",\n                                                        children: entry.user\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\NoiseCard.tsx\",\n                                                        lineNumber: 120,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    canDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs bg-accent/10 text-accent px-2 py-1 rounded-full\",\n                                                        children: \"Your post\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\NoiseCard.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\NoiseCard.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-700 mt-1\",\n                                                children: entry.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\NoiseCard.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-500 mt-1\",\n                                                children: entry.timestamp.toLocaleString()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\NoiseCard.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\NoiseCard.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 17\n                                    }, this),\n                                    canDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleDelete(entry.id),\n                                        className: \"text-warn text-xs hover:text-red-700 ml-2 px-2 py-1 hover:bg-red-50 rounded transition-colors\",\n                                        title: \"Delete your post\",\n                                        children: \"Delete\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\NoiseCard.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\NoiseCard.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 15\n                            }, this)\n                        }, entry.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\NoiseCard.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 13\n                        }, this);\n                    }),\n                    noise.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-gray-600 py-4\",\n                        children: \"No noise reports yet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\NoiseCard.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 32\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\NoiseCard.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\NoiseCard.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/NoiseCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/UserSettings.tsx":
/*!*****************************************!*\
  !*** ./src/components/UserSettings.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ UserSettings)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/src/utils/userIdentification */ \"(ssr)/./src/utils/userIdentification.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction UserSettings({ isOpen, onClose }) {\n    const [displayName, setDisplayName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [deviceId, setDeviceId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UserSettings.useEffect\": ()=>{\n            setIsClient(true);\n            if (isClient) {\n                setDisplayName((0,_src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_2__.getUserDisplayName)());\n                setDeviceId((0,_src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_2__.getDeviceUserId)());\n            }\n        }\n    }[\"UserSettings.useEffect\"], [\n        isClient\n    ]);\n    const handleSave = ()=>{\n        if (displayName.trim()) {\n            (0,_src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_2__.setUserDisplayName)(displayName.trim());\n            onClose();\n            window.location.reload() // Refresh to update all components\n            ;\n        }\n    };\n    if (!isOpen || !isClient) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl border border-gray-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold text-primary\",\n                            children: \"User Settings\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\UserSettings.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-500 hover:text-gray-700 text-xl w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors\",\n                            children: \"\\xd7\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\UserSettings.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\UserSettings.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-primary mb-1\",\n                                    children: \"Device ID (Read-only)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\UserSettings.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: deviceId,\n                                    readOnly: true,\n                                    className: \"w-full p-3 border border-gray-300 rounded-lg bg-bgDark text-gray-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\UserSettings.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-1\",\n                                    children: \"This unique ID identifies your device\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\UserSettings.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\UserSettings.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-primary mb-1\",\n                                    children: \"Display Name\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\UserSettings.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: displayName,\n                                    onChange: (e)=>setDisplayName(e.target.value),\n                                    placeholder: \"Enter your display name\",\n                                    className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent focus:border-accent\",\n                                    maxLength: 20\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\UserSettings.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-1\",\n                                    children: \"This name will appear on your posts (max 20 characters)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\UserSettings.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\UserSettings.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\UserSettings.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-3 mt-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleSave,\n                            disabled: !displayName.trim(),\n                            className: `flex-1 p-3 rounded-lg font-medium transition-colors ${!displayName.trim() ? \"bg-gray-300 text-gray-500 cursor-not-allowed\" : \"bg-accent hover:bg-teal-600 text-white\"}`,\n                            children: \"Save Changes\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\UserSettings.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\",\n                            children: \"Cancel\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\UserSettings.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\UserSettings.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\UserSettings.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\UserSettings.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/UserSettings.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/CardWrapper.tsx":
/*!*******************************************!*\
  !*** ./src/components/ui/CardWrapper.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CardWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction CardWrapper({ children, color = \"white\", className = \"\", count, title }) {\n    const colorClasses = {\n        white: \"bg-white\",\n        bgDark: \"bg-bgDark\",\n        primary: \"bg-primary text-white\",\n        accent: \"bg-accent text-white\"\n    };\n    const bgClass = colorClasses[color] || `bg-${color}`;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `relative p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 ${bgClass} ${className}`,\n        children: [\n            count !== undefined && count > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -top-2 -right-2 bg-warn text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center animate-pulse-slow\",\n                children: count\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\ui\\\\CardWrapper.tsx\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\ui\\\\CardWrapper.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/CardWrapper.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/DryerOutlineSVG.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/DryerOutlineSVG.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DryerOutlineSVG)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction DryerOutlineSVG({ className = \"w-12 h-12\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        viewBox: \"0 0 100 100\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"10\",\n                y: \"15\",\n                width: \"80\",\n                height: \"70\",\n                rx: \"8\",\n                fill: \"none\",\n                stroke: \"#1A1F36\",\n                strokeWidth: \"2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\ui\\\\DryerOutlineSVG.tsx\",\n                lineNumber: 5,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"15\",\n                y: \"20\",\n                width: \"70\",\n                height: \"15\",\n                rx: \"4\",\n                fill: \"none\",\n                stroke: \"#1A1F36\",\n                strokeWidth: \"1\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\ui\\\\DryerOutlineSVG.tsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"25\",\n                cy: \"27.5\",\n                r: \"3\",\n                fill: \"none\",\n                stroke: \"#1A1F36\",\n                strokeWidth: \"1\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\ui\\\\DryerOutlineSVG.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"35\",\n                cy: \"27.5\",\n                r: \"3\",\n                fill: \"none\",\n                stroke: \"#1A1F36\",\n                strokeWidth: \"1\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\ui\\\\DryerOutlineSVG.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"45\",\n                cy: \"27.5\",\n                r: \"3\",\n                fill: \"none\",\n                stroke: \"#1A1F36\",\n                strokeWidth: \"1\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\ui\\\\DryerOutlineSVG.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"55\",\n                y: \"23\",\n                width: \"25\",\n                height: \"9\",\n                rx: \"2\",\n                fill: \"none\",\n                stroke: \"#1A1F36\",\n                strokeWidth: \"1\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\ui\\\\DryerOutlineSVG.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"50\",\n                cy: \"60\",\n                r: \"20\",\n                fill: \"none\",\n                stroke: \"#1A1F36\",\n                strokeWidth: \"2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\ui\\\\DryerOutlineSVG.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"50\",\n                cy: \"60\",\n                r: \"15\",\n                fill: \"none\",\n                stroke: \"#1A1F36\",\n                strokeWidth: \"1\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\ui\\\\DryerOutlineSVG.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"72\",\n                y: \"58\",\n                width: \"8\",\n                height: \"4\",\n                rx: \"2\",\n                fill: \"none\",\n                stroke: \"#1A1F36\",\n                strokeWidth: \"1\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\ui\\\\DryerOutlineSVG.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"15\",\n                y: \"85\",\n                width: \"4\",\n                height: \"8\",\n                fill: \"none\",\n                stroke: \"#1A1F36\",\n                strokeWidth: \"1\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\ui\\\\DryerOutlineSVG.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"81\",\n                y: \"85\",\n                width: \"4\",\n                height: \"8\",\n                fill: \"none\",\n                stroke: \"#1A1F36\",\n                strokeWidth: \"1\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\ui\\\\DryerOutlineSVG.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"20\",\n                y1: \"45\",\n                x2: \"30\",\n                y2: \"45\",\n                stroke: \"#1A1F36\",\n                strokeWidth: \"1\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\ui\\\\DryerOutlineSVG.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"20\",\n                y1: \"50\",\n                x2: \"30\",\n                y2: \"50\",\n                stroke: \"#1A1F36\",\n                strokeWidth: \"1\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\ui\\\\DryerOutlineSVG.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"20\",\n                y1: \"55\",\n                x2: \"30\",\n                y2: \"55\",\n                stroke: \"#1A1F36\",\n                strokeWidth: \"1\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\ui\\\\DryerOutlineSVG.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\ui\\\\DryerOutlineSVG.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/DryerOutlineSVG.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/WasherSVG.tsx":
/*!*****************************************!*\
  !*** ./src/components/ui/WasherSVG.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WasherSVG)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction WasherSVG({ className = \"w-12 h-12\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        viewBox: \"0 0 100 100\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"10\",\n                y: \"15\",\n                width: \"80\",\n                height: \"70\",\n                rx: \"8\",\n                fill: \"#00D1C1\",\n                stroke: \"#1A1F36\",\n                strokeWidth: \"2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\ui\\\\WasherSVG.tsx\",\n                lineNumber: 5,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"15\",\n                y: \"20\",\n                width: \"70\",\n                height: \"15\",\n                rx: \"4\",\n                fill: \"#1A1F36\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\ui\\\\WasherSVG.tsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"25\",\n                cy: \"27.5\",\n                r: \"3\",\n                fill: \"#00D1C1\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\ui\\\\WasherSVG.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"35\",\n                cy: \"27.5\",\n                r: \"3\",\n                fill: \"#00D1C1\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\ui\\\\WasherSVG.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"45\",\n                cy: \"27.5\",\n                r: \"3\",\n                fill: \"#00D1C1\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\ui\\\\WasherSVG.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"55\",\n                y: \"23\",\n                width: \"25\",\n                height: \"9\",\n                rx: \"2\",\n                fill: \"#00D1C1\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\ui\\\\WasherSVG.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"50\",\n                cy: \"60\",\n                r: \"20\",\n                fill: \"#F7FAFC\",\n                stroke: \"#1A1F36\",\n                strokeWidth: \"2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\ui\\\\WasherSVG.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"50\",\n                cy: \"60\",\n                r: \"15\",\n                fill: \"none\",\n                stroke: \"#1A1F36\",\n                strokeWidth: \"1\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\ui\\\\WasherSVG.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"72\",\n                y: \"58\",\n                width: \"8\",\n                height: \"4\",\n                rx: \"2\",\n                fill: \"#1A1F36\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\ui\\\\WasherSVG.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"15\",\n                y: \"85\",\n                width: \"4\",\n                height: \"8\",\n                fill: \"#1A1F36\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\ui\\\\WasherSVG.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"81\",\n                y: \"85\",\n                width: \"4\",\n                height: \"8\",\n                fill: \"#1A1F36\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\ui\\\\WasherSVG.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\ui\\\\WasherSVG.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/WasherSVG.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useCountdown.tsx":
/*!************************************!*\
  !*** ./src/hooks/useCountdown.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useCountdown)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction useCountdown(endAt, graceEndAt) {\n    const [secondsLeft, setSecondsLeft] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const [inGrace, setInGrace] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [graceSecondsLeft, setGraceSecondsLeft] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useCountdown.useEffect\": ()=>{\n            const updateCountdown = {\n                \"useCountdown.useEffect.updateCountdown\": ()=>{\n                    const now = new Date();\n                    if (endAt) {\n                        const timeLeft = Math.max(0, Math.floor((endAt.getTime() - now.getTime()) / 1000));\n                        setSecondsLeft(timeLeft);\n                        // Check if we're in grace period\n                        if (graceEndAt) {\n                            const graceLeft = Math.max(0, Math.floor((graceEndAt.getTime() - now.getTime()) / 1000));\n                            setGraceSecondsLeft(graceLeft);\n                            setInGrace(timeLeft === 0 && graceLeft > 0);\n                        } else if (timeLeft === 0) {\n                            // If no graceEndAt but we're at 0 time left and status is finishedGrace,\n                            // use a default 5 minute grace period\n                            const defaultGraceEnd = new Date(endAt.getTime() + 5 * 60 * 1000);\n                            const graceLeft = Math.max(0, Math.floor((defaultGraceEnd.getTime() - now.getTime()) / 1000));\n                            setGraceSecondsLeft(graceLeft);\n                            setInGrace(graceLeft > 0);\n                        } else {\n                            setInGrace(false);\n                            setGraceSecondsLeft(0);\n                        }\n                    } else {\n                        setSecondsLeft(0);\n                        setInGrace(false);\n                        setGraceSecondsLeft(0);\n                    }\n                }\n            }[\"useCountdown.useEffect.updateCountdown\"];\n            updateCountdown();\n            const interval = setInterval(updateCountdown, 1000);\n            return ({\n                \"useCountdown.useEffect\": ()=>clearInterval(interval)\n            })[\"useCountdown.useEffect\"];\n        }\n    }[\"useCountdown.useEffect\"], [\n        endAt,\n        graceEndAt\n    ]);\n    return {\n        secondsLeft,\n        inGrace,\n        graceSecondsLeft\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useCountdown.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useSupabaseData.tsx":
/*!***************************************!*\
  !*** ./src/hooks/useSupabaseData.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useSupabaseData)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/src/lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n/* harmony import */ var _src_lib_subscriptionManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/src/lib/subscriptionManager */ \"(ssr)/./src/lib/subscriptionManager.ts\");\n/* harmony import */ var _src_lib_machineStatusManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/lib/machineStatusManager */ \"(ssr)/./src/lib/machineStatusManager.ts\");\n/* harmony import */ var _src_utils_parseDuration__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/src/utils/parseDuration */ \"(ssr)/./src/utils/parseDuration.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n// Helper functions to convert between DB and app formats\nconst dbToMachine = (row)=>{\n    const graceEndAt = row.grace_end_at ? new Date(row.grace_end_at) : row.status === \"finishedGrace\" && row.end_at ? new Date(new Date(row.end_at).getTime() + 5 * 60 * 1000) : undefined;\n    return {\n        id: row.id,\n        name: row.name,\n        status: row.status,\n        startAt: row.start_at ? new Date(row.start_at) : undefined,\n        endAt: row.end_at ? new Date(row.end_at) : undefined,\n        graceEndAt: graceEndAt,\n        updatedAt: new Date(row.updated_at)\n    };\n};\nconst dbToNoise = (row)=>({\n        id: row.id,\n        user: row.user_name,\n        description: row.description || \"Noise reported\",\n        timestamp: new Date(row.timestamp),\n        lastReported: new Date(row.last_reported)\n    });\nconst dbToAnnouncement = (row)=>({\n        id: row.id,\n        user: row.user_name,\n        title: row.title,\n        description: row.description,\n        type: row.announcement_type,\n        timestamp: new Date(row.timestamp)\n    });\nconst dbToSublet = (row)=>({\n        id: row.id,\n        user: row.user_name,\n        duration: row.duration,\n        timestamp: new Date(row.timestamp)\n    });\nconst dbToHelpMe = (row)=>({\n        id: row.id,\n        user: row.user_name,\n        description: row.description,\n        timestamp: new Date(row.timestamp)\n    });\nconst dbToIncident = (row)=>({\n        id: row.id,\n        machineId: row.machine_id,\n        timestamp: new Date(row.timestamp),\n        type: row.incident_type\n    });\nfunction useSupabaseData() {\n    const [laundry, setLaundry] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [noise, setNoise] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [announcements, setAnnouncements] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [helpMe, setHelpMe] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [incidents, setIncidents] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [hasGraceEndColumn, setHasGraceEndColumn] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    // Refs to prevent recursive calls and multiple setups\n    const isLoadingDataRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const lastLoadTimeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const subscriptionSetupRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const statusManagerSetupRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const isMountedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(true);\n    // Debounced data loading function with selective loading\n    const loadAllData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[loadAllData]\": async (specificTable)=>{\n            const now = Date.now();\n            if (isLoadingDataRef.current || now - lastLoadTimeRef.current < 1000) {\n                console.log(\"⏭️ Skipping data load (too recent or in progress)\");\n                return;\n            }\n            if (!isMountedRef.current) {\n                console.log(\"⏭️ Skipping data load (component unmounted)\");\n                return;\n            }\n            isLoadingDataRef.current = true;\n            lastLoadTimeRef.current = now;\n            try {\n                console.log(`🔄 Loading data from Supabase${specificTable ? ` (${specificTable})` : \"\"}...`);\n                setError(null);\n                // If specific table is provided, only load that table\n                if (specificTable) {\n                    await loadSpecificTable(specificTable);\n                } else {\n                    // Load all data\n                    setIsLoading(true);\n                    await loadAllTables();\n                }\n                console.log(\"✅ Data loaded successfully\");\n            } catch (err) {\n                console.error(\"❌ Error loading data:\", err);\n                if (isMountedRef.current) {\n                    setError(err instanceof Error ? err.message : \"Failed to load data\");\n                }\n            } finally{\n                if (isMountedRef.current && !specificTable) {\n                    setIsLoading(false);\n                }\n                isLoadingDataRef.current = false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[loadAllData]\"], []);\n    // Helper function to load specific table\n    const loadSpecificTable = async (table)=>{\n        switch(table){\n            case \"machines\":\n                const machinesResult = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").select(\"*\").order(\"name\");\n                if (machinesResult.error) throw machinesResult.error;\n                setLaundry(machinesResult.data?.map(dbToMachine) || []);\n                break;\n            case \"noise_reports\":\n                const noiseResult = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"noise_reports\").select(\"*\").order(\"timestamp\", {\n                    ascending: false\n                }).limit(50);\n                if (noiseResult.error) throw noiseResult.error;\n                setNoise(noiseResult.data?.map(dbToNoise) || []);\n                break;\n            case \"announcements\":\n                const announcementsResult = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"announcements\").select(\"*\").order(\"timestamp\", {\n                    ascending: false\n                }).limit(50);\n                if (announcementsResult.error) throw announcementsResult.error;\n                setAnnouncements(announcementsResult.data?.map(dbToAnnouncement) || []);\n                break;\n            case \"help_requests\":\n                const helpResult = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"help_requests\").select(\"*\").gte(\"timestamp\", new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()).order(\"timestamp\", {\n                    ascending: false\n                }).limit(50);\n                if (helpResult.error) throw helpResult.error;\n                setHelpMe(helpResult.data?.map(dbToHelpMe) || []);\n                break;\n            case \"incidents\":\n                const incidentsResult = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"incidents\").select(\"*\").order(\"timestamp\", {\n                    ascending: false\n                }).limit(50);\n                if (incidentsResult.error) throw incidentsResult.error;\n                setIncidents(incidentsResult.data?.map(dbToIncident) || []);\n                break;\n        }\n    };\n    // Helper function to load all tables\n    const loadAllTables = async ()=>{\n        const timeoutPromise = new Promise((_, reject)=>setTimeout(()=>reject(new Error(\"Request timeout\")), 15000));\n        const dataPromise = Promise.all([\n            _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").select(\"*\").order(\"name\"),\n            _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"noise_reports\").select(\"*\").order(\"timestamp\", {\n                ascending: false\n            }).limit(50),\n            _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"announcements\").select(\"*\").order(\"timestamp\", {\n                ascending: false\n            }).limit(50),\n            _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"help_requests\").select(\"*\").gte(\"timestamp\", new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()).order(\"timestamp\", {\n                ascending: false\n            }).limit(50),\n            _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"incidents\").select(\"*\").order(\"timestamp\", {\n                ascending: false\n            }).limit(50)\n        ]);\n        const [machinesResult, noiseResult, announcementsResult, helpResult, incidentsResult] = await Promise.race([\n            dataPromise,\n            timeoutPromise\n        ]);\n        if (!isMountedRef.current) return;\n        if (machinesResult.error) throw new Error(`Failed to load machines: ${machinesResult.error.message}`);\n        if (noiseResult.error) throw new Error(`Failed to load noise reports: ${noiseResult.error.message}`);\n        if (announcementsResult.error) throw new Error(`Failed to load announcements: ${announcementsResult.error.message}`);\n        if (helpResult.error) throw new Error(`Failed to load help requests: ${helpResult.error.message}`);\n        if (incidentsResult.error) throw new Error(`Failed to load incidents: ${incidentsResult.error.message}`);\n        setLaundry(machinesResult.data?.map(dbToMachine) || []);\n        setNoise(noiseResult.data?.map(dbToNoise) || []);\n        setAnnouncements(announcementsResult.data?.map(dbToAnnouncement) || []);\n        setHelpMe(helpResult.data?.map(dbToHelpMe) || []);\n        setIncidents(incidentsResult.data?.map(dbToIncident) || []);\n    };\n    // Set up real-time subscriptions with table-specific updates\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseData.useEffect\": ()=>{\n            if (subscriptionSetupRef.current) {\n                return;\n            }\n            subscriptionSetupRef.current = true;\n            console.log(\"🔄 Setting up subscription manager...\");\n            const subscriptionManager = _src_lib_subscriptionManager__WEBPACK_IMPORTED_MODULE_2__[\"default\"].getInstance();\n            // Enhanced callback that handles specific table updates\n            const handleRealtimeUpdate = {\n                \"useSupabaseData.useEffect.handleRealtimeUpdate\": (table, event)=>{\n                    if (table && table !== \"polling\") {\n                        // Load only the specific table that changed for faster updates\n                        loadAllData(table);\n                    } else {\n                        // Fallback to loading all data\n                        loadAllData();\n                    }\n                }\n            }[\"useSupabaseData.useEffect.handleRealtimeUpdate\"];\n            subscriptionManager.addCallback(handleRealtimeUpdate);\n            return ({\n                \"useSupabaseData.useEffect\": ()=>{\n                    console.log(\"🧹 Cleaning up subscription manager...\");\n                    subscriptionManager.removeCallback(handleRealtimeUpdate);\n                    subscriptionSetupRef.current = false;\n                }\n            })[\"useSupabaseData.useEffect\"];\n        }\n    }[\"useSupabaseData.useEffect\"], [\n        loadAllData\n    ]);\n    // Set up machine status monitoring (only once)\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseData.useEffect\": ()=>{\n            if (statusManagerSetupRef.current) {\n                return;\n            }\n            statusManagerSetupRef.current = true;\n            console.log(\"🔄 Setting up machine status manager...\");\n            const statusManager = _src_lib_machineStatusManager__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getInstance();\n            statusManager.startStatusMonitoring();\n            return ({\n                \"useSupabaseData.useEffect\": ()=>{\n                    console.log(\"🧹 Cleaning up machine status manager...\");\n                    statusManager.stopStatusMonitoring();\n                    statusManagerSetupRef.current = false;\n                }\n            })[\"useSupabaseData.useEffect\"];\n        }\n    }[\"useSupabaseData.useEffect\"], []);\n    // Initial data load and cleanup\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseData.useEffect\": ()=>{\n            isMountedRef.current = true;\n            loadAllData();\n            return ({\n                \"useSupabaseData.useEffect\": ()=>{\n                    isMountedRef.current = false;\n                }\n            })[\"useSupabaseData.useEffect\"];\n        }\n    }[\"useSupabaseData.useEffect\"], []) // Only run once on mount\n    ;\n    // Machine operations with optimistic updates\n    const toggleMachineStatus = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[toggleMachineStatus]\": async (id)=>{\n            try {\n                const machine = laundry.find({\n                    \"useSupabaseData.useCallback[toggleMachineStatus].machine\": (m)=>m.id === id\n                }[\"useSupabaseData.useCallback[toggleMachineStatus].machine\"]);\n                if (!machine) {\n                    return false;\n                }\n                let optimisticUpdate;\n                let updateData;\n                if (machine.status === \"free\") {\n                    const startAt = new Date();\n                    const endAt = new Date(startAt.getTime() + 60 * 60 * 1000);\n                    optimisticUpdate = {\n                        ...machine,\n                        status: \"running\",\n                        startAt,\n                        endAt,\n                        graceEndAt: undefined,\n                        updatedAt: new Date()\n                    };\n                    updateData = {\n                        status: \"running\",\n                        start_at: startAt.toISOString(),\n                        end_at: endAt.toISOString()\n                    };\n                    if (hasGraceEndColumn) {\n                        updateData.grace_end_at = null;\n                    }\n                } else {\n                    optimisticUpdate = {\n                        ...machine,\n                        status: \"free\",\n                        startAt: undefined,\n                        endAt: undefined,\n                        graceEndAt: undefined,\n                        updatedAt: new Date()\n                    };\n                    updateData = {\n                        status: \"free\",\n                        start_at: null,\n                        end_at: null\n                    };\n                    if (hasGraceEndColumn) {\n                        updateData.grace_end_at = null;\n                    }\n                }\n                // Apply optimistic update\n                setLaundry({\n                    \"useSupabaseData.useCallback[toggleMachineStatus]\": (prev)=>prev.map({\n                            \"useSupabaseData.useCallback[toggleMachineStatus]\": (m)=>m.id === id ? optimisticUpdate : m\n                        }[\"useSupabaseData.useCallback[toggleMachineStatus]\"])\n                }[\"useSupabaseData.useCallback[toggleMachineStatus]\"]);\n                // Send update to database\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").update(updateData).eq(\"id\", id);\n                if (error) {\n                    if (error.message && error.message.includes(\"grace_end_at\")) {\n                        setHasGraceEndColumn(false);\n                        delete updateData.grace_end_at;\n                        const { error: retryError } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").update(updateData).eq(\"id\", id);\n                        if (retryError) {\n                            setLaundry({\n                                \"useSupabaseData.useCallback[toggleMachineStatus]\": (prev)=>prev.map({\n                                        \"useSupabaseData.useCallback[toggleMachineStatus]\": (m)=>m.id === id ? machine : m\n                                    }[\"useSupabaseData.useCallback[toggleMachineStatus]\"])\n                            }[\"useSupabaseData.useCallback[toggleMachineStatus]\"]);\n                            throw retryError;\n                        }\n                    } else {\n                        setLaundry({\n                            \"useSupabaseData.useCallback[toggleMachineStatus]\": (prev)=>prev.map({\n                                    \"useSupabaseData.useCallback[toggleMachineStatus]\": (m)=>m.id === id ? machine : m\n                                }[\"useSupabaseData.useCallback[toggleMachineStatus]\"])\n                        }[\"useSupabaseData.useCallback[toggleMachineStatus]\"]);\n                        throw error;\n                    }\n                }\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to update machine\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[toggleMachineStatus]\"], [\n        laundry,\n        hasGraceEndColumn\n    ]);\n    const reserveMachine = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[reserveMachine]\": async (id, durationStr)=>{\n            try {\n                const machine = laundry.find({\n                    \"useSupabaseData.useCallback[reserveMachine].machine\": (m)=>m.id === id\n                }[\"useSupabaseData.useCallback[reserveMachine].machine\"]);\n                if (!machine) {\n                    return false;\n                }\n                const durationSeconds = (0,_src_utils_parseDuration__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(durationStr);\n                const startAt = new Date();\n                const endAt = new Date(startAt.getTime() + durationSeconds * 1000);\n                const optimisticUpdate = {\n                    ...machine,\n                    status: \"running\",\n                    startAt,\n                    endAt,\n                    graceEndAt: undefined,\n                    updatedAt: new Date()\n                };\n                setLaundry({\n                    \"useSupabaseData.useCallback[reserveMachine]\": (prev)=>prev.map({\n                            \"useSupabaseData.useCallback[reserveMachine]\": (m)=>m.id === id ? optimisticUpdate : m\n                        }[\"useSupabaseData.useCallback[reserveMachine]\"])\n                }[\"useSupabaseData.useCallback[reserveMachine]\"]);\n                const updateData = {\n                    status: \"running\",\n                    start_at: startAt.toISOString(),\n                    end_at: endAt.toISOString()\n                };\n                if (hasGraceEndColumn) {\n                    updateData.grace_end_at = null;\n                }\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").update(updateData).eq(\"id\", id);\n                if (error) {\n                    if (error.message && error.message.includes(\"grace_end_at\")) {\n                        setHasGraceEndColumn(false);\n                        delete updateData.grace_end_at;\n                        const { error: retryError } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").update(updateData).eq(\"id\", id);\n                        if (retryError) {\n                            setLaundry({\n                                \"useSupabaseData.useCallback[reserveMachine]\": (prev)=>prev.map({\n                                        \"useSupabaseData.useCallback[reserveMachine]\": (m)=>m.id === id ? machine : m\n                                    }[\"useSupabaseData.useCallback[reserveMachine]\"])\n                            }[\"useSupabaseData.useCallback[reserveMachine]\"]);\n                            throw retryError;\n                        }\n                    } else {\n                        setLaundry({\n                            \"useSupabaseData.useCallback[reserveMachine]\": (prev)=>prev.map({\n                                    \"useSupabaseData.useCallback[reserveMachine]\": (m)=>m.id === id ? machine : m\n                                }[\"useSupabaseData.useCallback[reserveMachine]\"])\n                        }[\"useSupabaseData.useCallback[reserveMachine]\"]);\n                        throw error;\n                    }\n                }\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to reserve machine\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[reserveMachine]\"], [\n        laundry,\n        hasGraceEndColumn\n    ]);\n    // Simplified CRUD operations\n    const addNoiseWithDescription = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[addNoiseWithDescription]\": async (user, description)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"noise_reports\").insert({\n                    id: Date.now().toString(),\n                    user_name: user,\n                    description: description\n                });\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to add noise report\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[addNoiseWithDescription]\"], []);\n    const deleteNoise = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[deleteNoise]\": async (id)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"noise_reports\").delete().eq(\"id\", id);\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to delete noise report\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[deleteNoise]\"], []);\n    const addAnnouncement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[addAnnouncement]\": async (user, title, description, type)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"announcements\").insert({\n                    id: Date.now().toString(),\n                    user_name: user,\n                    title: title,\n                    description: description,\n                    announcement_type: type\n                });\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to add announcement\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[addAnnouncement]\"], []);\n    const deleteAnnouncement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[deleteAnnouncement]\": async (id)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"announcements\").delete().eq(\"id\", id);\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to delete announcement\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[deleteAnnouncement]\"], []);\n    const addHelpRequest = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[addHelpRequest]\": async (user, description)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"help_requests\").insert({\n                    id: Date.now().toString(),\n                    user_name: user,\n                    description\n                });\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to add help request\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[addHelpRequest]\"], []);\n    const deleteHelpRequest = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[deleteHelpRequest]\": async (id)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"help_requests\").delete().eq(\"id\", id);\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to delete help request\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[deleteHelpRequest]\"], []);\n    const deleteIncident = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[deleteIncident]\": async (id)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"incidents\").delete().eq(\"id\", id);\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to delete incident\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[deleteIncident]\"], []);\n    return {\n        // Data\n        laundry,\n        noise,\n        announcements,\n        helpMe,\n        incidents,\n        // State\n        isLoading,\n        error,\n        // Operations\n        toggleMachineStatus,\n        reserveMachine,\n        addNoiseWithDescription,\n        deleteNoise,\n        addAnnouncement,\n        deleteAnnouncement,\n        addHelpRequest,\n        deleteHelpRequest,\n        deleteIncident,\n        refreshData: loadAllData\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useSupabaseData.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/machineStatusManager.ts":
/*!*****************************************!*\
  !*** ./src/lib/machineStatusManager.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(ssr)/./src/lib/supabase.ts\");\n\nclass MachineStatusManager {\n    static getInstance() {\n        if (!MachineStatusManager.instance) {\n            MachineStatusManager.instance = new MachineStatusManager();\n        }\n        return MachineStatusManager.instance;\n    }\n    startStatusMonitoring() {\n        if (this.statusCheckInterval || this.isDestroyed) {\n            return;\n        }\n        console.log(\"🔄 Starting machine status monitoring...\");\n        // Check every 30 seconds (less aggressive)\n        this.statusCheckInterval = setInterval(()=>{\n            if (!this.isDestroyed) {\n                this.checkMachineStatuses();\n            }\n        }, 30000);\n        // Also check immediately\n        this.checkMachineStatuses();\n    }\n    stopStatusMonitoring() {\n        if (this.statusCheckInterval) {\n            clearInterval(this.statusCheckInterval);\n            this.statusCheckInterval = null;\n            console.log(\"⏹️ Stopped machine status monitoring\");\n        }\n    }\n    destroy() {\n        this.isDestroyed = true;\n        this.stopStatusMonitoring();\n    }\n    async checkMachineStatuses() {\n        if (this.isChecking || this.isDestroyed) {\n            return;\n        }\n        this.isChecking = true;\n        try {\n            const now = new Date();\n            const { data: machines, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"machines\").select(\"*\").in(\"status\", [\n                \"running\",\n                \"finishedGrace\"\n            ]);\n            if (error) {\n                console.error(\"❌ Error fetching machines for status check:\", error);\n                return;\n            }\n            if (!machines || machines.length === 0) {\n                return;\n            }\n            const updates = [];\n            for (const machine of machines){\n                if (this.isDestroyed) break;\n                const endAt = machine.end_at ? new Date(machine.end_at) : null;\n                let graceEndAt = null;\n                if (this.hasGraceEndColumn && machine.grace_end_at) {\n                    graceEndAt = new Date(machine.grace_end_at);\n                }\n                if (machine.status === \"running\" && endAt && now >= endAt) {\n                    const graceEnd = new Date(now.getTime() + 5 * 60 * 1000);\n                    const updateObj = {\n                        status: \"finishedGrace\"\n                    };\n                    if (this.hasGraceEndColumn) {\n                        updateObj.grace_end_at = graceEnd.toISOString();\n                    }\n                    updates.push({\n                        id: machine.id,\n                        updates: updateObj\n                    });\n                    console.log(`⚠️ Machine ${machine.name} transitioning to grace period`);\n                } else if (machine.status === \"finishedGrace\") {\n                    const defaultGraceEnd = endAt ? new Date(endAt.getTime() + 5 * 60 * 1000) : null;\n                    const effectiveGraceEnd = graceEndAt || defaultGraceEnd;\n                    if (effectiveGraceEnd && now >= effectiveGraceEnd) {\n                        updates.push({\n                            id: machine.id,\n                            updates: {\n                                status: \"free\",\n                                start_at: null,\n                                end_at: null,\n                                ...this.hasGraceEndColumn ? {\n                                    grace_end_at: null\n                                } : {}\n                            }\n                        });\n                        console.log(`✅ Machine ${machine.name} grace period ended, now available`);\n                    }\n                }\n            }\n            // Apply updates sequentially to avoid conflicts\n            for (const update of updates){\n                if (this.isDestroyed) break;\n                try {\n                    const { error: updateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"machines\").update(update.updates).eq(\"id\", update.id);\n                    if (updateError) {\n                        if (updateError.message && updateError.message.includes(\"grace_end_at\")) {\n                            console.log(\"⚠️ grace_end_at column not found, disabling this feature\");\n                            this.hasGraceEndColumn = false;\n                        }\n                        console.error(`❌ Error updating machine ${update.id}:`, updateError);\n                    }\n                } catch (err) {\n                    console.error(`❌ Error updating machine ${update.id}:`, err);\n                }\n                // Small delay between updates to prevent overwhelming the database\n                await new Promise((resolve)=>setTimeout(resolve, 100));\n            }\n            if (updates.length > 0) {\n                console.log(`🔄 Updated ${updates.length} machine statuses`);\n            }\n        } catch (error) {\n            console.error(\"❌ Error in machine status check:\", error);\n        } finally{\n            this.isChecking = false;\n        }\n    }\n    async triggerStatusCheck() {\n        if (!this.isDestroyed) {\n            await this.checkMachineStatuses();\n        }\n    }\n    constructor(){\n        this.statusCheckInterval = null;\n        this.isChecking = false;\n        this.hasGraceEndColumn = true;\n        this.isDestroyed = false;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MachineStatusManager);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/machineStatusManager.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/subscriptionManager.ts":
/*!****************************************!*\
  !*** ./src/lib/subscriptionManager.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(ssr)/./src/lib/supabase.ts\");\n\nclass SubscriptionManager {\n    static getInstance() {\n        if (!SubscriptionManager.instance) {\n            SubscriptionManager.instance = new SubscriptionManager();\n        }\n        return SubscriptionManager.instance;\n    }\n    addCallback(callback) {\n        if (this.isDestroyed) return;\n        this.callbacks.add(callback);\n        console.log(`📡 Added callback, total: ${this.callbacks.size}`);\n        if (this.callbacks.size === 1 && !this.isSubscribed) {\n            this.initializeSubscription();\n        }\n    }\n    removeCallback(callback) {\n        this.callbacks.delete(callback);\n        console.log(`📡 Removed callback, total: ${this.callbacks.size}`);\n        if (this.callbacks.size === 0) {\n            this.cleanup();\n        }\n    }\n    async initializeSubscription() {\n        if (this.isSubscribed || this.channel || this.isDestroyed) {\n            return;\n        }\n        console.log(\"🔄 Initializing Supabase subscription...\");\n        this.isSubscribed = true;\n        this.subscriptionId++;\n        try {\n            const channelName = `dorm-dashboard-${this.subscriptionId}-${Date.now()}`;\n            this.channel = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.channel(channelName, {\n                config: {\n                    broadcast: {\n                        self: false\n                    },\n                    presence: {\n                        key: \"\"\n                    }\n                }\n            }).on(\"postgres_changes\", {\n                event: \"*\",\n                schema: \"public\",\n                table: \"machines\"\n            }, (payload)=>{\n                this.handleChange(\"machines\", payload.eventType);\n            }).on(\"postgres_changes\", {\n                event: \"*\",\n                schema: \"public\",\n                table: \"noise_reports\"\n            }, (payload)=>{\n                this.handleChange(\"noise_reports\", payload.eventType);\n            }).on(\"postgres_changes\", {\n                event: \"*\",\n                schema: \"public\",\n                table: \"announcements\"\n            }, (payload)=>{\n                this.handleChange(\"announcements\", payload.eventType);\n            }).on(\"postgres_changes\", {\n                event: \"*\",\n                schema: \"public\",\n                table: \"help_requests\"\n            }, (payload)=>{\n                this.handleChange(\"help_requests\", payload.eventType);\n            }).on(\"postgres_changes\", {\n                event: \"*\",\n                schema: \"public\",\n                table: \"incidents\"\n            }, (payload)=>{\n                this.handleChange(\"incidents\", payload.eventType);\n            }).subscribe((status)=>{\n                console.log(`📡 Subscription status: ${status}`);\n                if (status === \"SUBSCRIBED\") {\n                    console.log(\"✅ Successfully subscribed to realtime updates\");\n                } else if (status === \"CHANNEL_ERROR\" || status === \"TIMED_OUT\" || status === \"CLOSED\") {\n                    console.log(\"❌ Subscription error, will retry...\");\n                    this.handleSubscriptionError();\n                }\n            });\n            // Reduced polling frequency\n            this.startPolling();\n        } catch (error) {\n            console.error(\"❌ Error initializing subscription:\", error);\n            this.handleSubscriptionError();\n        }\n    }\n    notifyCallbacks(table, event) {\n        if (this.isDestroyed) return;\n        // Use setTimeout to prevent stack overflow and allow for immediate UI updates\n        setTimeout(()=>{\n            if (this.isDestroyed) return;\n            this.callbacks.forEach((callback)=>{\n                try {\n                    callback(table, event);\n                } catch (error) {\n                    console.error(\"❌ Error in subscription callback:\", error);\n                }\n            });\n        }, 50) // Reduced delay for faster updates\n        ;\n    }\n    startPolling() {\n        if (this.pollingInterval || this.isDestroyed) {\n            return;\n        }\n        // Polling every 60 seconds as fallback\n        this.pollingInterval = setInterval(()=>{\n            if (this.isDestroyed) return;\n            console.log(\"🔄 Polling for changes...\");\n            this.notifyCallbacks(\"polling\", \"fallback\");\n        }, 60000);\n    }\n    handleSubscriptionError() {\n        if (this.isDestroyed) return;\n        this.cleanup();\n        setTimeout(()=>{\n            if (this.callbacks.size > 0 && !this.isDestroyed) {\n                console.log(\"🔄 Retrying subscription...\");\n                this.initializeSubscription();\n            }\n        }, 3000);\n    }\n    cleanup() {\n        console.log(\"🧹 Cleaning up subscription manager...\");\n        this.isSubscribed = false;\n        if (this.channel) {\n            try {\n                this.channel.unsubscribe();\n                _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.removeChannel(this.channel);\n            } catch (error) {\n                console.warn(\"⚠️ Error during channel cleanup:\", error);\n            }\n            this.channel = null;\n        }\n        if (this.pollingInterval) {\n            clearInterval(this.pollingInterval);\n            this.pollingInterval = null;\n        }\n    }\n    forceCleanup() {\n        console.log(\"🧹 Force cleaning up subscription manager...\");\n        this.isDestroyed = true;\n        this.callbacks.clear();\n        this.cleanup();\n    }\n    triggerSync() {\n        if (this.isDestroyed) return;\n        this.notifyCallbacks(\"manual\", \"trigger\");\n    }\n    constructor(){\n        this.channel = null;\n        this.callbacks = new Set();\n        this.isSubscribed = false;\n        this.pollingInterval = null;\n        this.isDestroyed = false;\n        this.subscriptionId = 0;\n        this.lastChangeTime = 0;\n        this.handleChange = (table, event)=>{\n            if (this.isDestroyed) return;\n            const now = Date.now();\n            // Debounce rapid changes (max once per 500ms)\n            if (now - this.lastChangeTime < 500) {\n                return;\n            }\n            this.lastChangeTime = now;\n            console.log(`📡 Database change detected: ${table} - ${event}`);\n            this.notifyCallbacks(table, event);\n        };\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SubscriptionManager);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/subscriptionManager.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   testSupabaseConnection: () => (/* binding */ testSupabaseConnection)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.0/node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://aodjyjxsqfytythosrka.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFvZGp5anhzcWZ5dHl0aG9zcmthIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1MDYzOTYsImV4cCI6MjA2NTA4MjM5Nn0.V0K-uTAl8FDkupkjYZn9R6P4qIMhJ0kX1iE4LQFr_tg\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n    realtime: {\n        params: {\n            eventsPerSecond: 50\n        }\n    },\n    auth: {\n        persistSession: false\n    }\n});\n// Test connection function\nasync function testSupabaseConnection() {\n    try {\n        const { data, error } = await supabase.from(\"machines\").select(\"count\").single();\n        if (error) throw error;\n        return {\n            success: true,\n            data\n        };\n    } catch (error) {\n        return {\n            success: false,\n            error\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/parseDuration.tsx":
/*!*************************************!*\
  !*** ./src/utils/parseDuration.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ parseDuration)\n/* harmony export */ });\nfunction parseDuration(durationStr) {\n    const trimmed = durationStr.trim().toLowerCase();\n    // Match patterns like \"1 hr 30 min\", \"2 hours\", \"45 minutes\", etc.\n    const hourMatch = trimmed.match(/(\\d+)\\s*(hr|hour|hours)/);\n    const minuteMatch = trimmed.match(/(\\d+)\\s*(min|minute|minutes)/);\n    let totalSeconds = 0;\n    if (hourMatch) {\n        totalSeconds += Number.parseInt(hourMatch[1]) * 3600;\n    }\n    if (minuteMatch) {\n        totalSeconds += Number.parseInt(minuteMatch[1]) * 60;\n    }\n    // If no valid time found, throw error\n    if (totalSeconds === 0) {\n        throw new Error(`Invalid duration format: ${durationStr}`);\n    }\n    return totalSeconds;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/parseDuration.tsx\n");

/***/ }),

/***/ "(ssr)/./src/utils/userIdentification.ts":
/*!*****************************************!*\
  !*** ./src/utils/userIdentification.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDeviceUserId: () => (/* binding */ getDeviceUserId),\n/* harmony export */   getUserDisplayName: () => (/* binding */ getUserDisplayName),\n/* harmony export */   isCurrentUserPost: () => (/* binding */ isCurrentUserPost),\n/* harmony export */   setUserDisplayName: () => (/* binding */ setUserDisplayName)\n/* harmony export */ });\n// Generate a consistent user ID for this device\nfunction getDeviceUserId() {\n    // Return a default value during SSR\n    if (true) return \"User1\";\n    try {\n        let userId = localStorage.getItem(\"dorm-dashboard-user-id\");\n        if (!userId) {\n            // Generate a more robust device fingerprint\n            const fingerprint = generateDeviceFingerprint();\n            const hash = hashString(fingerprint);\n            const userNumber = Math.abs(hash % 9999) + 1;\n            userId = `User${userNumber.toString().padStart(4, \"0\")}`;\n            localStorage.setItem(\"dorm-dashboard-user-id\", userId);\n        }\n        return userId;\n    } catch (error) {\n        return \"User1\";\n    }\n}\n// Generate a device fingerprint\nfunction generateDeviceFingerprint() {\n    const canvas = document.createElement(\"canvas\");\n    const ctx = canvas.getContext(\"2d\");\n    let fingerprint = \"\";\n    if (ctx) {\n        ctx.textBaseline = \"top\";\n        ctx.font = \"14px Arial\";\n        ctx.fillText(\"Device fingerprint\", 2, 2);\n        fingerprint += canvas.toDataURL();\n    }\n    // Add more device characteristics\n    fingerprint += navigator.userAgent;\n    fingerprint += navigator.language;\n    fingerprint += screen.width + \"x\" + screen.height;\n    fingerprint += new Date().getTimezoneOffset();\n    fingerprint += navigator.hardwareConcurrency || \"unknown\";\n    return fingerprint;\n}\n// Simple hash function\nfunction hashString(str) {\n    let hash = 0;\n    for(let i = 0; i < str.length; i++){\n        const char = str.charCodeAt(i);\n        hash = (hash << 5) - hash + char;\n        hash = hash & hash // Convert to 32-bit integer\n        ;\n    }\n    return hash;\n}\n// Get a display name for the user\nfunction getUserDisplayName() {\n    if (true) return \"User\";\n    try {\n        const displayName = localStorage.getItem(\"dorm-dashboard-display-name\");\n        if (displayName) {\n            return displayName;\n        }\n        return getDeviceUserId();\n    } catch (error) {\n        return \"User\";\n    }\n}\n// Set a custom display name\nfunction setUserDisplayName(name) {\n    if (true) return;\n    try {\n        // Sanitize the display name\n        const sanitized = name.trim().slice(0, 20) // Max 20 characters\n        ;\n        if (sanitized) {\n            localStorage.setItem(\"dorm-dashboard-display-name\", sanitized);\n        }\n    } catch (error) {\n        console.warn(\"Could not save display name:\", error);\n    }\n}\n// Check if current user owns a post\nfunction isCurrentUserPost(postUserId) {\n    if (true) return false;\n    const currentUserId = getDeviceUserId();\n    const currentDisplayName = getUserDisplayName();\n    // Check both user ID and display name for backwards compatibility\n    return postUserId === currentUserId || postUserId === currentDisplayName;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/userIdentification.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?8bfc":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?cf7b":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0","vendor-chunks/tr46@0.0.3","vendor-chunks/@supabase+auth-js@2.70.0","vendor-chunks/ws@8.18.2","vendor-chunks/@supabase+realtime-js@2.11.10","vendor-chunks/@supabase+postgrest-js@1.19.4","vendor-chunks/@supabase+node-fetch@2.6.15","vendor-chunks/whatwg-url@5.0.0","vendor-chunks/@supabase+storage-js@2.7.1","vendor-chunks/@supabase+supabase-js@2.50.0","vendor-chunks/@supabase+functions-js@2.4.4","vendor-chunks/webidl-conversions@3.0.1","vendor-chunks/@swc+helpers@0.5.15"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmemet%5CDesktop%5Cdorm_21%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmemet%5CDesktop%5Cdorm_21&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();