"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@supabase+realtime-js@2.11.10";
exports.ids = ["vendor-chunks/@supabase+realtime-js@2.11.10"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/RealtimeChannel.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/RealtimeChannel.js ***!
  \**************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || (function () {\n    var ownKeys = function(o) {\n        ownKeys = Object.getOwnPropertyNames || function (o) {\n            var ar = [];\n            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n            return ar;\n        };\n        return ownKeys(o);\n    };\n    return function (mod) {\n        if (mod && mod.__esModule) return mod;\n        var result = {};\n        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n        __setModuleDefault(result, mod);\n        return result;\n    };\n})();\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.REALTIME_CHANNEL_STATES = exports.REALTIME_SUBSCRIBE_STATES = exports.REALTIME_LISTEN_TYPES = exports.REALTIME_POSTGRES_CHANGES_LISTEN_EVENT = void 0;\nconst constants_1 = __webpack_require__(/*! ./lib/constants */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/lib/constants.js\");\nconst push_1 = __importDefault(__webpack_require__(/*! ./lib/push */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/lib/push.js\"));\nconst timer_1 = __importDefault(__webpack_require__(/*! ./lib/timer */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/lib/timer.js\"));\nconst RealtimePresence_1 = __importDefault(__webpack_require__(/*! ./RealtimePresence */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/RealtimePresence.js\"));\nconst Transformers = __importStar(__webpack_require__(/*! ./lib/transformers */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/lib/transformers.js\"));\nconst transformers_1 = __webpack_require__(/*! ./lib/transformers */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/lib/transformers.js\");\nvar REALTIME_POSTGRES_CHANGES_LISTEN_EVENT;\n(function (REALTIME_POSTGRES_CHANGES_LISTEN_EVENT) {\n    REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"ALL\"] = \"*\";\n    REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"INSERT\"] = \"INSERT\";\n    REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"UPDATE\"] = \"UPDATE\";\n    REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"DELETE\"] = \"DELETE\";\n})(REALTIME_POSTGRES_CHANGES_LISTEN_EVENT || (exports.REALTIME_POSTGRES_CHANGES_LISTEN_EVENT = REALTIME_POSTGRES_CHANGES_LISTEN_EVENT = {}));\nvar REALTIME_LISTEN_TYPES;\n(function (REALTIME_LISTEN_TYPES) {\n    REALTIME_LISTEN_TYPES[\"BROADCAST\"] = \"broadcast\";\n    REALTIME_LISTEN_TYPES[\"PRESENCE\"] = \"presence\";\n    REALTIME_LISTEN_TYPES[\"POSTGRES_CHANGES\"] = \"postgres_changes\";\n    REALTIME_LISTEN_TYPES[\"SYSTEM\"] = \"system\";\n})(REALTIME_LISTEN_TYPES || (exports.REALTIME_LISTEN_TYPES = REALTIME_LISTEN_TYPES = {}));\nvar REALTIME_SUBSCRIBE_STATES;\n(function (REALTIME_SUBSCRIBE_STATES) {\n    REALTIME_SUBSCRIBE_STATES[\"SUBSCRIBED\"] = \"SUBSCRIBED\";\n    REALTIME_SUBSCRIBE_STATES[\"TIMED_OUT\"] = \"TIMED_OUT\";\n    REALTIME_SUBSCRIBE_STATES[\"CLOSED\"] = \"CLOSED\";\n    REALTIME_SUBSCRIBE_STATES[\"CHANNEL_ERROR\"] = \"CHANNEL_ERROR\";\n})(REALTIME_SUBSCRIBE_STATES || (exports.REALTIME_SUBSCRIBE_STATES = REALTIME_SUBSCRIBE_STATES = {}));\nexports.REALTIME_CHANNEL_STATES = constants_1.CHANNEL_STATES;\n/** A channel is the basic building block of Realtime\n * and narrows the scope of data flow to subscribed clients.\n * You can think of a channel as a chatroom where participants are able to see who's online\n * and send and receive messages.\n */\nclass RealtimeChannel {\n    constructor(\n    /** Topic name can be any string. */\n    topic, params = { config: {} }, socket) {\n        this.topic = topic;\n        this.params = params;\n        this.socket = socket;\n        this.bindings = {};\n        this.state = constants_1.CHANNEL_STATES.closed;\n        this.joinedOnce = false;\n        this.pushBuffer = [];\n        this.subTopic = topic.replace(/^realtime:/i, '');\n        this.params.config = Object.assign({\n            broadcast: { ack: false, self: false },\n            presence: { key: '' },\n            private: false,\n        }, params.config);\n        this.timeout = this.socket.timeout;\n        this.joinPush = new push_1.default(this, constants_1.CHANNEL_EVENTS.join, this.params, this.timeout);\n        this.rejoinTimer = new timer_1.default(() => this._rejoinUntilConnected(), this.socket.reconnectAfterMs);\n        this.joinPush.receive('ok', () => {\n            this.state = constants_1.CHANNEL_STATES.joined;\n            this.rejoinTimer.reset();\n            this.pushBuffer.forEach((pushEvent) => pushEvent.send());\n            this.pushBuffer = [];\n        });\n        this._onClose(() => {\n            this.rejoinTimer.reset();\n            this.socket.log('channel', `close ${this.topic} ${this._joinRef()}`);\n            this.state = constants_1.CHANNEL_STATES.closed;\n            this.socket._remove(this);\n        });\n        this._onError((reason) => {\n            if (this._isLeaving() || this._isClosed()) {\n                return;\n            }\n            this.socket.log('channel', `error ${this.topic}`, reason);\n            this.state = constants_1.CHANNEL_STATES.errored;\n            this.rejoinTimer.scheduleTimeout();\n        });\n        this.joinPush.receive('timeout', () => {\n            if (!this._isJoining()) {\n                return;\n            }\n            this.socket.log('channel', `timeout ${this.topic}`, this.joinPush.timeout);\n            this.state = constants_1.CHANNEL_STATES.errored;\n            this.rejoinTimer.scheduleTimeout();\n        });\n        this._on(constants_1.CHANNEL_EVENTS.reply, {}, (payload, ref) => {\n            this._trigger(this._replyEventName(ref), payload);\n        });\n        this.presence = new RealtimePresence_1.default(this);\n        this.broadcastEndpointURL =\n            (0, transformers_1.httpEndpointURL)(this.socket.endPoint) + '/api/broadcast';\n        this.private = this.params.config.private || false;\n    }\n    /** Subscribe registers your client with the server */\n    subscribe(callback, timeout = this.timeout) {\n        var _a, _b;\n        if (!this.socket.isConnected()) {\n            this.socket.connect();\n        }\n        if (this.joinedOnce) {\n            throw `tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance`;\n        }\n        else {\n            const { config: { broadcast, presence, private: isPrivate }, } = this.params;\n            this._onError((e) => callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR, e));\n            this._onClose(() => callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CLOSED));\n            const accessTokenPayload = {};\n            const config = {\n                broadcast,\n                presence,\n                postgres_changes: (_b = (_a = this.bindings.postgres_changes) === null || _a === void 0 ? void 0 : _a.map((r) => r.filter)) !== null && _b !== void 0 ? _b : [],\n                private: isPrivate,\n            };\n            if (this.socket.accessTokenValue) {\n                accessTokenPayload.access_token = this.socket.accessTokenValue;\n            }\n            this.updateJoinPayload(Object.assign({ config }, accessTokenPayload));\n            this.joinedOnce = true;\n            this._rejoin(timeout);\n            this.joinPush\n                .receive('ok', async ({ postgres_changes }) => {\n                var _a;\n                this.socket.setAuth();\n                if (postgres_changes === undefined) {\n                    callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.SUBSCRIBED);\n                    return;\n                }\n                else {\n                    const clientPostgresBindings = this.bindings.postgres_changes;\n                    const bindingsLen = (_a = clientPostgresBindings === null || clientPostgresBindings === void 0 ? void 0 : clientPostgresBindings.length) !== null && _a !== void 0 ? _a : 0;\n                    const newPostgresBindings = [];\n                    for (let i = 0; i < bindingsLen; i++) {\n                        const clientPostgresBinding = clientPostgresBindings[i];\n                        const { filter: { event, schema, table, filter }, } = clientPostgresBinding;\n                        const serverPostgresFilter = postgres_changes && postgres_changes[i];\n                        if (serverPostgresFilter &&\n                            serverPostgresFilter.event === event &&\n                            serverPostgresFilter.schema === schema &&\n                            serverPostgresFilter.table === table &&\n                            serverPostgresFilter.filter === filter) {\n                            newPostgresBindings.push(Object.assign(Object.assign({}, clientPostgresBinding), { id: serverPostgresFilter.id }));\n                        }\n                        else {\n                            this.unsubscribe();\n                            this.state = constants_1.CHANNEL_STATES.errored;\n                            callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR, new Error('mismatch between server and client bindings for postgres changes'));\n                            return;\n                        }\n                    }\n                    this.bindings.postgres_changes = newPostgresBindings;\n                    callback && callback(REALTIME_SUBSCRIBE_STATES.SUBSCRIBED);\n                    return;\n                }\n            })\n                .receive('error', (error) => {\n                this.state = constants_1.CHANNEL_STATES.errored;\n                callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR, new Error(JSON.stringify(Object.values(error).join(', ') || 'error')));\n                return;\n            })\n                .receive('timeout', () => {\n                callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.TIMED_OUT);\n                return;\n            });\n        }\n        return this;\n    }\n    presenceState() {\n        return this.presence.state;\n    }\n    async track(payload, opts = {}) {\n        return await this.send({\n            type: 'presence',\n            event: 'track',\n            payload,\n        }, opts.timeout || this.timeout);\n    }\n    async untrack(opts = {}) {\n        return await this.send({\n            type: 'presence',\n            event: 'untrack',\n        }, opts);\n    }\n    on(type, filter, callback) {\n        return this._on(type, filter, callback);\n    }\n    /**\n     * Sends a message into the channel.\n     *\n     * @param args Arguments to send to channel\n     * @param args.type The type of event to send\n     * @param args.event The name of the event being sent\n     * @param args.payload Payload to be sent\n     * @param opts Options to be used during the send process\n     */\n    async send(args, opts = {}) {\n        var _a, _b;\n        if (!this._canPush() && args.type === 'broadcast') {\n            const { event, payload: endpoint_payload } = args;\n            const authorization = this.socket.accessTokenValue\n                ? `Bearer ${this.socket.accessTokenValue}`\n                : '';\n            const options = {\n                method: 'POST',\n                headers: {\n                    Authorization: authorization,\n                    apikey: this.socket.apiKey ? this.socket.apiKey : '',\n                    'Content-Type': 'application/json',\n                },\n                body: JSON.stringify({\n                    messages: [\n                        {\n                            topic: this.subTopic,\n                            event,\n                            payload: endpoint_payload,\n                            private: this.private,\n                        },\n                    ],\n                }),\n            };\n            try {\n                const response = await this._fetchWithTimeout(this.broadcastEndpointURL, options, (_a = opts.timeout) !== null && _a !== void 0 ? _a : this.timeout);\n                await ((_b = response.body) === null || _b === void 0 ? void 0 : _b.cancel());\n                return response.ok ? 'ok' : 'error';\n            }\n            catch (error) {\n                if (error.name === 'AbortError') {\n                    return 'timed out';\n                }\n                else {\n                    return 'error';\n                }\n            }\n        }\n        else {\n            return new Promise((resolve) => {\n                var _a, _b, _c;\n                const push = this._push(args.type, args, opts.timeout || this.timeout);\n                if (args.type === 'broadcast' && !((_c = (_b = (_a = this.params) === null || _a === void 0 ? void 0 : _a.config) === null || _b === void 0 ? void 0 : _b.broadcast) === null || _c === void 0 ? void 0 : _c.ack)) {\n                    resolve('ok');\n                }\n                push.receive('ok', () => resolve('ok'));\n                push.receive('error', () => resolve('error'));\n                push.receive('timeout', () => resolve('timed out'));\n            });\n        }\n    }\n    updateJoinPayload(payload) {\n        this.joinPush.updatePayload(payload);\n    }\n    /**\n     * Leaves the channel.\n     *\n     * Unsubscribes from server events, and instructs channel to terminate on server.\n     * Triggers onClose() hooks.\n     *\n     * To receive leave acknowledgements, use the a `receive` hook to bind to the server ack, ie:\n     * channel.unsubscribe().receive(\"ok\", () => alert(\"left!\") )\n     */\n    unsubscribe(timeout = this.timeout) {\n        this.state = constants_1.CHANNEL_STATES.leaving;\n        const onClose = () => {\n            this.socket.log('channel', `leave ${this.topic}`);\n            this._trigger(constants_1.CHANNEL_EVENTS.close, 'leave', this._joinRef());\n        };\n        this.joinPush.destroy();\n        return new Promise((resolve) => {\n            const leavePush = new push_1.default(this, constants_1.CHANNEL_EVENTS.leave, {}, timeout);\n            leavePush\n                .receive('ok', () => {\n                onClose();\n                resolve('ok');\n            })\n                .receive('timeout', () => {\n                onClose();\n                resolve('timed out');\n            })\n                .receive('error', () => {\n                resolve('error');\n            });\n            leavePush.send();\n            if (!this._canPush()) {\n                leavePush.trigger('ok', {});\n            }\n        });\n    }\n    /**\n     * Teardown the channel.\n     *\n     * Destroys and stops related timers.\n     */\n    teardown() {\n        this.pushBuffer.forEach((push) => push.destroy());\n        this.rejoinTimer && clearTimeout(this.rejoinTimer.timer);\n        this.joinPush.destroy();\n    }\n    /** @internal */\n    async _fetchWithTimeout(url, options, timeout) {\n        const controller = new AbortController();\n        const id = setTimeout(() => controller.abort(), timeout);\n        const response = await this.socket.fetch(url, Object.assign(Object.assign({}, options), { signal: controller.signal }));\n        clearTimeout(id);\n        return response;\n    }\n    /** @internal */\n    _push(event, payload, timeout = this.timeout) {\n        if (!this.joinedOnce) {\n            throw `tried to push '${event}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;\n        }\n        let pushEvent = new push_1.default(this, event, payload, timeout);\n        if (this._canPush()) {\n            pushEvent.send();\n        }\n        else {\n            pushEvent.startTimeout();\n            this.pushBuffer.push(pushEvent);\n        }\n        return pushEvent;\n    }\n    /**\n     * Overridable message hook\n     *\n     * Receives all events for specialized message handling before dispatching to the channel callbacks.\n     * Must return the payload, modified or unmodified.\n     *\n     * @internal\n     */\n    _onMessage(_event, payload, _ref) {\n        return payload;\n    }\n    /** @internal */\n    _isMember(topic) {\n        return this.topic === topic;\n    }\n    /** @internal */\n    _joinRef() {\n        return this.joinPush.ref;\n    }\n    /** @internal */\n    _trigger(type, payload, ref) {\n        var _a, _b;\n        const typeLower = type.toLocaleLowerCase();\n        const { close, error, leave, join } = constants_1.CHANNEL_EVENTS;\n        const events = [close, error, leave, join];\n        if (ref && events.indexOf(typeLower) >= 0 && ref !== this._joinRef()) {\n            return;\n        }\n        let handledPayload = this._onMessage(typeLower, payload, ref);\n        if (payload && !handledPayload) {\n            throw 'channel onMessage callbacks must return the payload, modified or unmodified';\n        }\n        if (['insert', 'update', 'delete'].includes(typeLower)) {\n            (_a = this.bindings.postgres_changes) === null || _a === void 0 ? void 0 : _a.filter((bind) => {\n                var _a, _b, _c;\n                return (((_a = bind.filter) === null || _a === void 0 ? void 0 : _a.event) === '*' ||\n                    ((_c = (_b = bind.filter) === null || _b === void 0 ? void 0 : _b.event) === null || _c === void 0 ? void 0 : _c.toLocaleLowerCase()) === typeLower);\n            }).map((bind) => bind.callback(handledPayload, ref));\n        }\n        else {\n            (_b = this.bindings[typeLower]) === null || _b === void 0 ? void 0 : _b.filter((bind) => {\n                var _a, _b, _c, _d, _e, _f;\n                if (['broadcast', 'presence', 'postgres_changes'].includes(typeLower)) {\n                    if ('id' in bind) {\n                        const bindId = bind.id;\n                        const bindEvent = (_a = bind.filter) === null || _a === void 0 ? void 0 : _a.event;\n                        return (bindId &&\n                            ((_b = payload.ids) === null || _b === void 0 ? void 0 : _b.includes(bindId)) &&\n                            (bindEvent === '*' ||\n                                (bindEvent === null || bindEvent === void 0 ? void 0 : bindEvent.toLocaleLowerCase()) ===\n                                    ((_c = payload.data) === null || _c === void 0 ? void 0 : _c.type.toLocaleLowerCase())));\n                    }\n                    else {\n                        const bindEvent = (_e = (_d = bind === null || bind === void 0 ? void 0 : bind.filter) === null || _d === void 0 ? void 0 : _d.event) === null || _e === void 0 ? void 0 : _e.toLocaleLowerCase();\n                        return (bindEvent === '*' ||\n                            bindEvent === ((_f = payload === null || payload === void 0 ? void 0 : payload.event) === null || _f === void 0 ? void 0 : _f.toLocaleLowerCase()));\n                    }\n                }\n                else {\n                    return bind.type.toLocaleLowerCase() === typeLower;\n                }\n            }).map((bind) => {\n                if (typeof handledPayload === 'object' && 'ids' in handledPayload) {\n                    const postgresChanges = handledPayload.data;\n                    const { schema, table, commit_timestamp, type, errors } = postgresChanges;\n                    const enrichedPayload = {\n                        schema: schema,\n                        table: table,\n                        commit_timestamp: commit_timestamp,\n                        eventType: type,\n                        new: {},\n                        old: {},\n                        errors: errors,\n                    };\n                    handledPayload = Object.assign(Object.assign({}, enrichedPayload), this._getPayloadRecords(postgresChanges));\n                }\n                bind.callback(handledPayload, ref);\n            });\n        }\n    }\n    /** @internal */\n    _isClosed() {\n        return this.state === constants_1.CHANNEL_STATES.closed;\n    }\n    /** @internal */\n    _isJoined() {\n        return this.state === constants_1.CHANNEL_STATES.joined;\n    }\n    /** @internal */\n    _isJoining() {\n        return this.state === constants_1.CHANNEL_STATES.joining;\n    }\n    /** @internal */\n    _isLeaving() {\n        return this.state === constants_1.CHANNEL_STATES.leaving;\n    }\n    /** @internal */\n    _replyEventName(ref) {\n        return `chan_reply_${ref}`;\n    }\n    /** @internal */\n    _on(type, filter, callback) {\n        const typeLower = type.toLocaleLowerCase();\n        const binding = {\n            type: typeLower,\n            filter: filter,\n            callback: callback,\n        };\n        if (this.bindings[typeLower]) {\n            this.bindings[typeLower].push(binding);\n        }\n        else {\n            this.bindings[typeLower] = [binding];\n        }\n        return this;\n    }\n    /** @internal */\n    _off(type, filter) {\n        const typeLower = type.toLocaleLowerCase();\n        this.bindings[typeLower] = this.bindings[typeLower].filter((bind) => {\n            var _a;\n            return !(((_a = bind.type) === null || _a === void 0 ? void 0 : _a.toLocaleLowerCase()) === typeLower &&\n                RealtimeChannel.isEqual(bind.filter, filter));\n        });\n        return this;\n    }\n    /** @internal */\n    static isEqual(obj1, obj2) {\n        if (Object.keys(obj1).length !== Object.keys(obj2).length) {\n            return false;\n        }\n        for (const k in obj1) {\n            if (obj1[k] !== obj2[k]) {\n                return false;\n            }\n        }\n        return true;\n    }\n    /** @internal */\n    _rejoinUntilConnected() {\n        this.rejoinTimer.scheduleTimeout();\n        if (this.socket.isConnected()) {\n            this._rejoin();\n        }\n    }\n    /**\n     * Registers a callback that will be executed when the channel closes.\n     *\n     * @internal\n     */\n    _onClose(callback) {\n        this._on(constants_1.CHANNEL_EVENTS.close, {}, callback);\n    }\n    /**\n     * Registers a callback that will be executed when the channel encounteres an error.\n     *\n     * @internal\n     */\n    _onError(callback) {\n        this._on(constants_1.CHANNEL_EVENTS.error, {}, (reason) => callback(reason));\n    }\n    /**\n     * Returns `true` if the socket is connected and the channel has been joined.\n     *\n     * @internal\n     */\n    _canPush() {\n        return this.socket.isConnected() && this._isJoined();\n    }\n    /** @internal */\n    _rejoin(timeout = this.timeout) {\n        if (this._isLeaving()) {\n            return;\n        }\n        this.socket._leaveOpenTopic(this.topic);\n        this.state = constants_1.CHANNEL_STATES.joining;\n        this.joinPush.resend(timeout);\n    }\n    /** @internal */\n    _getPayloadRecords(payload) {\n        const records = {\n            new: {},\n            old: {},\n        };\n        if (payload.type === 'INSERT' || payload.type === 'UPDATE') {\n            records.new = Transformers.convertChangeData(payload.columns, payload.record);\n        }\n        if (payload.type === 'UPDATE' || payload.type === 'DELETE') {\n            records.old = Transformers.convertChangeData(payload.columns, payload.old_record);\n        }\n        return records;\n    }\n}\nexports[\"default\"] = RealtimeChannel;\n//# sourceMappingURL=RealtimeChannel.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/RealtimeChannel.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/RealtimeClient.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/RealtimeClient.js ***!
  \*************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || (function () {\n    var ownKeys = function(o) {\n        ownKeys = Object.getOwnPropertyNames || function (o) {\n            var ar = [];\n            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n            return ar;\n        };\n        return ownKeys(o);\n    };\n    return function (mod) {\n        if (mod && mod.__esModule) return mod;\n        var result = {};\n        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n        __setModuleDefault(result, mod);\n        return result;\n    };\n})();\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst WebSocket_1 = __importDefault(__webpack_require__(/*! ./WebSocket */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/WebSocket.js\"));\nconst constants_1 = __webpack_require__(/*! ./lib/constants */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/lib/constants.js\");\nconst serializer_1 = __importDefault(__webpack_require__(/*! ./lib/serializer */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/lib/serializer.js\"));\nconst timer_1 = __importDefault(__webpack_require__(/*! ./lib/timer */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/lib/timer.js\"));\nconst transformers_1 = __webpack_require__(/*! ./lib/transformers */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/lib/transformers.js\");\nconst RealtimeChannel_1 = __importDefault(__webpack_require__(/*! ./RealtimeChannel */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/RealtimeChannel.js\"));\nconst noop = () => { };\nconst WORKER_SCRIPT = `\n  addEventListener(\"message\", (e) => {\n    if (e.data.event === \"start\") {\n      setInterval(() => postMessage({ event: \"keepAlive\" }), e.data.interval);\n    }\n  });`;\nclass RealtimeClient {\n    /**\n     * Initializes the Socket.\n     *\n     * @param endPoint The string WebSocket endpoint, ie, \"ws://example.com/socket\", \"wss://example.com\", \"/socket\" (inherited host & protocol)\n     * @param httpEndpoint The string HTTP endpoint, ie, \"https://example.com\", \"/\" (inherited host & protocol)\n     * @param options.transport The Websocket Transport, for example WebSocket. This can be a custom implementation\n     * @param options.timeout The default timeout in milliseconds to trigger push timeouts.\n     * @param options.params The optional params to pass when connecting.\n     * @param options.headers The optional headers to pass when connecting.\n     * @param options.heartbeatIntervalMs The millisec interval to send a heartbeat message.\n     * @param options.logger The optional function for specialized logging, ie: logger: (kind, msg, data) => { console.log(`${kind}: ${msg}`, data) }\n     * @param options.logLevel Sets the log level for Realtime\n     * @param options.encode The function to encode outgoing messages. Defaults to JSON: (payload, callback) => callback(JSON.stringify(payload))\n     * @param options.decode The function to decode incoming messages. Defaults to Serializer's decode.\n     * @param options.reconnectAfterMs he optional function that returns the millsec reconnect interval. Defaults to stepped backoff off.\n     * @param options.worker Use Web Worker to set a side flow. Defaults to false.\n     * @param options.workerUrl The URL of the worker script. Defaults to https://realtime.supabase.com/worker.js that includes a heartbeat event call to keep the connection alive.\n     */\n    constructor(endPoint, options) {\n        var _a;\n        this.accessTokenValue = null;\n        this.apiKey = null;\n        this.channels = new Array();\n        this.endPoint = '';\n        this.httpEndpoint = '';\n        this.headers = constants_1.DEFAULT_HEADERS;\n        this.params = {};\n        this.timeout = constants_1.DEFAULT_TIMEOUT;\n        this.heartbeatIntervalMs = 25000;\n        this.heartbeatTimer = undefined;\n        this.pendingHeartbeatRef = null;\n        this.heartbeatCallback = noop;\n        this.ref = 0;\n        this.logger = noop;\n        this.conn = null;\n        this.sendBuffer = [];\n        this.serializer = new serializer_1.default();\n        this.stateChangeCallbacks = {\n            open: [],\n            close: [],\n            error: [],\n            message: [],\n        };\n        this.accessToken = null;\n        /**\n         * Use either custom fetch, if provided, or default fetch to make HTTP requests\n         *\n         * @internal\n         */\n        this._resolveFetch = (customFetch) => {\n            let _fetch;\n            if (customFetch) {\n                _fetch = customFetch;\n            }\n            else if (typeof fetch === 'undefined') {\n                _fetch = (...args) => Promise.resolve(`${'@supabase/node-fetch'}`).then(s => __importStar(__webpack_require__(\"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main sync recursive\")(s))).then(({ default: fetch }) => fetch(...args));\n            }\n            else {\n                _fetch = fetch;\n            }\n            return (...args) => _fetch(...args);\n        };\n        this.endPoint = `${endPoint}/${constants_1.TRANSPORTS.websocket}`;\n        this.httpEndpoint = (0, transformers_1.httpEndpointURL)(endPoint);\n        if (options === null || options === void 0 ? void 0 : options.transport) {\n            this.transport = options.transport;\n        }\n        else {\n            this.transport = null;\n        }\n        if (options === null || options === void 0 ? void 0 : options.params)\n            this.params = options.params;\n        if (options === null || options === void 0 ? void 0 : options.headers)\n            this.headers = Object.assign(Object.assign({}, this.headers), options.headers);\n        if (options === null || options === void 0 ? void 0 : options.timeout)\n            this.timeout = options.timeout;\n        if (options === null || options === void 0 ? void 0 : options.logger)\n            this.logger = options.logger;\n        if ((options === null || options === void 0 ? void 0 : options.logLevel) || (options === null || options === void 0 ? void 0 : options.log_level)) {\n            this.logLevel = options.logLevel || options.log_level;\n            this.params = Object.assign(Object.assign({}, this.params), { log_level: this.logLevel });\n        }\n        if (options === null || options === void 0 ? void 0 : options.heartbeatIntervalMs)\n            this.heartbeatIntervalMs = options.heartbeatIntervalMs;\n        const accessTokenValue = (_a = options === null || options === void 0 ? void 0 : options.params) === null || _a === void 0 ? void 0 : _a.apikey;\n        if (accessTokenValue) {\n            this.accessTokenValue = accessTokenValue;\n            this.apiKey = accessTokenValue;\n        }\n        this.reconnectAfterMs = (options === null || options === void 0 ? void 0 : options.reconnectAfterMs)\n            ? options.reconnectAfterMs\n            : (tries) => {\n                return [1000, 2000, 5000, 10000][tries - 1] || 10000;\n            };\n        this.encode = (options === null || options === void 0 ? void 0 : options.encode)\n            ? options.encode\n            : (payload, callback) => {\n                return callback(JSON.stringify(payload));\n            };\n        this.decode = (options === null || options === void 0 ? void 0 : options.decode)\n            ? options.decode\n            : this.serializer.decode.bind(this.serializer);\n        this.reconnectTimer = new timer_1.default(async () => {\n            this.disconnect();\n            this.connect();\n        }, this.reconnectAfterMs);\n        this.fetch = this._resolveFetch(options === null || options === void 0 ? void 0 : options.fetch);\n        if (options === null || options === void 0 ? void 0 : options.worker) {\n            if (typeof window !== 'undefined' && !window.Worker) {\n                throw new Error('Web Worker is not supported');\n            }\n            this.worker = (options === null || options === void 0 ? void 0 : options.worker) || false;\n            this.workerUrl = options === null || options === void 0 ? void 0 : options.workerUrl;\n        }\n        this.accessToken = (options === null || options === void 0 ? void 0 : options.accessToken) || null;\n    }\n    /**\n     * Connects the socket, unless already connected.\n     */\n    connect() {\n        if (this.conn) {\n            return;\n        }\n        if (!this.transport) {\n            this.transport = WebSocket_1.default;\n        }\n        if (this.transport) {\n            // Detect if using the native browser WebSocket\n            const isBrowser = typeof window !== 'undefined' && this.transport === window.WebSocket;\n            if (isBrowser) {\n                this.conn = new this.transport(this.endpointURL());\n            }\n            else {\n                this.conn = new this.transport(this.endpointURL(), undefined, {\n                    headers: this.headers,\n                });\n            }\n            this.setupConnection();\n            return;\n        }\n        this.conn = new WSWebSocketDummy(this.endpointURL(), undefined, {\n            close: () => {\n                this.conn = null;\n            },\n        });\n    }\n    /**\n     * Returns the URL of the websocket.\n     * @returns string The URL of the websocket.\n     */\n    endpointURL() {\n        return this._appendParams(this.endPoint, Object.assign({}, this.params, { vsn: constants_1.VSN }));\n    }\n    /**\n     * Disconnects the socket.\n     *\n     * @param code A numeric status code to send on disconnect.\n     * @param reason A custom reason for the disconnect.\n     */\n    disconnect(code, reason) {\n        if (this.conn) {\n            this.conn.onclose = function () { }; // noop\n            if (code) {\n                this.conn.close(code, reason !== null && reason !== void 0 ? reason : '');\n            }\n            else {\n                this.conn.close();\n            }\n            this.conn = null;\n            // remove open handles\n            this.heartbeatTimer && clearInterval(this.heartbeatTimer);\n            this.reconnectTimer.reset();\n            this.channels.forEach((channel) => channel.teardown());\n        }\n    }\n    /**\n     * Returns all created channels\n     */\n    getChannels() {\n        return this.channels;\n    }\n    /**\n     * Unsubscribes and removes a single channel\n     * @param channel A RealtimeChannel instance\n     */\n    async removeChannel(channel) {\n        const status = await channel.unsubscribe();\n        this.channels = this.channels.filter((c) => c._joinRef !== channel._joinRef);\n        if (this.channels.length === 0) {\n            this.disconnect();\n        }\n        return status;\n    }\n    /**\n     * Unsubscribes and removes all channels\n     */\n    async removeAllChannels() {\n        const values_1 = await Promise.all(this.channels.map((channel) => channel.unsubscribe()));\n        this.channels = [];\n        this.disconnect();\n        return values_1;\n    }\n    /**\n     * Logs the message.\n     *\n     * For customized logging, `this.logger` can be overridden.\n     */\n    log(kind, msg, data) {\n        this.logger(kind, msg, data);\n    }\n    /**\n     * Returns the current state of the socket.\n     */\n    connectionState() {\n        switch (this.conn && this.conn.readyState) {\n            case constants_1.SOCKET_STATES.connecting:\n                return constants_1.CONNECTION_STATE.Connecting;\n            case constants_1.SOCKET_STATES.open:\n                return constants_1.CONNECTION_STATE.Open;\n            case constants_1.SOCKET_STATES.closing:\n                return constants_1.CONNECTION_STATE.Closing;\n            default:\n                return constants_1.CONNECTION_STATE.Closed;\n        }\n    }\n    /**\n     * Returns `true` is the connection is open.\n     */\n    isConnected() {\n        return this.connectionState() === constants_1.CONNECTION_STATE.Open;\n    }\n    channel(topic, params = { config: {} }) {\n        const realtimeTopic = `realtime:${topic}`;\n        const exists = this.getChannels().find((c) => c.topic === realtimeTopic);\n        if (!exists) {\n            const chan = new RealtimeChannel_1.default(`realtime:${topic}`, params, this);\n            this.channels.push(chan);\n            return chan;\n        }\n        else {\n            return exists;\n        }\n    }\n    /**\n     * Push out a message if the socket is connected.\n     *\n     * If the socket is not connected, the message gets enqueued within a local buffer, and sent out when a connection is next established.\n     */\n    push(data) {\n        const { topic, event, payload, ref } = data;\n        const callback = () => {\n            this.encode(data, (result) => {\n                var _a;\n                (_a = this.conn) === null || _a === void 0 ? void 0 : _a.send(result);\n            });\n        };\n        this.log('push', `${topic} ${event} (${ref})`, payload);\n        if (this.isConnected()) {\n            callback();\n        }\n        else {\n            this.sendBuffer.push(callback);\n        }\n    }\n    /**\n     * Sets the JWT access token used for channel subscription authorization and Realtime RLS.\n     *\n     * If param is null it will use the `accessToken` callback function or the token set on the client.\n     *\n     * On callback used, it will set the value of the token internal to the client.\n     *\n     * @param token A JWT string to override the token set on the client.\n     */\n    async setAuth(token = null) {\n        let tokenToSend = token ||\n            (this.accessToken && (await this.accessToken())) ||\n            this.accessTokenValue;\n        if (this.accessTokenValue != tokenToSend) {\n            this.accessTokenValue = tokenToSend;\n            this.channels.forEach((channel) => {\n                tokenToSend &&\n                    channel.updateJoinPayload({\n                        access_token: tokenToSend,\n                        version: this.headers && this.headers['X-Client-Info'],\n                    });\n                if (channel.joinedOnce && channel._isJoined()) {\n                    channel._push(constants_1.CHANNEL_EVENTS.access_token, {\n                        access_token: tokenToSend,\n                    });\n                }\n            });\n        }\n    }\n    /**\n     * Sends a heartbeat message if the socket is connected.\n     */\n    async sendHeartbeat() {\n        var _a;\n        if (!this.isConnected()) {\n            this.heartbeatCallback('disconnected');\n            return;\n        }\n        if (this.pendingHeartbeatRef) {\n            this.pendingHeartbeatRef = null;\n            this.log('transport', 'heartbeat timeout. Attempting to re-establish connection');\n            this.heartbeatCallback('timeout');\n            (_a = this.conn) === null || _a === void 0 ? void 0 : _a.close(constants_1.WS_CLOSE_NORMAL, 'hearbeat timeout');\n            return;\n        }\n        this.pendingHeartbeatRef = this._makeRef();\n        this.push({\n            topic: 'phoenix',\n            event: 'heartbeat',\n            payload: {},\n            ref: this.pendingHeartbeatRef,\n        });\n        this.heartbeatCallback('sent');\n        await this.setAuth();\n    }\n    onHeartbeat(callback) {\n        this.heartbeatCallback = callback;\n    }\n    /**\n     * Flushes send buffer\n     */\n    flushSendBuffer() {\n        if (this.isConnected() && this.sendBuffer.length > 0) {\n            this.sendBuffer.forEach((callback) => callback());\n            this.sendBuffer = [];\n        }\n    }\n    /**\n     * Return the next message ref, accounting for overflows\n     *\n     * @internal\n     */\n    _makeRef() {\n        let newRef = this.ref + 1;\n        if (newRef === this.ref) {\n            this.ref = 0;\n        }\n        else {\n            this.ref = newRef;\n        }\n        return this.ref.toString();\n    }\n    /**\n     * Unsubscribe from channels with the specified topic.\n     *\n     * @internal\n     */\n    _leaveOpenTopic(topic) {\n        let dupChannel = this.channels.find((c) => c.topic === topic && (c._isJoined() || c._isJoining()));\n        if (dupChannel) {\n            this.log('transport', `leaving duplicate topic \"${topic}\"`);\n            dupChannel.unsubscribe();\n        }\n    }\n    /**\n     * Removes a subscription from the socket.\n     *\n     * @param channel An open subscription.\n     *\n     * @internal\n     */\n    _remove(channel) {\n        this.channels = this.channels.filter((c) => c.topic !== channel.topic);\n    }\n    /**\n     * Sets up connection handlers.\n     *\n     * @internal\n     */\n    setupConnection() {\n        if (this.conn) {\n            this.conn.binaryType = 'arraybuffer';\n            this.conn.onopen = () => this._onConnOpen();\n            this.conn.onerror = (error) => this._onConnError(error);\n            this.conn.onmessage = (event) => this._onConnMessage(event);\n            this.conn.onclose = (event) => this._onConnClose(event);\n        }\n    }\n    /** @internal */\n    _onConnMessage(rawMessage) {\n        this.decode(rawMessage.data, (msg) => {\n            let { topic, event, payload, ref } = msg;\n            if (topic === 'phoenix' && event === 'phx_reply') {\n                this.heartbeatCallback(msg.payload.status == 'ok' ? 'ok' : 'error');\n            }\n            if (ref && ref === this.pendingHeartbeatRef) {\n                this.pendingHeartbeatRef = null;\n            }\n            this.log('receive', `${payload.status || ''} ${topic} ${event} ${(ref && '(' + ref + ')') || ''}`, payload);\n            Array.from(this.channels)\n                .filter((channel) => channel._isMember(topic))\n                .forEach((channel) => channel._trigger(event, payload, ref));\n            this.stateChangeCallbacks.message.forEach((callback) => callback(msg));\n        });\n    }\n    /** @internal */\n    _onConnOpen() {\n        this.log('transport', `connected to ${this.endpointURL()}`);\n        this.flushSendBuffer();\n        this.reconnectTimer.reset();\n        if (!this.worker) {\n            this.heartbeatTimer && clearInterval(this.heartbeatTimer);\n            this.heartbeatTimer = setInterval(() => this.sendHeartbeat(), this.heartbeatIntervalMs);\n        }\n        else {\n            if (this.workerUrl) {\n                this.log('worker', `starting worker for from ${this.workerUrl}`);\n            }\n            else {\n                this.log('worker', `starting default worker`);\n            }\n            const objectUrl = this._workerObjectUrl(this.workerUrl);\n            this.workerRef = new Worker(objectUrl);\n            this.workerRef.onerror = (error) => {\n                this.log('worker', 'worker error', error.message);\n                this.workerRef.terminate();\n            };\n            this.workerRef.onmessage = (event) => {\n                if (event.data.event === 'keepAlive') {\n                    this.sendHeartbeat();\n                }\n            };\n            this.workerRef.postMessage({\n                event: 'start',\n                interval: this.heartbeatIntervalMs,\n            });\n        }\n        this.stateChangeCallbacks.open.forEach((callback) => callback());\n    }\n    /** @internal */\n    _onConnClose(event) {\n        this.log('transport', 'close', event);\n        this._triggerChanError();\n        this.heartbeatTimer && clearInterval(this.heartbeatTimer);\n        this.reconnectTimer.scheduleTimeout();\n        this.stateChangeCallbacks.close.forEach((callback) => callback(event));\n    }\n    /** @internal */\n    _onConnError(error) {\n        this.log('transport', error.message);\n        this._triggerChanError();\n        this.stateChangeCallbacks.error.forEach((callback) => callback(error));\n    }\n    /** @internal */\n    _triggerChanError() {\n        this.channels.forEach((channel) => channel._trigger(constants_1.CHANNEL_EVENTS.error));\n    }\n    /** @internal */\n    _appendParams(url, params) {\n        if (Object.keys(params).length === 0) {\n            return url;\n        }\n        const prefix = url.match(/\\?/) ? '&' : '?';\n        const query = new URLSearchParams(params);\n        return `${url}${prefix}${query}`;\n    }\n    _workerObjectUrl(url) {\n        let result_url;\n        if (url) {\n            result_url = url;\n        }\n        else {\n            const blob = new Blob([WORKER_SCRIPT], { type: 'application/javascript' });\n            result_url = URL.createObjectURL(blob);\n        }\n        return result_url;\n    }\n}\nexports[\"default\"] = RealtimeClient;\nclass WSWebSocketDummy {\n    constructor(address, _protocols, options) {\n        this.binaryType = 'arraybuffer';\n        this.onclose = () => { };\n        this.onerror = () => { };\n        this.onmessage = () => { };\n        this.onopen = () => { };\n        this.readyState = constants_1.SOCKET_STATES.connecting;\n        this.send = () => { };\n        this.url = null;\n        this.url = address;\n        this.close = options.close;\n    }\n}\n//# sourceMappingURL=RealtimeClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/RealtimeClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/RealtimePresence.js":
/*!***************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/RealtimePresence.js ***!
  \***************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n  This file draws heavily from https://github.com/phoenixframework/phoenix/blob/d344ec0a732ab4ee204215b31de69cf4be72e3bf/assets/js/phoenix/presence.js\n  License: https://github.com/phoenixframework/phoenix/blob/d344ec0a732ab4ee204215b31de69cf4be72e3bf/LICENSE.md\n*/\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.REALTIME_PRESENCE_LISTEN_EVENTS = void 0;\nvar REALTIME_PRESENCE_LISTEN_EVENTS;\n(function (REALTIME_PRESENCE_LISTEN_EVENTS) {\n    REALTIME_PRESENCE_LISTEN_EVENTS[\"SYNC\"] = \"sync\";\n    REALTIME_PRESENCE_LISTEN_EVENTS[\"JOIN\"] = \"join\";\n    REALTIME_PRESENCE_LISTEN_EVENTS[\"LEAVE\"] = \"leave\";\n})(REALTIME_PRESENCE_LISTEN_EVENTS || (exports.REALTIME_PRESENCE_LISTEN_EVENTS = REALTIME_PRESENCE_LISTEN_EVENTS = {}));\nclass RealtimePresence {\n    /**\n     * Initializes the Presence.\n     *\n     * @param channel - The RealtimeChannel\n     * @param opts - The options,\n     *        for example `{events: {state: 'state', diff: 'diff'}}`\n     */\n    constructor(channel, opts) {\n        this.channel = channel;\n        this.state = {};\n        this.pendingDiffs = [];\n        this.joinRef = null;\n        this.caller = {\n            onJoin: () => { },\n            onLeave: () => { },\n            onSync: () => { },\n        };\n        const events = (opts === null || opts === void 0 ? void 0 : opts.events) || {\n            state: 'presence_state',\n            diff: 'presence_diff',\n        };\n        this.channel._on(events.state, {}, (newState) => {\n            const { onJoin, onLeave, onSync } = this.caller;\n            this.joinRef = this.channel._joinRef();\n            this.state = RealtimePresence.syncState(this.state, newState, onJoin, onLeave);\n            this.pendingDiffs.forEach((diff) => {\n                this.state = RealtimePresence.syncDiff(this.state, diff, onJoin, onLeave);\n            });\n            this.pendingDiffs = [];\n            onSync();\n        });\n        this.channel._on(events.diff, {}, (diff) => {\n            const { onJoin, onLeave, onSync } = this.caller;\n            if (this.inPendingSyncState()) {\n                this.pendingDiffs.push(diff);\n            }\n            else {\n                this.state = RealtimePresence.syncDiff(this.state, diff, onJoin, onLeave);\n                onSync();\n            }\n        });\n        this.onJoin((key, currentPresences, newPresences) => {\n            this.channel._trigger('presence', {\n                event: 'join',\n                key,\n                currentPresences,\n                newPresences,\n            });\n        });\n        this.onLeave((key, currentPresences, leftPresences) => {\n            this.channel._trigger('presence', {\n                event: 'leave',\n                key,\n                currentPresences,\n                leftPresences,\n            });\n        });\n        this.onSync(() => {\n            this.channel._trigger('presence', { event: 'sync' });\n        });\n    }\n    /**\n     * Used to sync the list of presences on the server with the\n     * client's state.\n     *\n     * An optional `onJoin` and `onLeave` callback can be provided to\n     * react to changes in the client's local presences across\n     * disconnects and reconnects with the server.\n     *\n     * @internal\n     */\n    static syncState(currentState, newState, onJoin, onLeave) {\n        const state = this.cloneDeep(currentState);\n        const transformedState = this.transformState(newState);\n        const joins = {};\n        const leaves = {};\n        this.map(state, (key, presences) => {\n            if (!transformedState[key]) {\n                leaves[key] = presences;\n            }\n        });\n        this.map(transformedState, (key, newPresences) => {\n            const currentPresences = state[key];\n            if (currentPresences) {\n                const newPresenceRefs = newPresences.map((m) => m.presence_ref);\n                const curPresenceRefs = currentPresences.map((m) => m.presence_ref);\n                const joinedPresences = newPresences.filter((m) => curPresenceRefs.indexOf(m.presence_ref) < 0);\n                const leftPresences = currentPresences.filter((m) => newPresenceRefs.indexOf(m.presence_ref) < 0);\n                if (joinedPresences.length > 0) {\n                    joins[key] = joinedPresences;\n                }\n                if (leftPresences.length > 0) {\n                    leaves[key] = leftPresences;\n                }\n            }\n            else {\n                joins[key] = newPresences;\n            }\n        });\n        return this.syncDiff(state, { joins, leaves }, onJoin, onLeave);\n    }\n    /**\n     * Used to sync a diff of presence join and leave events from the\n     * server, as they happen.\n     *\n     * Like `syncState`, `syncDiff` accepts optional `onJoin` and\n     * `onLeave` callbacks to react to a user joining or leaving from a\n     * device.\n     *\n     * @internal\n     */\n    static syncDiff(state, diff, onJoin, onLeave) {\n        const { joins, leaves } = {\n            joins: this.transformState(diff.joins),\n            leaves: this.transformState(diff.leaves),\n        };\n        if (!onJoin) {\n            onJoin = () => { };\n        }\n        if (!onLeave) {\n            onLeave = () => { };\n        }\n        this.map(joins, (key, newPresences) => {\n            var _a;\n            const currentPresences = (_a = state[key]) !== null && _a !== void 0 ? _a : [];\n            state[key] = this.cloneDeep(newPresences);\n            if (currentPresences.length > 0) {\n                const joinedPresenceRefs = state[key].map((m) => m.presence_ref);\n                const curPresences = currentPresences.filter((m) => joinedPresenceRefs.indexOf(m.presence_ref) < 0);\n                state[key].unshift(...curPresences);\n            }\n            onJoin(key, currentPresences, newPresences);\n        });\n        this.map(leaves, (key, leftPresences) => {\n            let currentPresences = state[key];\n            if (!currentPresences)\n                return;\n            const presenceRefsToRemove = leftPresences.map((m) => m.presence_ref);\n            currentPresences = currentPresences.filter((m) => presenceRefsToRemove.indexOf(m.presence_ref) < 0);\n            state[key] = currentPresences;\n            onLeave(key, currentPresences, leftPresences);\n            if (currentPresences.length === 0)\n                delete state[key];\n        });\n        return state;\n    }\n    /** @internal */\n    static map(obj, func) {\n        return Object.getOwnPropertyNames(obj).map((key) => func(key, obj[key]));\n    }\n    /**\n     * Remove 'metas' key\n     * Change 'phx_ref' to 'presence_ref'\n     * Remove 'phx_ref' and 'phx_ref_prev'\n     *\n     * @example\n     * // returns {\n     *  abc123: [\n     *    { presence_ref: '2', user_id: 1 },\n     *    { presence_ref: '3', user_id: 2 }\n     *  ]\n     * }\n     * RealtimePresence.transformState({\n     *  abc123: {\n     *    metas: [\n     *      { phx_ref: '2', phx_ref_prev: '1' user_id: 1 },\n     *      { phx_ref: '3', user_id: 2 }\n     *    ]\n     *  }\n     * })\n     *\n     * @internal\n     */\n    static transformState(state) {\n        state = this.cloneDeep(state);\n        return Object.getOwnPropertyNames(state).reduce((newState, key) => {\n            const presences = state[key];\n            if ('metas' in presences) {\n                newState[key] = presences.metas.map((presence) => {\n                    presence['presence_ref'] = presence['phx_ref'];\n                    delete presence['phx_ref'];\n                    delete presence['phx_ref_prev'];\n                    return presence;\n                });\n            }\n            else {\n                newState[key] = presences;\n            }\n            return newState;\n        }, {});\n    }\n    /** @internal */\n    static cloneDeep(obj) {\n        return JSON.parse(JSON.stringify(obj));\n    }\n    /** @internal */\n    onJoin(callback) {\n        this.caller.onJoin = callback;\n    }\n    /** @internal */\n    onLeave(callback) {\n        this.caller.onLeave = callback;\n    }\n    /** @internal */\n    onSync(callback) {\n        this.caller.onSync = callback;\n    }\n    /** @internal */\n    inPendingSyncState() {\n        return !this.joinRef || this.joinRef !== this.channel._joinRef();\n    }\n}\nexports[\"default\"] = RealtimePresence;\n//# sourceMappingURL=RealtimePresence.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/RealtimePresence.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/WebSocket.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/WebSocket.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Node.js WebSocket entry point\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nlet WebSocketImpl;\nif (typeof window === 'undefined') {\n    // Node.js environment\n    // eslint-disable-next-line @typescript-eslint/no-var-requires\n    WebSocketImpl = __webpack_require__(/*! ws */ \"(ssr)/./node_modules/.pnpm/ws@8.18.2/node_modules/ws/index.js\");\n}\nelse {\n    // Browser environment\n    WebSocketImpl = window.WebSocket;\n}\nexports[\"default\"] = WebSocketImpl;\n//# sourceMappingURL=WebSocket.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3JlYWx0aW1lLWpzQDIuMTEuMTAvbm9kZV9tb2R1bGVzL0BzdXBhYmFzZS9yZWFsdGltZS1qcy9kaXN0L21haW4vV2ViU29ja2V0LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsbUJBQU8sQ0FBQyx5RUFBSTtBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWU7QUFDZiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxtZW1ldFxcRGVza3RvcFxcZG9ybV8yMVxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHN1cGFiYXNlK3JlYWx0aW1lLWpzQDIuMTEuMTBcXG5vZGVfbW9kdWxlc1xcQHN1cGFiYXNlXFxyZWFsdGltZS1qc1xcZGlzdFxcbWFpblxcV2ViU29ja2V0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLy8gTm9kZS5qcyBXZWJTb2NrZXQgZW50cnkgcG9pbnRcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmxldCBXZWJTb2NrZXRJbXBsO1xuaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgLy8gTm9kZS5qcyBlbnZpcm9ubWVudFxuICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvbm8tdmFyLXJlcXVpcmVzXG4gICAgV2ViU29ja2V0SW1wbCA9IHJlcXVpcmUoJ3dzJyk7XG59XG5lbHNlIHtcbiAgICAvLyBCcm93c2VyIGVudmlyb25tZW50XG4gICAgV2ViU29ja2V0SW1wbCA9IHdpbmRvdy5XZWJTb2NrZXQ7XG59XG5leHBvcnRzLmRlZmF1bHQgPSBXZWJTb2NrZXRJbXBsO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9V2ViU29ja2V0LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/WebSocket.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/index.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/index.js ***!
  \****************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || (function () {\n    var ownKeys = function(o) {\n        ownKeys = Object.getOwnPropertyNames || function (o) {\n            var ar = [];\n            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n            return ar;\n        };\n        return ownKeys(o);\n    };\n    return function (mod) {\n        if (mod && mod.__esModule) return mod;\n        var result = {};\n        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n        __setModuleDefault(result, mod);\n        return result;\n    };\n})();\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.REALTIME_CHANNEL_STATES = exports.REALTIME_SUBSCRIBE_STATES = exports.REALTIME_PRESENCE_LISTEN_EVENTS = exports.REALTIME_POSTGRES_CHANGES_LISTEN_EVENT = exports.REALTIME_LISTEN_TYPES = exports.RealtimeClient = exports.RealtimeChannel = exports.RealtimePresence = void 0;\nconst RealtimeClient_1 = __importDefault(__webpack_require__(/*! ./RealtimeClient */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/RealtimeClient.js\"));\nexports.RealtimeClient = RealtimeClient_1.default;\nconst RealtimeChannel_1 = __importStar(__webpack_require__(/*! ./RealtimeChannel */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/RealtimeChannel.js\"));\nexports.RealtimeChannel = RealtimeChannel_1.default;\nObject.defineProperty(exports, \"REALTIME_LISTEN_TYPES\", ({ enumerable: true, get: function () { return RealtimeChannel_1.REALTIME_LISTEN_TYPES; } }));\nObject.defineProperty(exports, \"REALTIME_POSTGRES_CHANGES_LISTEN_EVENT\", ({ enumerable: true, get: function () { return RealtimeChannel_1.REALTIME_POSTGRES_CHANGES_LISTEN_EVENT; } }));\nObject.defineProperty(exports, \"REALTIME_SUBSCRIBE_STATES\", ({ enumerable: true, get: function () { return RealtimeChannel_1.REALTIME_SUBSCRIBE_STATES; } }));\nObject.defineProperty(exports, \"REALTIME_CHANNEL_STATES\", ({ enumerable: true, get: function () { return RealtimeChannel_1.REALTIME_CHANNEL_STATES; } }));\nconst RealtimePresence_1 = __importStar(__webpack_require__(/*! ./RealtimePresence */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/RealtimePresence.js\"));\nexports.RealtimePresence = RealtimePresence_1.default;\nObject.defineProperty(exports, \"REALTIME_PRESENCE_LISTEN_EVENTS\", ({ enumerable: true, get: function () { return RealtimePresence_1.REALTIME_PRESENCE_LISTEN_EVENTS; } }));\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/lib/constants.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/lib/constants.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.CONNECTION_STATE = exports.TRANSPORTS = exports.CHANNEL_EVENTS = exports.CHANNEL_STATES = exports.SOCKET_STATES = exports.WS_CLOSE_NORMAL = exports.DEFAULT_TIMEOUT = exports.VERSION = exports.VSN = exports.DEFAULT_HEADERS = void 0;\nconst version_1 = __webpack_require__(/*! ./version */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/lib/version.js\");\nexports.DEFAULT_HEADERS = { 'X-Client-Info': `realtime-js/${version_1.version}` };\nexports.VSN = '1.0.0';\nexports.VERSION = version_1.version;\nexports.DEFAULT_TIMEOUT = 10000;\nexports.WS_CLOSE_NORMAL = 1000;\nvar SOCKET_STATES;\n(function (SOCKET_STATES) {\n    SOCKET_STATES[SOCKET_STATES[\"connecting\"] = 0] = \"connecting\";\n    SOCKET_STATES[SOCKET_STATES[\"open\"] = 1] = \"open\";\n    SOCKET_STATES[SOCKET_STATES[\"closing\"] = 2] = \"closing\";\n    SOCKET_STATES[SOCKET_STATES[\"closed\"] = 3] = \"closed\";\n})(SOCKET_STATES || (exports.SOCKET_STATES = SOCKET_STATES = {}));\nvar CHANNEL_STATES;\n(function (CHANNEL_STATES) {\n    CHANNEL_STATES[\"closed\"] = \"closed\";\n    CHANNEL_STATES[\"errored\"] = \"errored\";\n    CHANNEL_STATES[\"joined\"] = \"joined\";\n    CHANNEL_STATES[\"joining\"] = \"joining\";\n    CHANNEL_STATES[\"leaving\"] = \"leaving\";\n})(CHANNEL_STATES || (exports.CHANNEL_STATES = CHANNEL_STATES = {}));\nvar CHANNEL_EVENTS;\n(function (CHANNEL_EVENTS) {\n    CHANNEL_EVENTS[\"close\"] = \"phx_close\";\n    CHANNEL_EVENTS[\"error\"] = \"phx_error\";\n    CHANNEL_EVENTS[\"join\"] = \"phx_join\";\n    CHANNEL_EVENTS[\"reply\"] = \"phx_reply\";\n    CHANNEL_EVENTS[\"leave\"] = \"phx_leave\";\n    CHANNEL_EVENTS[\"access_token\"] = \"access_token\";\n})(CHANNEL_EVENTS || (exports.CHANNEL_EVENTS = CHANNEL_EVENTS = {}));\nvar TRANSPORTS;\n(function (TRANSPORTS) {\n    TRANSPORTS[\"websocket\"] = \"websocket\";\n})(TRANSPORTS || (exports.TRANSPORTS = TRANSPORTS = {}));\nvar CONNECTION_STATE;\n(function (CONNECTION_STATE) {\n    CONNECTION_STATE[\"Connecting\"] = \"connecting\";\n    CONNECTION_STATE[\"Open\"] = \"open\";\n    CONNECTION_STATE[\"Closing\"] = \"closing\";\n    CONNECTION_STATE[\"Closed\"] = \"closed\";\n})(CONNECTION_STATE || (exports.CONNECTION_STATE = CONNECTION_STATE = {}));\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/lib/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/lib/push.js":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/lib/push.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst constants_1 = __webpack_require__(/*! ../lib/constants */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/lib/constants.js\");\nclass Push {\n    /**\n     * Initializes the Push\n     *\n     * @param channel The Channel\n     * @param event The event, for example `\"phx_join\"`\n     * @param payload The payload, for example `{user_id: 123}`\n     * @param timeout The push timeout in milliseconds\n     */\n    constructor(channel, event, payload = {}, timeout = constants_1.DEFAULT_TIMEOUT) {\n        this.channel = channel;\n        this.event = event;\n        this.payload = payload;\n        this.timeout = timeout;\n        this.sent = false;\n        this.timeoutTimer = undefined;\n        this.ref = '';\n        this.receivedResp = null;\n        this.recHooks = [];\n        this.refEvent = null;\n    }\n    resend(timeout) {\n        this.timeout = timeout;\n        this._cancelRefEvent();\n        this.ref = '';\n        this.refEvent = null;\n        this.receivedResp = null;\n        this.sent = false;\n        this.send();\n    }\n    send() {\n        if (this._hasReceived('timeout')) {\n            return;\n        }\n        this.startTimeout();\n        this.sent = true;\n        this.channel.socket.push({\n            topic: this.channel.topic,\n            event: this.event,\n            payload: this.payload,\n            ref: this.ref,\n            join_ref: this.channel._joinRef(),\n        });\n    }\n    updatePayload(payload) {\n        this.payload = Object.assign(Object.assign({}, this.payload), payload);\n    }\n    receive(status, callback) {\n        var _a;\n        if (this._hasReceived(status)) {\n            callback((_a = this.receivedResp) === null || _a === void 0 ? void 0 : _a.response);\n        }\n        this.recHooks.push({ status, callback });\n        return this;\n    }\n    startTimeout() {\n        if (this.timeoutTimer) {\n            return;\n        }\n        this.ref = this.channel.socket._makeRef();\n        this.refEvent = this.channel._replyEventName(this.ref);\n        const callback = (payload) => {\n            this._cancelRefEvent();\n            this._cancelTimeout();\n            this.receivedResp = payload;\n            this._matchReceive(payload);\n        };\n        this.channel._on(this.refEvent, {}, callback);\n        this.timeoutTimer = setTimeout(() => {\n            this.trigger('timeout', {});\n        }, this.timeout);\n    }\n    trigger(status, response) {\n        if (this.refEvent)\n            this.channel._trigger(this.refEvent, { status, response });\n    }\n    destroy() {\n        this._cancelRefEvent();\n        this._cancelTimeout();\n    }\n    _cancelRefEvent() {\n        if (!this.refEvent) {\n            return;\n        }\n        this.channel._off(this.refEvent, {});\n    }\n    _cancelTimeout() {\n        clearTimeout(this.timeoutTimer);\n        this.timeoutTimer = undefined;\n    }\n    _matchReceive({ status, response, }) {\n        this.recHooks\n            .filter((h) => h.status === status)\n            .forEach((h) => h.callback(response));\n    }\n    _hasReceived(status) {\n        return this.receivedResp && this.receivedResp.status === status;\n    }\n}\nexports[\"default\"] = Push;\n//# sourceMappingURL=push.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/lib/push.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/lib/serializer.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/lib/serializer.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// This file draws heavily from https://github.com/phoenixframework/phoenix/commit/cf098e9cf7a44ee6479d31d911a97d3c7430c6fe\n// License: https://github.com/phoenixframework/phoenix/blob/master/LICENSE.md\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nclass Serializer {\n    constructor() {\n        this.HEADER_LENGTH = 1;\n    }\n    decode(rawPayload, callback) {\n        if (rawPayload.constructor === ArrayBuffer) {\n            return callback(this._binaryDecode(rawPayload));\n        }\n        if (typeof rawPayload === 'string') {\n            return callback(JSON.parse(rawPayload));\n        }\n        return callback({});\n    }\n    _binaryDecode(buffer) {\n        const view = new DataView(buffer);\n        const decoder = new TextDecoder();\n        return this._decodeBroadcast(buffer, view, decoder);\n    }\n    _decodeBroadcast(buffer, view, decoder) {\n        const topicSize = view.getUint8(1);\n        const eventSize = view.getUint8(2);\n        let offset = this.HEADER_LENGTH + 2;\n        const topic = decoder.decode(buffer.slice(offset, offset + topicSize));\n        offset = offset + topicSize;\n        const event = decoder.decode(buffer.slice(offset, offset + eventSize));\n        offset = offset + eventSize;\n        const data = JSON.parse(decoder.decode(buffer.slice(offset, buffer.byteLength)));\n        return { ref: null, topic: topic, event: event, payload: data };\n    }\n}\nexports[\"default\"] = Serializer;\n//# sourceMappingURL=serializer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/lib/serializer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/lib/timer.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/lib/timer.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n/**\n * Creates a timer that accepts a `timerCalc` function to perform calculated timeout retries, such as exponential backoff.\n *\n * @example\n *    let reconnectTimer = new Timer(() => this.connect(), function(tries){\n *      return [1000, 5000, 10000][tries - 1] || 10000\n *    })\n *    reconnectTimer.scheduleTimeout() // fires after 1000\n *    reconnectTimer.scheduleTimeout() // fires after 5000\n *    reconnectTimer.reset()\n *    reconnectTimer.scheduleTimeout() // fires after 1000\n */\nclass Timer {\n    constructor(callback, timerCalc) {\n        this.callback = callback;\n        this.timerCalc = timerCalc;\n        this.timer = undefined;\n        this.tries = 0;\n        this.callback = callback;\n        this.timerCalc = timerCalc;\n    }\n    reset() {\n        this.tries = 0;\n        clearTimeout(this.timer);\n    }\n    // Cancels any previous scheduleTimeout and schedules callback\n    scheduleTimeout() {\n        clearTimeout(this.timer);\n        this.timer = setTimeout(() => {\n            this.tries = this.tries + 1;\n            this.callback();\n        }, this.timerCalc(this.tries + 1));\n    }\n}\nexports[\"default\"] = Timer;\n//# sourceMappingURL=timer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/lib/timer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/lib/transformers.js":
/*!***************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/lib/transformers.js ***!
  \***************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/**\n * Helpers to convert the change Payload into native JS types.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.httpEndpointURL = exports.toTimestampString = exports.toArray = exports.toJson = exports.toNumber = exports.toBoolean = exports.convertCell = exports.convertColumn = exports.convertChangeData = exports.PostgresTypes = void 0;\n// Adapted from epgsql (src/epgsql_binary.erl), this module licensed under\n// 3-clause BSD found here: https://raw.githubusercontent.com/epgsql/epgsql/devel/LICENSE\nvar PostgresTypes;\n(function (PostgresTypes) {\n    PostgresTypes[\"abstime\"] = \"abstime\";\n    PostgresTypes[\"bool\"] = \"bool\";\n    PostgresTypes[\"date\"] = \"date\";\n    PostgresTypes[\"daterange\"] = \"daterange\";\n    PostgresTypes[\"float4\"] = \"float4\";\n    PostgresTypes[\"float8\"] = \"float8\";\n    PostgresTypes[\"int2\"] = \"int2\";\n    PostgresTypes[\"int4\"] = \"int4\";\n    PostgresTypes[\"int4range\"] = \"int4range\";\n    PostgresTypes[\"int8\"] = \"int8\";\n    PostgresTypes[\"int8range\"] = \"int8range\";\n    PostgresTypes[\"json\"] = \"json\";\n    PostgresTypes[\"jsonb\"] = \"jsonb\";\n    PostgresTypes[\"money\"] = \"money\";\n    PostgresTypes[\"numeric\"] = \"numeric\";\n    PostgresTypes[\"oid\"] = \"oid\";\n    PostgresTypes[\"reltime\"] = \"reltime\";\n    PostgresTypes[\"text\"] = \"text\";\n    PostgresTypes[\"time\"] = \"time\";\n    PostgresTypes[\"timestamp\"] = \"timestamp\";\n    PostgresTypes[\"timestamptz\"] = \"timestamptz\";\n    PostgresTypes[\"timetz\"] = \"timetz\";\n    PostgresTypes[\"tsrange\"] = \"tsrange\";\n    PostgresTypes[\"tstzrange\"] = \"tstzrange\";\n})(PostgresTypes || (exports.PostgresTypes = PostgresTypes = {}));\n/**\n * Takes an array of columns and an object of string values then converts each string value\n * to its mapped type.\n *\n * @param {{name: String, type: String}[]} columns\n * @param {Object} record\n * @param {Object} options The map of various options that can be applied to the mapper\n * @param {Array} options.skipTypes The array of types that should not be converted\n *\n * @example convertChangeData([{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: 'Paul', age:'33'}, {})\n * //=>{ first_name: 'Paul', age: 33 }\n */\nconst convertChangeData = (columns, record, options = {}) => {\n    var _a;\n    const skipTypes = (_a = options.skipTypes) !== null && _a !== void 0 ? _a : [];\n    return Object.keys(record).reduce((acc, rec_key) => {\n        acc[rec_key] = (0, exports.convertColumn)(rec_key, columns, record, skipTypes);\n        return acc;\n    }, {});\n};\nexports.convertChangeData = convertChangeData;\n/**\n * Converts the value of an individual column.\n *\n * @param {String} columnName The column that you want to convert\n * @param {{name: String, type: String}[]} columns All of the columns\n * @param {Object} record The map of string values\n * @param {Array} skipTypes An array of types that should not be converted\n * @return {object} Useless information\n *\n * @example convertColumn('age', [{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: 'Paul', age: '33'}, [])\n * //=> 33\n * @example convertColumn('age', [{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: 'Paul', age: '33'}, ['int4'])\n * //=> \"33\"\n */\nconst convertColumn = (columnName, columns, record, skipTypes) => {\n    const column = columns.find((x) => x.name === columnName);\n    const colType = column === null || column === void 0 ? void 0 : column.type;\n    const value = record[columnName];\n    if (colType && !skipTypes.includes(colType)) {\n        return (0, exports.convertCell)(colType, value);\n    }\n    return noop(value);\n};\nexports.convertColumn = convertColumn;\n/**\n * If the value of the cell is `null`, returns null.\n * Otherwise converts the string value to the correct type.\n * @param {String} type A postgres column type\n * @param {String} value The cell value\n *\n * @example convertCell('bool', 't')\n * //=> true\n * @example convertCell('int8', '10')\n * //=> 10\n * @example convertCell('_int4', '{1,2,3,4}')\n * //=> [1,2,3,4]\n */\nconst convertCell = (type, value) => {\n    // if data type is an array\n    if (type.charAt(0) === '_') {\n        const dataType = type.slice(1, type.length);\n        return (0, exports.toArray)(value, dataType);\n    }\n    // If not null, convert to correct type.\n    switch (type) {\n        case PostgresTypes.bool:\n            return (0, exports.toBoolean)(value);\n        case PostgresTypes.float4:\n        case PostgresTypes.float8:\n        case PostgresTypes.int2:\n        case PostgresTypes.int4:\n        case PostgresTypes.int8:\n        case PostgresTypes.numeric:\n        case PostgresTypes.oid:\n            return (0, exports.toNumber)(value);\n        case PostgresTypes.json:\n        case PostgresTypes.jsonb:\n            return (0, exports.toJson)(value);\n        case PostgresTypes.timestamp:\n            return (0, exports.toTimestampString)(value); // Format to be consistent with PostgREST\n        case PostgresTypes.abstime: // To allow users to cast it based on Timezone\n        case PostgresTypes.date: // To allow users to cast it based on Timezone\n        case PostgresTypes.daterange:\n        case PostgresTypes.int4range:\n        case PostgresTypes.int8range:\n        case PostgresTypes.money:\n        case PostgresTypes.reltime: // To allow users to cast it based on Timezone\n        case PostgresTypes.text:\n        case PostgresTypes.time: // To allow users to cast it based on Timezone\n        case PostgresTypes.timestamptz: // To allow users to cast it based on Timezone\n        case PostgresTypes.timetz: // To allow users to cast it based on Timezone\n        case PostgresTypes.tsrange:\n        case PostgresTypes.tstzrange:\n            return noop(value);\n        default:\n            // Return the value for remaining types\n            return noop(value);\n    }\n};\nexports.convertCell = convertCell;\nconst noop = (value) => {\n    return value;\n};\nconst toBoolean = (value) => {\n    switch (value) {\n        case 't':\n            return true;\n        case 'f':\n            return false;\n        default:\n            return value;\n    }\n};\nexports.toBoolean = toBoolean;\nconst toNumber = (value) => {\n    if (typeof value === 'string') {\n        const parsedValue = parseFloat(value);\n        if (!Number.isNaN(parsedValue)) {\n            return parsedValue;\n        }\n    }\n    return value;\n};\nexports.toNumber = toNumber;\nconst toJson = (value) => {\n    if (typeof value === 'string') {\n        try {\n            return JSON.parse(value);\n        }\n        catch (error) {\n            console.log(`JSON parse error: ${error}`);\n            return value;\n        }\n    }\n    return value;\n};\nexports.toJson = toJson;\n/**\n * Converts a Postgres Array into a native JS array\n *\n * @example toArray('{}', 'int4')\n * //=> []\n * @example toArray('{\"[2021-01-01,2021-12-31)\",\"(2021-01-01,2021-12-32]\"}', 'daterange')\n * //=> ['[2021-01-01,2021-12-31)', '(2021-01-01,2021-12-32]']\n * @example toArray([1,2,3,4], 'int4')\n * //=> [1,2,3,4]\n */\nconst toArray = (value, type) => {\n    if (typeof value !== 'string') {\n        return value;\n    }\n    const lastIdx = value.length - 1;\n    const closeBrace = value[lastIdx];\n    const openBrace = value[0];\n    // Confirm value is a Postgres array by checking curly brackets\n    if (openBrace === '{' && closeBrace === '}') {\n        let arr;\n        const valTrim = value.slice(1, lastIdx);\n        // TODO: find a better solution to separate Postgres array data\n        try {\n            arr = JSON.parse('[' + valTrim + ']');\n        }\n        catch (_) {\n            // WARNING: splitting on comma does not cover all edge cases\n            arr = valTrim ? valTrim.split(',') : [];\n        }\n        return arr.map((val) => (0, exports.convertCell)(type, val));\n    }\n    return value;\n};\nexports.toArray = toArray;\n/**\n * Fixes timestamp to be ISO-8601. Swaps the space between the date and time for a 'T'\n * See https://github.com/supabase/supabase/issues/18\n *\n * @example toTimestampString('2019-09-10 00:00:00')\n * //=> '2019-09-10T00:00:00'\n */\nconst toTimestampString = (value) => {\n    if (typeof value === 'string') {\n        return value.replace(' ', 'T');\n    }\n    return value;\n};\nexports.toTimestampString = toTimestampString;\nconst httpEndpointURL = (socketUrl) => {\n    let url = socketUrl;\n    url = url.replace(/^ws/i, 'http');\n    url = url.replace(/(\\/socket\\/websocket|\\/socket|\\/websocket)\\/?$/i, '');\n    return url.replace(/\\/+$/, '');\n};\nexports.httpEndpointURL = httpEndpointURL;\n//# sourceMappingURL=transformers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/lib/transformers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/lib/version.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/lib/version.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.version = void 0;\nexports.version = '2.11.10';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3JlYWx0aW1lLWpzQDIuMTEuMTAvbm9kZV9tb2R1bGVzL0BzdXBhYmFzZS9yZWFsdGltZS1qcy9kaXN0L21haW4vbGliL3ZlcnNpb24uanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsZUFBZTtBQUNmLGVBQWU7QUFDZiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxtZW1ldFxcRGVza3RvcFxcZG9ybV8yMVxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHN1cGFiYXNlK3JlYWx0aW1lLWpzQDIuMTEuMTBcXG5vZGVfbW9kdWxlc1xcQHN1cGFiYXNlXFxyZWFsdGltZS1qc1xcZGlzdFxcbWFpblxcbGliXFx2ZXJzaW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy52ZXJzaW9uID0gdm9pZCAwO1xuZXhwb3J0cy52ZXJzaW9uID0gJzIuMTEuMTAnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dmVyc2lvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/lib/version.js\n");

/***/ })

};
;