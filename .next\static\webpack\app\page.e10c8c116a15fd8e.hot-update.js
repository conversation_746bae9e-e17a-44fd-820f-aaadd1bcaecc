"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/LaundryCard.tsx":
/*!****************************************!*\
  !*** ./src/components/LaundryCard.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LaundryCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/src/hooks/useSupabaseData */ \"(app-pages-browser)/./src/hooks/useSupabaseData.tsx\");\n/* harmony import */ var _src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/src/hooks/useCountdown */ \"(app-pages-browser)/./src/hooks/useCountdown.tsx\");\n/* harmony import */ var _IncidentBadge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./IncidentBadge */ \"(app-pages-browser)/./src/components/IncidentBadge.tsx\");\n/* harmony import */ var _ui_CardWrapper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/CardWrapper */ \"(app-pages-browser)/./src/components/ui/CardWrapper.tsx\");\n/* harmony import */ var _ui_WasherSVG__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/WasherSVG */ \"(app-pages-browser)/./src/components/ui/WasherSVG.tsx\");\n/* harmony import */ var _ui_DryerOutlineSVG__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/DryerOutlineSVG */ \"(app-pages-browser)/./src/components/ui/DryerOutlineSVG.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\nfunction MachineStatus(param) {\n    let { machine } = param;\n    _s();\n    const countdown = (0,_src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(machine.endAt, machine.graceEndAt);\n    if (machine.status === \"free\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-green-100 text-green-800 border-2 border-green-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"w-2 h-2 bg-green-500 rounded-full mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 11\n                    }, this),\n                    \"Available\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                lineNumber: 17,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this);\n    }\n    if (machine.status === \"running\" && machine.endAt) {\n        const hours = Math.floor(countdown.secondsLeft / 3600);\n        const minutes = Math.floor(countdown.secondsLeft % 3600 / 60);\n        const seconds = countdown.secondsLeft % 60;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-blue-100 text-blue-800 border-2 border-blue-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"w-2 h-2 bg-blue-500 rounded-full mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 11\n                    }, this),\n                    hours,\n                    \":\",\n                    minutes.toString().padStart(2, \"0\"),\n                    \":\",\n                    seconds.toString().padStart(2, \"0\"),\n                    \" left\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this);\n    }\n    if (machine.status === \"finishedGrace\") {\n        let graceTimeDisplay = \"5:00\";\n        if (machine.graceEndAt) {\n            const minutes = Math.floor(countdown.graceSecondsLeft / 60);\n            const seconds = countdown.graceSecondsLeft % 60;\n            graceTimeDisplay = \"\".concat(minutes, \":\").concat(seconds.toString().padStart(2, \"0\"));\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-orange-100 text-orange-800 border-2 border-orange-200 animate-pulse-slow\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"w-2 h-2 bg-orange-500 rounded-full mr-2 animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, this),\n                    \"Collect items - \",\n                    graceTimeDisplay\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-gray-100 text-gray-800 border-2 border-gray-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"w-2 h-2 bg-gray-500 rounded-full mr-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this),\n                \"Unknown\"\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n            lineNumber: 61,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\n_s(MachineStatus, \"u8Q9UI2BbjjlXAW3yY+wFIi4lfA=\", false, function() {\n    return [\n        _src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n    ];\n});\n_c = MachineStatus;\nfunction ActionButton(param) {\n    let { machine } = param;\n    const handleClick = ()=>{\n        const url = \"\".concat( true ? window.location.origin : 0, \"/checkin?machine=\").concat(machine.id);\n        window.open(url, \"_blank\");\n    };\n    if (machine.status === \"free\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: handleClick,\n            className: \"w-full bg-accent hover:bg-teal-600 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-accent focus:ring-offset-2\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"flex items-center justify-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"▶️\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Start Using\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                lineNumber: 81,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n            lineNumber: 77,\n            columnNumber: 7\n        }, this);\n    }\n    if (machine.status === \"finishedGrace\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: handleClick,\n            className: \"w-full bg-orange-500 hover:bg-orange-600 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-[1.02] animate-pulse-slow focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"flex items-center justify-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"✅\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"I've Collected Items\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                lineNumber: 95,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n            lineNumber: 91,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: handleClick,\n        className: \"w-full bg-warn hover:bg-red-600 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-warn focus:ring-offset-2\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"flex items-center justify-center gap-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: \"⏹️\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: \"Stop Using\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n            lineNumber: 108,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ActionButton;\nfunction LaundryCard() {\n    _s1();\n    const { laundry, incidents, deleteIncident } = (0,_src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n    // Count total incidents for the badge\n    const totalIncidents = incidents.length;\n    // Custom display names for machines\n    const getDisplayName = (machine)=>{\n        const isWasher = machine.name.toLowerCase().includes(\"washer\");\n        if (isWasher) {\n            // Extract number from washer name (e.g., \"Washer 1\" -> \"1\")\n            const numberMatch = machine.name.match(/\\d+/);\n            const number = numberMatch ? numberMatch[0] : \"\";\n            return \"Washer \".concat(number);\n        } else {\n            // For dryers, use \"Dryer 5\" through \"Dryer 8\" based on original number\n            const numberMatch = machine.name.match(/\\d+/);\n            const originalNumber = numberMatch ? Number.parseInt(numberMatch[0]) : 0;\n            const newNumber = originalNumber + 4 // Map 1->5, 2->6, 3->7, 4->8\n            ;\n            return \"Dryer \".concat(newNumber);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_CardWrapper__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        color: \"white\",\n        className: \"border-l-4 border-accent shadow-lg\",\n        count: totalIncidents,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-4 h-4 bg-accent rounded-full mr-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-primary\",\n                                children: \"\\uD83D\\uDC55 Laundry Machines\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-600 bg-bgLight px-3 py-1 rounded-full\",\n                        children: [\n                            laundry.filter((m)=>m.status === \"free\").length,\n                            \" of \",\n                            laundry.length,\n                            \" available\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\",\n                children: laundry.map((machine)=>{\n                    var _machine_graceEndAt, _machine_endAt;\n                    const machineIncidents = incidents.filter((inc)=>inc.machineId === machine.id);\n                    const isWasher = machine.name.toLowerCase().includes(\"washer\");\n                    const displayName = getDisplayName(machine);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border-2 border-gray-100 rounded-xl p-6 shadow-sm hover:shadow-lg transition-all duration-300 hover:border-accent/30 relative group\",\n                        children: [\n                            machineIncidents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-3 -right-3 z-10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_IncidentBadge__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    incidents: machineIncidents,\n                                    onDelete: deleteIncident\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 bg-bgLight rounded-xl group-hover:bg-accent/10 transition-colors duration-300\",\n                                    children: isWasher ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_WasherSVG__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-20 h-20\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 31\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_DryerOutlineSVG__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-20 h-20\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 69\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-bold text-primary text-xl mb-1\",\n                                        children: displayName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500 capitalize\",\n                                        children: isWasher ? \"Washing Machine\" : \"Dryer\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MachineStatus, {\n                                        machine: machine\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 17\n                                    }, this),\n                                    machine.status === \"finishedGrace\" && machine.graceEndAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-orange-600 text-center mt-2 bg-orange-50 py-2 px-3 rounded-lg\",\n                                        children: [\n                                            \"Grace ends: \",\n                                            (_machine_graceEndAt = machine.graceEndAt) === null || _machine_graceEndAt === void 0 ? void 0 : _machine_graceEndAt.toLocaleTimeString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 19\n                                    }, this),\n                                    machine.status === \"running\" && machine.endAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-blue-600 text-center mt-2 bg-blue-50 py-2 px-3 rounded-lg\",\n                                        children: [\n                                            \"Cycle ends: \",\n                                            (_machine_endAt = machine.endAt) === null || _machine_endAt === void 0 ? void 0 : _machine_endAt.toLocaleTimeString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                                    machine: machine\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t border-gray-100 pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 text-center mb-2 font-medium\",\n                                        children: \"Quick Check-in\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white p-3 rounded-lg border-2 border-gray-200 hover:border-accent/50 transition-colors duration-200 group-hover:shadow-md\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QRCodeSVG, {\n                                                value: \"\".concat( true ? window.location.origin : 0, \"/checkin?machine=\").concat(machine.id),\n                                                size: 64,\n                                                fgColor: \"#1A1F36\",\n                                                bgColor: \"#FFFFFF\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, machine.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 pt-6 border-t border-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap justify-center gap-4 text-sm text-gray-600\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Available\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"In Use\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"w-2 h-2 bg-orange-500 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Please Collect\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n        lineNumber: 141,\n        columnNumber: 5\n    }, this);\n}\n_s1(LaundryCard, \"LxkqdWGvpeonC73MNgMFUYmDkJo=\", false, function() {\n    return [\n        _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_1__[\"default\"]\n    ];\n});\n_c2 = LaundryCard;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"MachineStatus\");\n$RefreshReg$(_c1, \"ActionButton\");\n$RefreshReg$(_c2, \"LaundryCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/LaundryCard.tsx\n"));

/***/ })

});