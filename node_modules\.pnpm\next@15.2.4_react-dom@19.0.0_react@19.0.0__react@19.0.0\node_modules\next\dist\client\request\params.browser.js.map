{"version": 3, "sources": ["../../../src/client/request/params.browser.ts"], "sourcesContent": ["export const createRenderParamsFromClient =\n  process.env.NODE_ENV === 'development'\n    ? (require('./params.browser.dev') as typeof import('./params.browser.dev'))\n        .makeDynamicallyTrackedExoticParamsWithDevWarnings\n    : (\n        require('./params.browser.prod') as typeof import('./params.browser.prod')\n      ).makeUntrackedExoticParams\n"], "names": ["createRenderParamsFromClient", "process", "env", "NODE_ENV", "require", "makeDynamicallyTrackedExoticParamsWithDevWarnings", "makeUntrackedExoticParams"], "mappings": ";;;;+BAAaA;;;eAAAA;;;AAAN,MAAMA,+BACXC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACrB,AAACC,QAAQ,wBACNC,iDAAiD,GACpD,AACED,QAAQ,yBACRE,yBAAyB"}