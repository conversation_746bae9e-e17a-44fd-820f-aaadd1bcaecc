"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useSupabaseData.tsx":
/*!***************************************!*\
  !*** ./src/hooks/useSupabaseData.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useSupabaseData)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/src/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _src_lib_subscriptionManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/src/lib/subscriptionManager */ \"(app-pages-browser)/./src/lib/subscriptionManager.ts\");\n/* harmony import */ var _src_lib_machineStatusManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/lib/machineStatusManager */ \"(app-pages-browser)/./src/lib/machineStatusManager.ts\");\n/* harmony import */ var _src_utils_parseDuration__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/src/utils/parseDuration */ \"(app-pages-browser)/./src/utils/parseDuration.tsx\");\n/* harmony import */ var _src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/src/utils/userIdentification */ \"(app-pages-browser)/./src/utils/userIdentification.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\n\n\n\n\n\n// Helper functions to convert between DB and app formats\nconst dbToMachine = (row)=>{\n    const graceEndAt = row.grace_end_at ? new Date(row.grace_end_at) : row.status === \"finishedGrace\" && row.end_at ? new Date(new Date(row.end_at).getTime() + 5 * 60 * 1000) : undefined;\n    return {\n        id: row.id,\n        name: row.name,\n        status: row.status,\n        startAt: row.start_at ? new Date(row.start_at) : undefined,\n        endAt: row.end_at ? new Date(row.end_at) : undefined,\n        graceEndAt: graceEndAt,\n        updatedAt: new Date(row.updated_at),\n        startedByUserId: row.started_by_user_id || undefined,\n        startedByDeviceFingerprint: row.started_by_device_fingerprint || undefined\n    };\n};\nconst dbToNoise = (row)=>({\n        id: row.id,\n        user: row.user_name,\n        description: row.description || \"Noise reported\",\n        timestamp: new Date(row.timestamp),\n        lastReported: new Date(row.last_reported)\n    });\nconst dbToAnnouncement = (row)=>({\n        id: row.id,\n        user: row.user_name,\n        title: row.title,\n        description: row.description,\n        type: row.announcement_type,\n        timestamp: new Date(row.timestamp)\n    });\nconst dbToSublet = (row)=>({\n        id: row.id,\n        user: row.user_name,\n        duration: row.duration,\n        timestamp: new Date(row.timestamp)\n    });\nconst dbToHelpMe = (row)=>({\n        id: row.id,\n        user: row.user_name,\n        description: row.description,\n        timestamp: new Date(row.timestamp)\n    });\nconst dbToIncident = (row)=>({\n        id: row.id,\n        machineId: row.machine_id,\n        timestamp: new Date(row.timestamp),\n        type: row.incident_type\n    });\nfunction useSupabaseData() {\n    _s();\n    const [laundry, setLaundry] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [noise, setNoise] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [announcements, setAnnouncements] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [helpMe, setHelpMe] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [incidents, setIncidents] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [hasGraceEndColumn, setHasGraceEndColumn] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    // Refs to prevent recursive calls and multiple setups\n    const isLoadingDataRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const lastLoadTimeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const subscriptionSetupRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const statusManagerSetupRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const isMountedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(true);\n    // Debounced data loading function with selective loading\n    const loadAllData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[loadAllData]\": async function(specificTable) {\n            let forceRefresh = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n            const now = Date.now();\n            // Skip debounce for manual refresh (forceRefresh = true)\n            if (!forceRefresh && (isLoadingDataRef.current || now - lastLoadTimeRef.current < 1000)) {\n                console.log(\"⏭️ Skipping data load (too recent or in progress)\");\n                return;\n            }\n            if (!isMountedRef.current) {\n                console.log(\"⏭️ Skipping data load (component unmounted)\");\n                return;\n            }\n            isLoadingDataRef.current = true;\n            lastLoadTimeRef.current = now;\n            try {\n                console.log(\"\\uD83D\\uDD04 Loading data from Supabase\".concat(specificTable ? \" (\".concat(specificTable, \")\") : \"\", \"...\"));\n                setError(null);\n                // If specific table is provided, only load that table\n                if (specificTable) {\n                    await loadSpecificTable(specificTable);\n                } else {\n                    // Load all data\n                    setIsLoading(true);\n                    await loadAllTables();\n                }\n                console.log(\"✅ Data loaded successfully\");\n            } catch (err) {\n                console.error(\"❌ Error loading data:\", err);\n                if (isMountedRef.current) {\n                    setError(err instanceof Error ? err.message : \"Failed to load data\");\n                }\n            } finally{\n                if (isMountedRef.current && !specificTable) {\n                    setIsLoading(false);\n                }\n                isLoadingDataRef.current = false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[loadAllData]\"], []);\n    // Helper function to load specific table\n    const loadSpecificTable = async (table)=>{\n        switch(table){\n            case \"machines\":\n                var _machinesResult_data;\n                const machinesResult = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").select(\"*\").order(\"name\");\n                if (machinesResult.error) throw machinesResult.error;\n                setLaundry(((_machinesResult_data = machinesResult.data) === null || _machinesResult_data === void 0 ? void 0 : _machinesResult_data.map(dbToMachine)) || []);\n                break;\n            case \"noise_reports\":\n                var _noiseResult_data;\n                const noiseResult = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"noise_reports\").select(\"*\").order(\"timestamp\", {\n                    ascending: false\n                }).limit(50);\n                if (noiseResult.error) throw noiseResult.error;\n                setNoise(((_noiseResult_data = noiseResult.data) === null || _noiseResult_data === void 0 ? void 0 : _noiseResult_data.map(dbToNoise)) || []);\n                break;\n            case \"announcements\":\n                var _announcementsResult_data;\n                const announcementsResult = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"announcements\").select(\"*\").order(\"timestamp\", {\n                    ascending: false\n                }).limit(50);\n                if (announcementsResult.error) throw announcementsResult.error;\n                setAnnouncements(((_announcementsResult_data = announcementsResult.data) === null || _announcementsResult_data === void 0 ? void 0 : _announcementsResult_data.map(dbToAnnouncement)) || []);\n                break;\n            case \"help_requests\":\n                var _helpResult_data;\n                const helpResult = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"help_requests\").select(\"*\").gte(\"timestamp\", new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()).order(\"timestamp\", {\n                    ascending: false\n                }).limit(50);\n                if (helpResult.error) throw helpResult.error;\n                setHelpMe(((_helpResult_data = helpResult.data) === null || _helpResult_data === void 0 ? void 0 : _helpResult_data.map(dbToHelpMe)) || []);\n                break;\n            case \"incidents\":\n                var _incidentsResult_data;\n                const incidentsResult = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"incidents\").select(\"*\").order(\"timestamp\", {\n                    ascending: false\n                }).limit(50);\n                if (incidentsResult.error) throw incidentsResult.error;\n                setIncidents(((_incidentsResult_data = incidentsResult.data) === null || _incidentsResult_data === void 0 ? void 0 : _incidentsResult_data.map(dbToIncident)) || []);\n                break;\n        }\n    };\n    // Helper function to load all tables\n    const loadAllTables = async ()=>{\n        var _machinesResult_data, _noiseResult_data, _announcementsResult_data, _helpResult_data, _incidentsResult_data;\n        const timeoutPromise = new Promise((_, reject)=>setTimeout(()=>reject(new Error(\"Request timeout\")), 15000));\n        const dataPromise = Promise.all([\n            _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").select(\"*\").order(\"name\"),\n            _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"noise_reports\").select(\"*\").order(\"timestamp\", {\n                ascending: false\n            }).limit(50),\n            _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"announcements\").select(\"*\").order(\"timestamp\", {\n                ascending: false\n            }).limit(50),\n            _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"help_requests\").select(\"*\").gte(\"timestamp\", new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()).order(\"timestamp\", {\n                ascending: false\n            }).limit(50),\n            _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"incidents\").select(\"*\").order(\"timestamp\", {\n                ascending: false\n            }).limit(50)\n        ]);\n        const [machinesResult, noiseResult, announcementsResult, helpResult, incidentsResult] = await Promise.race([\n            dataPromise,\n            timeoutPromise\n        ]);\n        if (!isMountedRef.current) return;\n        if (machinesResult.error) throw new Error(\"Failed to load machines: \".concat(machinesResult.error.message));\n        if (noiseResult.error) throw new Error(\"Failed to load noise reports: \".concat(noiseResult.error.message));\n        if (announcementsResult.error) throw new Error(\"Failed to load announcements: \".concat(announcementsResult.error.message));\n        if (helpResult.error) throw new Error(\"Failed to load help requests: \".concat(helpResult.error.message));\n        if (incidentsResult.error) throw new Error(\"Failed to load incidents: \".concat(incidentsResult.error.message));\n        setLaundry(((_machinesResult_data = machinesResult.data) === null || _machinesResult_data === void 0 ? void 0 : _machinesResult_data.map(dbToMachine)) || []);\n        setNoise(((_noiseResult_data = noiseResult.data) === null || _noiseResult_data === void 0 ? void 0 : _noiseResult_data.map(dbToNoise)) || []);\n        setAnnouncements(((_announcementsResult_data = announcementsResult.data) === null || _announcementsResult_data === void 0 ? void 0 : _announcementsResult_data.map(dbToAnnouncement)) || []);\n        setHelpMe(((_helpResult_data = helpResult.data) === null || _helpResult_data === void 0 ? void 0 : _helpResult_data.map(dbToHelpMe)) || []);\n        setIncidents(((_incidentsResult_data = incidentsResult.data) === null || _incidentsResult_data === void 0 ? void 0 : _incidentsResult_data.map(dbToIncident)) || []);\n    };\n    // Set up real-time subscriptions with table-specific updates\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseData.useEffect\": ()=>{\n            if (subscriptionSetupRef.current) {\n                return;\n            }\n            subscriptionSetupRef.current = true;\n            console.log(\"🔄 Setting up subscription manager...\");\n            const subscriptionManager = _src_lib_subscriptionManager__WEBPACK_IMPORTED_MODULE_2__[\"default\"].getInstance();\n            // Enhanced callback that handles specific table updates\n            const handleRealtimeUpdate = {\n                \"useSupabaseData.useEffect.handleRealtimeUpdate\": (table, event)=>{\n                    if (table && table !== \"polling\") {\n                        // Load only the specific table that changed for faster updates\n                        loadAllData(table);\n                    } else {\n                        // Fallback to loading all data\n                        loadAllData();\n                    }\n                }\n            }[\"useSupabaseData.useEffect.handleRealtimeUpdate\"];\n            subscriptionManager.addCallback(handleRealtimeUpdate);\n            return ({\n                \"useSupabaseData.useEffect\": ()=>{\n                    console.log(\"🧹 Cleaning up subscription manager...\");\n                    subscriptionManager.removeCallback(handleRealtimeUpdate);\n                    subscriptionSetupRef.current = false;\n                }\n            })[\"useSupabaseData.useEffect\"];\n        }\n    }[\"useSupabaseData.useEffect\"], [\n        loadAllData\n    ]);\n    // Set up machine status monitoring (only once)\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseData.useEffect\": ()=>{\n            if (statusManagerSetupRef.current) {\n                return;\n            }\n            statusManagerSetupRef.current = true;\n            console.log(\"🔄 Setting up machine status manager...\");\n            const statusManager = _src_lib_machineStatusManager__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getInstance();\n            statusManager.startStatusMonitoring();\n            return ({\n                \"useSupabaseData.useEffect\": ()=>{\n                    console.log(\"🧹 Cleaning up machine status manager...\");\n                    statusManager.stopStatusMonitoring();\n                    statusManagerSetupRef.current = false;\n                }\n            })[\"useSupabaseData.useEffect\"];\n        }\n    }[\"useSupabaseData.useEffect\"], []);\n    // Initial data load and cleanup\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseData.useEffect\": ()=>{\n            isMountedRef.current = true;\n            loadAllData();\n            return ({\n                \"useSupabaseData.useEffect\": ()=>{\n                    isMountedRef.current = false;\n                }\n            })[\"useSupabaseData.useEffect\"];\n        }\n    }[\"useSupabaseData.useEffect\"], []) // Only run once on mount\n    ;\n    // Machine operations with optimistic updates\n    const toggleMachineStatus = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[toggleMachineStatus]\": async (id)=>{\n            try {\n                const machine = laundry.find({\n                    \"useSupabaseData.useCallback[toggleMachineStatus].machine\": (m)=>m.id === id\n                }[\"useSupabaseData.useCallback[toggleMachineStatus].machine\"]);\n                if (!machine) {\n                    return false;\n                }\n                let optimisticUpdate;\n                let updateData;\n                if (machine.status === \"free\") {\n                    const startAt = new Date();\n                    const endAt = new Date(startAt.getTime() + 60 * 60 * 1000);\n                    const currentUserId = (0,_src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_5__.getDeviceUserId)();\n                    optimisticUpdate = {\n                        ...machine,\n                        status: \"running\",\n                        startAt,\n                        endAt,\n                        graceEndAt: undefined,\n                        updatedAt: new Date(),\n                        startedByUserId: currentUserId,\n                        startedByDeviceFingerprint: currentUserId\n                    };\n                    updateData = {\n                        status: \"running\",\n                        start_at: startAt.toISOString(),\n                        end_at: endAt.toISOString(),\n                        started_by_user_id: currentUserId,\n                        started_by_device_fingerprint: currentUserId\n                    };\n                    if (hasGraceEndColumn) {\n                        updateData.grace_end_at = null;\n                    }\n                } else {\n                    optimisticUpdate = {\n                        ...machine,\n                        status: \"free\",\n                        startAt: undefined,\n                        endAt: undefined,\n                        graceEndAt: undefined,\n                        updatedAt: new Date(),\n                        startedByUserId: undefined,\n                        startedByDeviceFingerprint: undefined\n                    };\n                    updateData = {\n                        status: \"free\",\n                        start_at: null,\n                        end_at: null,\n                        started_by_user_id: null,\n                        started_by_device_fingerprint: null\n                    };\n                    if (hasGraceEndColumn) {\n                        updateData.grace_end_at = null;\n                    }\n                }\n                // Apply optimistic update\n                setLaundry({\n                    \"useSupabaseData.useCallback[toggleMachineStatus]\": (prev)=>prev.map({\n                            \"useSupabaseData.useCallback[toggleMachineStatus]\": (m)=>m.id === id ? optimisticUpdate : m\n                        }[\"useSupabaseData.useCallback[toggleMachineStatus]\"])\n                }[\"useSupabaseData.useCallback[toggleMachineStatus]\"]);\n                // Send update to database\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").update(updateData).eq(\"id\", id);\n                if (error) {\n                    if (error.message && error.message.includes(\"grace_end_at\")) {\n                        setHasGraceEndColumn(false);\n                        delete updateData.grace_end_at;\n                        const { error: retryError } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").update(updateData).eq(\"id\", id);\n                        if (retryError) {\n                            setLaundry({\n                                \"useSupabaseData.useCallback[toggleMachineStatus]\": (prev)=>prev.map({\n                                        \"useSupabaseData.useCallback[toggleMachineStatus]\": (m)=>m.id === id ? machine : m\n                                    }[\"useSupabaseData.useCallback[toggleMachineStatus]\"])\n                            }[\"useSupabaseData.useCallback[toggleMachineStatus]\"]);\n                            throw retryError;\n                        }\n                    } else {\n                        setLaundry({\n                            \"useSupabaseData.useCallback[toggleMachineStatus]\": (prev)=>prev.map({\n                                    \"useSupabaseData.useCallback[toggleMachineStatus]\": (m)=>m.id === id ? machine : m\n                                }[\"useSupabaseData.useCallback[toggleMachineStatus]\"])\n                        }[\"useSupabaseData.useCallback[toggleMachineStatus]\"]);\n                        throw error;\n                    }\n                }\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to update machine\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[toggleMachineStatus]\"], [\n        laundry,\n        hasGraceEndColumn\n    ]);\n    const reserveMachine = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[reserveMachine]\": async (id, durationStr)=>{\n            try {\n                const machine = laundry.find({\n                    \"useSupabaseData.useCallback[reserveMachine].machine\": (m)=>m.id === id\n                }[\"useSupabaseData.useCallback[reserveMachine].machine\"]);\n                if (!machine) {\n                    return false;\n                }\n                const durationSeconds = (0,_src_utils_parseDuration__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(durationStr);\n                const startAt = new Date();\n                const endAt = new Date(startAt.getTime() + durationSeconds * 1000);\n                const currentUserId = (0,_src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_5__.getDeviceUserId)();\n                const optimisticUpdate = {\n                    ...machine,\n                    status: \"running\",\n                    startAt,\n                    endAt,\n                    graceEndAt: undefined,\n                    updatedAt: new Date(),\n                    startedByUserId: currentUserId,\n                    startedByDeviceFingerprint: currentUserId\n                };\n                setLaundry({\n                    \"useSupabaseData.useCallback[reserveMachine]\": (prev)=>prev.map({\n                            \"useSupabaseData.useCallback[reserveMachine]\": (m)=>m.id === id ? optimisticUpdate : m\n                        }[\"useSupabaseData.useCallback[reserveMachine]\"])\n                }[\"useSupabaseData.useCallback[reserveMachine]\"]);\n                const updateData = {\n                    status: \"running\",\n                    start_at: startAt.toISOString(),\n                    end_at: endAt.toISOString(),\n                    started_by_user_id: currentUserId,\n                    started_by_device_fingerprint: currentUserId\n                };\n                if (hasGraceEndColumn) {\n                    updateData.grace_end_at = null;\n                }\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").update(updateData).eq(\"id\", id);\n                if (error) {\n                    if (error.message && error.message.includes(\"grace_end_at\")) {\n                        setHasGraceEndColumn(false);\n                        delete updateData.grace_end_at;\n                        const { error: retryError } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").update(updateData).eq(\"id\", id);\n                        if (retryError) {\n                            setLaundry({\n                                \"useSupabaseData.useCallback[reserveMachine]\": (prev)=>prev.map({\n                                        \"useSupabaseData.useCallback[reserveMachine]\": (m)=>m.id === id ? machine : m\n                                    }[\"useSupabaseData.useCallback[reserveMachine]\"])\n                            }[\"useSupabaseData.useCallback[reserveMachine]\"]);\n                            throw retryError;\n                        }\n                    } else {\n                        setLaundry({\n                            \"useSupabaseData.useCallback[reserveMachine]\": (prev)=>prev.map({\n                                    \"useSupabaseData.useCallback[reserveMachine]\": (m)=>m.id === id ? machine : m\n                                }[\"useSupabaseData.useCallback[reserveMachine]\"])\n                        }[\"useSupabaseData.useCallback[reserveMachine]\"]);\n                        throw error;\n                    }\n                }\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to reserve machine\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[reserveMachine]\"], [\n        laundry,\n        hasGraceEndColumn\n    ]);\n    // New function to adjust machine time (only for machines you started)\n    const adjustMachineTime = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[adjustMachineTime]\": async (id, newMinutes)=>{\n            try {\n                const machine = laundry.find({\n                    \"useSupabaseData.useCallback[adjustMachineTime].machine\": (m)=>m.id === id\n                }[\"useSupabaseData.useCallback[adjustMachineTime].machine\"]);\n                if (!machine) {\n                    return {\n                        success: false,\n                        error: \"Machine not found\"\n                    };\n                }\n                const currentUserId = (0,_src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_5__.getDeviceUserId)();\n                // Check ownership - only the user who started the machine can adjust it\n                if (machine.startedByUserId !== currentUserId) {\n                    return {\n                        success: false,\n                        error: \"You can only adjust machines you started\"\n                    };\n                }\n                // Validate time range (1-120 minutes for adjustment)\n                if (newMinutes < 1 || newMinutes > 120) {\n                    return {\n                        success: false,\n                        error: \"Please enter a number between 1-120 minutes\"\n                    };\n                }\n                // Calculate new end time\n                const now = new Date();\n                const newEndAt = new Date(now.getTime() + newMinutes * 60 * 1000);\n                const optimisticUpdate = {\n                    ...machine,\n                    endAt: newEndAt,\n                    updatedAt: new Date()\n                };\n                // Apply optimistic update\n                setLaundry({\n                    \"useSupabaseData.useCallback[adjustMachineTime]\": (prev)=>prev.map({\n                            \"useSupabaseData.useCallback[adjustMachineTime]\": (m)=>m.id === id ? optimisticUpdate : m\n                        }[\"useSupabaseData.useCallback[adjustMachineTime]\"])\n                }[\"useSupabaseData.useCallback[adjustMachineTime]\"]);\n                // Send update to database\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").update({\n                    end_at: newEndAt.toISOString()\n                }).eq(\"id\", id);\n                if (error) {\n                    // Revert optimistic update on error\n                    setLaundry({\n                        \"useSupabaseData.useCallback[adjustMachineTime]\": (prev)=>prev.map({\n                                \"useSupabaseData.useCallback[adjustMachineTime]\": (m)=>m.id === id ? machine : m\n                            }[\"useSupabaseData.useCallback[adjustMachineTime]\"])\n                    }[\"useSupabaseData.useCallback[adjustMachineTime]\"]);\n                    return {\n                        success: false,\n                        error: \"Unable to update timer. Please try again.\"\n                    };\n                }\n                return {\n                    success: true,\n                    error: null\n                };\n            } catch (err) {\n                return {\n                    success: false,\n                    error: err instanceof Error ? err.message : \"Unable to update timer. Please try again.\"\n                };\n            }\n        }\n    }[\"useSupabaseData.useCallback[adjustMachineTime]\"], [\n        laundry\n    ]);\n    // Simplified CRUD operations\n    const addNoiseWithDescription = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[addNoiseWithDescription]\": async (user, description)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"noise_reports\").insert({\n                    id: Date.now().toString(),\n                    user_name: user,\n                    description: description\n                });\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to add noise report\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[addNoiseWithDescription]\"], []);\n    const deleteNoise = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[deleteNoise]\": async (id)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"noise_reports\").delete().eq(\"id\", id);\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to delete noise report\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[deleteNoise]\"], []);\n    const addAnnouncement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[addAnnouncement]\": async (user, title, description, type)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"announcements\").insert({\n                    id: Date.now().toString(),\n                    user_name: user,\n                    title: title,\n                    description: description,\n                    announcement_type: type\n                });\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to add announcement\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[addAnnouncement]\"], []);\n    const deleteAnnouncement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[deleteAnnouncement]\": async (id)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"announcements\").delete().eq(\"id\", id);\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to delete announcement\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[deleteAnnouncement]\"], []);\n    const addHelpRequest = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[addHelpRequest]\": async (user, description)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"help_requests\").insert({\n                    id: Date.now().toString(),\n                    user_name: user,\n                    description\n                });\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to add help request\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[addHelpRequest]\"], []);\n    const deleteHelpRequest = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[deleteHelpRequest]\": async (id)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"help_requests\").delete().eq(\"id\", id);\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to delete help request\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[deleteHelpRequest]\"], []);\n    const deleteIncident = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[deleteIncident]\": async (id)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"incidents\").delete().eq(\"id\", id);\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to delete incident\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[deleteIncident]\"], []);\n    return {\n        // Data\n        laundry,\n        noise,\n        announcements,\n        helpMe,\n        incidents,\n        // State\n        isLoading,\n        error,\n        // Operations\n        toggleMachineStatus,\n        reserveMachine,\n        adjustMachineTime,\n        addNoiseWithDescription,\n        deleteNoise,\n        addAnnouncement,\n        deleteAnnouncement,\n        addHelpRequest,\n        deleteHelpRequest,\n        deleteIncident,\n        refreshData: ()=>loadAllData(undefined, true)\n    };\n}\n_s(useSupabaseData, \"2+7VgD2CuMwPZ4C28ZFRT/x3kEM=\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9ob29rcy91c2VTdXBhYmFzZURhdGEudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUVnRTtBQUNuQjtBQUVrQjtBQUNFO0FBQ1o7QUFDVztBQXFEaEUseURBQXlEO0FBQ3pELE1BQU1TLGNBQWMsQ0FBQ0M7SUFDbkIsTUFBTUMsYUFBYUQsSUFBSUUsWUFBWSxHQUMvQixJQUFJQyxLQUFLSCxJQUFJRSxZQUFZLElBQ3pCRixJQUFJSSxNQUFNLEtBQUssbUJBQW1CSixJQUFJSyxNQUFNLEdBQzFDLElBQUlGLEtBQUssSUFBSUEsS0FBS0gsSUFBSUssTUFBTSxFQUFFQyxPQUFPLEtBQUssSUFBSSxLQUFLLFFBQ25EQztJQUVOLE9BQU87UUFDTEMsSUFBSVIsSUFBSVEsRUFBRTtRQUNWQyxNQUFNVCxJQUFJUyxJQUFJO1FBQ2RMLFFBQVFKLElBQUlJLE1BQU07UUFDbEJNLFNBQVNWLElBQUlXLFFBQVEsR0FBRyxJQUFJUixLQUFLSCxJQUFJVyxRQUFRLElBQUlKO1FBQ2pESyxPQUFPWixJQUFJSyxNQUFNLEdBQUcsSUFBSUYsS0FBS0gsSUFBSUssTUFBTSxJQUFJRTtRQUMzQ04sWUFBWUE7UUFDWlksV0FBVyxJQUFJVixLQUFLSCxJQUFJYyxVQUFVO1FBQ2xDQyxpQkFBaUIsSUFBYUMsa0JBQWtCLElBQUlUO1FBQ3BEVSw0QkFBNEIsSUFBYUMsNkJBQTZCLElBQUlYO0lBQzVFO0FBQ0Y7QUFFQSxNQUFNWSxZQUFZLENBQUNuQixNQUEwQjtRQUMzQ1EsSUFBSVIsSUFBSVEsRUFBRTtRQUNWWSxNQUFNcEIsSUFBSXFCLFNBQVM7UUFDbkJDLGFBQWF0QixJQUFJc0IsV0FBVyxJQUFJO1FBQ2hDQyxXQUFXLElBQUlwQixLQUFLSCxJQUFJdUIsU0FBUztRQUNqQ0MsY0FBYyxJQUFJckIsS0FBS0gsSUFBSXlCLGFBQWE7SUFDMUM7QUFFQSxNQUFNQyxtQkFBbUIsQ0FBQzFCLE1BQWlDO1FBQ3pEUSxJQUFJUixJQUFJUSxFQUFFO1FBQ1ZZLE1BQU1wQixJQUFJcUIsU0FBUztRQUNuQk0sT0FBTzNCLElBQUkyQixLQUFLO1FBQ2hCTCxhQUFhdEIsSUFBSXNCLFdBQVc7UUFDNUJNLE1BQU01QixJQUFJNkIsaUJBQWlCO1FBQzNCTixXQUFXLElBQUlwQixLQUFLSCxJQUFJdUIsU0FBUztJQUNuQztBQUVBLE1BQU1PLGFBQWEsQ0FBQzlCLE1BQXNFO1FBQ3hGUSxJQUFJUixJQUFJUSxFQUFFO1FBQ1ZZLE1BQU1wQixJQUFJcUIsU0FBUztRQUNuQlUsVUFBVS9CLElBQUkrQixRQUFRO1FBQ3RCUixXQUFXLElBQUlwQixLQUFLSCxJQUFJdUIsU0FBUztJQUNuQztBQUVBLE1BQU1TLGFBQWEsQ0FBQ2hDLE1BQTRFO1FBQzlGUSxJQUFJUixJQUFJUSxFQUFFO1FBQ1ZZLE1BQU1wQixJQUFJcUIsU0FBUztRQUNuQkMsYUFBYXRCLElBQUlzQixXQUFXO1FBQzVCQyxXQUFXLElBQUlwQixLQUFLSCxJQUFJdUIsU0FBUztJQUNuQztBQUVBLE1BQU1VLGVBQWUsQ0FBQ2pDLE1BQXFFO1FBQ3pGUSxJQUFJUixJQUFJUSxFQUFFO1FBQ1YwQixXQUFXbEMsSUFBSW1DLFVBQVU7UUFDekJaLFdBQVcsSUFBSXBCLEtBQUtILElBQUl1QixTQUFTO1FBQ2pDSyxNQUFNNUIsSUFBSW9DLGFBQWE7SUFDekI7QUFFZSxTQUFTQzs7SUFDdEIsTUFBTSxDQUFDQyxTQUFTQyxXQUFXLEdBQUdqRCwrQ0FBUUEsQ0FBWSxFQUFFO0lBQ3BELE1BQU0sQ0FBQ2tELE9BQU9DLFNBQVMsR0FBR25ELCtDQUFRQSxDQUFlLEVBQUU7SUFDbkQsTUFBTSxDQUFDb0QsZUFBZUMsaUJBQWlCLEdBQUdyRCwrQ0FBUUEsQ0FBc0IsRUFBRTtJQUMxRSxNQUFNLENBQUNzRCxRQUFRQyxVQUFVLEdBQUd2RCwrQ0FBUUEsQ0FBZ0IsRUFBRTtJQUN0RCxNQUFNLENBQUN3RCxXQUFXQyxhQUFhLEdBQUd6RCwrQ0FBUUEsQ0FBYSxFQUFFO0lBQ3pELE1BQU0sQ0FBQzBELFdBQVdDLGFBQWEsR0FBRzNELCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQzRELE9BQU9DLFNBQVMsR0FBRzdELCtDQUFRQSxDQUFnQjtJQUNsRCxNQUFNLENBQUM4RCxtQkFBbUJDLHFCQUFxQixHQUFHL0QsK0NBQVFBLENBQUM7SUFFM0Qsc0RBQXNEO0lBQ3RELE1BQU1nRSxtQkFBbUI3RCw2Q0FBTUEsQ0FBQztJQUNoQyxNQUFNOEQsa0JBQWtCOUQsNkNBQU1BLENBQVM7SUFDdkMsTUFBTStELHVCQUF1Qi9ELDZDQUFNQSxDQUFDO0lBQ3BDLE1BQU1nRSx3QkFBd0JoRSw2Q0FBTUEsQ0FBQztJQUNyQyxNQUFNaUUsZUFBZWpFLDZDQUFNQSxDQUFDO0lBRTVCLHlEQUF5RDtJQUN6RCxNQUFNa0UsY0FBY25FLGtEQUFXQTtvREFBQyxlQUFPb0U7Z0JBQXdCQyxnRkFBZTtZQUM1RSxNQUFNQyxNQUFNM0QsS0FBSzJELEdBQUc7WUFDcEIseURBQXlEO1lBQ3pELElBQUksQ0FBQ0QsZ0JBQWlCUCxDQUFBQSxpQkFBaUJTLE9BQU8sSUFBSUQsTUFBTVAsZ0JBQWdCUSxPQUFPLEdBQUcsSUFBRyxHQUFJO2dCQUN2RkMsUUFBUUMsR0FBRyxDQUFDO2dCQUNaO1lBQ0Y7WUFFQSxJQUFJLENBQUNQLGFBQWFLLE9BQU8sRUFBRTtnQkFDekJDLFFBQVFDLEdBQUcsQ0FBQztnQkFDWjtZQUNGO1lBRUFYLGlCQUFpQlMsT0FBTyxHQUFHO1lBQzNCUixnQkFBZ0JRLE9BQU8sR0FBR0Q7WUFFMUIsSUFBSTtnQkFDRkUsUUFBUUMsR0FBRyxDQUFDLDBDQUEyRSxPQUEzQ0wsZ0JBQWdCLEtBQW1CLE9BQWRBLGVBQWMsT0FBSyxJQUFHO2dCQUN2RlQsU0FBUztnQkFFVCxzREFBc0Q7Z0JBQ3RELElBQUlTLGVBQWU7b0JBQ2pCLE1BQU1NLGtCQUFrQk47Z0JBQzFCLE9BQU87b0JBQ0wsZ0JBQWdCO29CQUNoQlgsYUFBYTtvQkFDYixNQUFNa0I7Z0JBQ1I7Z0JBRUFILFFBQVFDLEdBQUcsQ0FBQztZQUNkLEVBQUUsT0FBT0csS0FBSztnQkFDWkosUUFBUWQsS0FBSyxDQUFDLHlCQUF5QmtCO2dCQUN2QyxJQUFJVixhQUFhSyxPQUFPLEVBQUU7b0JBQ3hCWixTQUFTaUIsZUFBZUMsUUFBUUQsSUFBSUUsT0FBTyxHQUFHO2dCQUNoRDtZQUNGLFNBQVU7Z0JBQ1IsSUFBSVosYUFBYUssT0FBTyxJQUFJLENBQUNILGVBQWU7b0JBQzFDWCxhQUFhO2dCQUNmO2dCQUNBSyxpQkFBaUJTLE9BQU8sR0FBRztZQUM3QjtRQUNGO21EQUFHLEVBQUU7SUFFTCx5Q0FBeUM7SUFDekMsTUFBTUcsb0JBQW9CLE9BQU9LO1FBQy9CLE9BQVFBO1lBQ04sS0FBSztvQkFHUUM7Z0JBRlgsTUFBTUEsaUJBQWlCLE1BQU05RSx1REFBUUEsQ0FBQytFLElBQUksQ0FBQyxZQUFZQyxNQUFNLENBQUMsS0FBS0MsS0FBSyxDQUFDO2dCQUN6RSxJQUFJSCxlQUFldEIsS0FBSyxFQUFFLE1BQU1zQixlQUFldEIsS0FBSztnQkFDcERYLFdBQVdpQyxFQUFBQSx1QkFBQUEsZUFBZUksSUFBSSxjQUFuQkosMkNBQUFBLHFCQUFxQkssR0FBRyxDQUFDOUUsaUJBQWdCLEVBQUU7Z0JBQ3REO1lBRUYsS0FBSztvQkFPTStFO2dCQU5ULE1BQU1BLGNBQWMsTUFBTXBGLHVEQUFRQSxDQUMvQitFLElBQUksQ0FBQyxpQkFDTEMsTUFBTSxDQUFDLEtBQ1BDLEtBQUssQ0FBQyxhQUFhO29CQUFFSSxXQUFXO2dCQUFNLEdBQ3RDQyxLQUFLLENBQUM7Z0JBQ1QsSUFBSUYsWUFBWTVCLEtBQUssRUFBRSxNQUFNNEIsWUFBWTVCLEtBQUs7Z0JBQzlDVCxTQUFTcUMsRUFBQUEsb0JBQUFBLFlBQVlGLElBQUksY0FBaEJFLHdDQUFBQSxrQkFBa0JELEdBQUcsQ0FBQzFELGVBQWMsRUFBRTtnQkFDL0M7WUFFRixLQUFLO29CQU9jOEQ7Z0JBTmpCLE1BQU1BLHNCQUFzQixNQUFNdkYsdURBQVFBLENBQ3ZDK0UsSUFBSSxDQUFDLGlCQUNMQyxNQUFNLENBQUMsS0FDUEMsS0FBSyxDQUFDLGFBQWE7b0JBQUVJLFdBQVc7Z0JBQU0sR0FDdENDLEtBQUssQ0FBQztnQkFDVCxJQUFJQyxvQkFBb0IvQixLQUFLLEVBQUUsTUFBTStCLG9CQUFvQi9CLEtBQUs7Z0JBQzlEUCxpQkFBaUJzQyxFQUFBQSw0QkFBQUEsb0JBQW9CTCxJQUFJLGNBQXhCSyxnREFBQUEsMEJBQTBCSixHQUFHLENBQUNuRCxzQkFBcUIsRUFBRTtnQkFDdEU7WUFFRixLQUFLO29CQVFPd0Q7Z0JBUFYsTUFBTUEsYUFBYSxNQUFNeEYsdURBQVFBLENBQzlCK0UsSUFBSSxDQUFDLGlCQUNMQyxNQUFNLENBQUMsS0FDUFMsR0FBRyxDQUFDLGFBQWEsSUFBSWhGLEtBQUtBLEtBQUsyRCxHQUFHLEtBQUssS0FBSyxLQUFLLEtBQUssTUFBTXNCLFdBQVcsSUFDdkVULEtBQUssQ0FBQyxhQUFhO29CQUFFSSxXQUFXO2dCQUFNLEdBQ3RDQyxLQUFLLENBQUM7Z0JBQ1QsSUFBSUUsV0FBV2hDLEtBQUssRUFBRSxNQUFNZ0MsV0FBV2hDLEtBQUs7Z0JBQzVDTCxVQUFVcUMsRUFBQUEsbUJBQUFBLFdBQVdOLElBQUksY0FBZk0sdUNBQUFBLGlCQUFpQkwsR0FBRyxDQUFDN0MsZ0JBQWUsRUFBRTtnQkFDaEQ7WUFFRixLQUFLO29CQU9VcUQ7Z0JBTmIsTUFBTUEsa0JBQWtCLE1BQU0zRix1REFBUUEsQ0FDbkMrRSxJQUFJLENBQUMsYUFDTEMsTUFBTSxDQUFDLEtBQ1BDLEtBQUssQ0FBQyxhQUFhO29CQUFFSSxXQUFXO2dCQUFNLEdBQ3RDQyxLQUFLLENBQUM7Z0JBQ1QsSUFBSUssZ0JBQWdCbkMsS0FBSyxFQUFFLE1BQU1tQyxnQkFBZ0JuQyxLQUFLO2dCQUN0REgsYUFBYXNDLEVBQUFBLHdCQUFBQSxnQkFBZ0JULElBQUksY0FBcEJTLDRDQUFBQSxzQkFBc0JSLEdBQUcsQ0FBQzVDLGtCQUFpQixFQUFFO2dCQUMxRDtRQUNKO0lBQ0Y7SUFFQSxxQ0FBcUM7SUFDckMsTUFBTWtDLGdCQUFnQjtZQTZCVEssc0JBQ0ZNLG1CQUNRRywyQkFDUEMsa0JBQ0dHO1FBaENiLE1BQU1DLGlCQUFpQixJQUFJQyxRQUFRLENBQUNDLEdBQUdDLFNBQVdDLFdBQVcsSUFBTUQsT0FBTyxJQUFJcEIsTUFBTSxxQkFBcUI7UUFFekcsTUFBTXNCLGNBQWNKLFFBQVFLLEdBQUcsQ0FBQztZQUM5QmxHLHVEQUFRQSxDQUFDK0UsSUFBSSxDQUFDLFlBQVlDLE1BQU0sQ0FBQyxLQUFLQyxLQUFLLENBQUM7WUFDNUNqRix1REFBUUEsQ0FBQytFLElBQUksQ0FBQyxpQkFBaUJDLE1BQU0sQ0FBQyxLQUFLQyxLQUFLLENBQUMsYUFBYTtnQkFBRUksV0FBVztZQUFNLEdBQUdDLEtBQUssQ0FBQztZQUMxRnRGLHVEQUFRQSxDQUFDK0UsSUFBSSxDQUFDLGlCQUFpQkMsTUFBTSxDQUFDLEtBQUtDLEtBQUssQ0FBQyxhQUFhO2dCQUFFSSxXQUFXO1lBQU0sR0FBR0MsS0FBSyxDQUFDO1lBQzFGdEYsdURBQVFBLENBQ0wrRSxJQUFJLENBQUMsaUJBQ0xDLE1BQU0sQ0FBQyxLQUNQUyxHQUFHLENBQUMsYUFBYSxJQUFJaEYsS0FBS0EsS0FBSzJELEdBQUcsS0FBSyxLQUFLLEtBQUssS0FBSyxNQUFNc0IsV0FBVyxJQUN2RVQsS0FBSyxDQUFDLGFBQWE7Z0JBQUVJLFdBQVc7WUFBTSxHQUN0Q0MsS0FBSyxDQUFDO1lBQ1R0Rix1REFBUUEsQ0FBQytFLElBQUksQ0FBQyxhQUFhQyxNQUFNLENBQUMsS0FBS0MsS0FBSyxDQUFDLGFBQWE7Z0JBQUVJLFdBQVc7WUFBTSxHQUFHQyxLQUFLLENBQUM7U0FDdkY7UUFFRCxNQUFNLENBQUNSLGdCQUFnQk0sYUFBYUcscUJBQXFCQyxZQUFZRyxnQkFBZ0IsR0FBSSxNQUFNRSxRQUFRTSxJQUFJLENBQUM7WUFDMUdGO1lBQ0FMO1NBQ0Q7UUFFRCxJQUFJLENBQUM1QixhQUFhSyxPQUFPLEVBQUU7UUFFM0IsSUFBSVMsZUFBZXRCLEtBQUssRUFBRSxNQUFNLElBQUltQixNQUFNLDRCQUF5RCxPQUE3QkcsZUFBZXRCLEtBQUssQ0FBQ29CLE9BQU87UUFDbEcsSUFBSVEsWUFBWTVCLEtBQUssRUFBRSxNQUFNLElBQUltQixNQUFNLGlDQUEyRCxPQUExQlMsWUFBWTVCLEtBQUssQ0FBQ29CLE9BQU87UUFDakcsSUFBSVcsb0JBQW9CL0IsS0FBSyxFQUFFLE1BQU0sSUFBSW1CLE1BQU0saUNBQW1FLE9BQWxDWSxvQkFBb0IvQixLQUFLLENBQUNvQixPQUFPO1FBQ2pILElBQUlZLFdBQVdoQyxLQUFLLEVBQUUsTUFBTSxJQUFJbUIsTUFBTSxpQ0FBMEQsT0FBekJhLFdBQVdoQyxLQUFLLENBQUNvQixPQUFPO1FBQy9GLElBQUllLGdCQUFnQm5DLEtBQUssRUFBRSxNQUFNLElBQUltQixNQUFNLDZCQUEyRCxPQUE5QmdCLGdCQUFnQm5DLEtBQUssQ0FBQ29CLE9BQU87UUFFckcvQixXQUFXaUMsRUFBQUEsdUJBQUFBLGVBQWVJLElBQUksY0FBbkJKLDJDQUFBQSxxQkFBcUJLLEdBQUcsQ0FBQzlFLGlCQUFnQixFQUFFO1FBQ3REMEMsU0FBU3FDLEVBQUFBLG9CQUFBQSxZQUFZRixJQUFJLGNBQWhCRSx3Q0FBQUEsa0JBQWtCRCxHQUFHLENBQUMxRCxlQUFjLEVBQUU7UUFDL0N3QixpQkFBaUJzQyxFQUFBQSw0QkFBQUEsb0JBQW9CTCxJQUFJLGNBQXhCSyxnREFBQUEsMEJBQTBCSixHQUFHLENBQUNuRCxzQkFBcUIsRUFBRTtRQUN0RW1CLFVBQVVxQyxFQUFBQSxtQkFBQUEsV0FBV04sSUFBSSxjQUFmTSx1Q0FBQUEsaUJBQWlCTCxHQUFHLENBQUM3QyxnQkFBZSxFQUFFO1FBQ2hEZSxhQUFhc0MsRUFBQUEsd0JBQUFBLGdCQUFnQlQsSUFBSSxjQUFwQlMsNENBQUFBLHNCQUFzQlIsR0FBRyxDQUFDNUMsa0JBQWlCLEVBQUU7SUFDNUQ7SUFFQSw2REFBNkQ7SUFDN0QxQyxnREFBU0E7cUNBQUM7WUFDUixJQUFJaUUscUJBQXFCTyxPQUFPLEVBQUU7Z0JBQ2hDO1lBQ0Y7WUFFQVAscUJBQXFCTyxPQUFPLEdBQUc7WUFDL0JDLFFBQVFDLEdBQUcsQ0FBQztZQUVaLE1BQU02QixzQkFBc0JuRyxvRUFBbUJBLENBQUNvRyxXQUFXO1lBRTNELHdEQUF3RDtZQUN4RCxNQUFNQztrRUFBdUIsQ0FBQ3pCLE9BQWdCMEI7b0JBQzVDLElBQUkxQixTQUFTQSxVQUFVLFdBQVc7d0JBQ2hDLCtEQUErRDt3QkFDL0RaLFlBQVlZO29CQUNkLE9BQU87d0JBQ0wsK0JBQStCO3dCQUMvQlo7b0JBQ0Y7Z0JBQ0Y7O1lBRUFtQyxvQkFBb0JJLFdBQVcsQ0FBQ0Y7WUFFaEM7NkNBQU87b0JBQ0xoQyxRQUFRQyxHQUFHLENBQUM7b0JBQ1o2QixvQkFBb0JLLGNBQWMsQ0FBQ0g7b0JBQ25DeEMscUJBQXFCTyxPQUFPLEdBQUc7Z0JBQ2pDOztRQUNGO29DQUFHO1FBQUNKO0tBQVk7SUFFaEIsK0NBQStDO0lBQy9DcEUsZ0RBQVNBO3FDQUFDO1lBQ1IsSUFBSWtFLHNCQUFzQk0sT0FBTyxFQUFFO2dCQUNqQztZQUNGO1lBRUFOLHNCQUFzQk0sT0FBTyxHQUFHO1lBQ2hDQyxRQUFRQyxHQUFHLENBQUM7WUFFWixNQUFNbUMsZ0JBQWdCeEcscUVBQW9CQSxDQUFDbUcsV0FBVztZQUN0REssY0FBY0MscUJBQXFCO1lBRW5DOzZDQUFPO29CQUNMckMsUUFBUUMsR0FBRyxDQUFDO29CQUNabUMsY0FBY0Usb0JBQW9CO29CQUNsQzdDLHNCQUFzQk0sT0FBTyxHQUFHO2dCQUNsQzs7UUFDRjtvQ0FBRyxFQUFFO0lBRUwsZ0NBQWdDO0lBQ2hDeEUsZ0RBQVNBO3FDQUFDO1lBQ1JtRSxhQUFhSyxPQUFPLEdBQUc7WUFDdkJKO1lBRUE7NkNBQU87b0JBQ0xELGFBQWFLLE9BQU8sR0FBRztnQkFDekI7O1FBQ0Y7b0NBQUcsRUFBRSxFQUFFLHlCQUF5Qjs7SUFFaEMsNkNBQTZDO0lBQzdDLE1BQU13QyxzQkFBc0IvRyxrREFBV0E7NERBQ3JDLE9BQU9nQjtZQUNMLElBQUk7Z0JBQ0YsTUFBTWdHLFVBQVVsRSxRQUFRbUUsSUFBSTtnRkFBQyxDQUFDQyxJQUFNQSxFQUFFbEcsRUFBRSxLQUFLQTs7Z0JBQzdDLElBQUksQ0FBQ2dHLFNBQVM7b0JBQ1osT0FBTztnQkFDVDtnQkFFQSxJQUFJRztnQkFDSixJQUFJQztnQkFFSixJQUFJSixRQUFRcEcsTUFBTSxLQUFLLFFBQVE7b0JBQzdCLE1BQU1NLFVBQVUsSUFBSVA7b0JBQ3BCLE1BQU1TLFFBQVEsSUFBSVQsS0FBS08sUUFBUUosT0FBTyxLQUFLLEtBQUssS0FBSztvQkFDckQsTUFBTXVHLGdCQUFnQi9HLDhFQUFlQTtvQkFFckM2RyxtQkFBbUI7d0JBQ2pCLEdBQUdILE9BQU87d0JBQ1ZwRyxRQUFRO3dCQUNSTTt3QkFDQUU7d0JBQ0FYLFlBQVlNO3dCQUNaTSxXQUFXLElBQUlWO3dCQUNmWSxpQkFBaUI4Rjt3QkFDakI1Riw0QkFBNEI0RjtvQkFDOUI7b0JBRUFELGFBQWE7d0JBQ1h4RyxRQUFRO3dCQUNSTyxVQUFVRCxRQUFRMEUsV0FBVzt3QkFDN0IvRSxRQUFRTyxNQUFNd0UsV0FBVzt3QkFDekJwRSxvQkFBb0I2Rjt3QkFDcEIzRiwrQkFBK0IyRjtvQkFDakM7b0JBRUEsSUFBSXpELG1CQUFtQjt3QkFDckJ3RCxXQUFXMUcsWUFBWSxHQUFHO29CQUM1QjtnQkFDRixPQUFPO29CQUNMeUcsbUJBQW1CO3dCQUNqQixHQUFHSCxPQUFPO3dCQUNWcEcsUUFBUTt3QkFDUk0sU0FBU0g7d0JBQ1RLLE9BQU9MO3dCQUNQTixZQUFZTTt3QkFDWk0sV0FBVyxJQUFJVjt3QkFDZlksaUJBQWlCUjt3QkFDakJVLDRCQUE0QlY7b0JBQzlCO29CQUVBcUcsYUFBYTt3QkFDWHhHLFFBQVE7d0JBQ1JPLFVBQVU7d0JBQ1ZOLFFBQVE7d0JBQ1JXLG9CQUFvQjt3QkFDcEJFLCtCQUErQjtvQkFDakM7b0JBRUEsSUFBSWtDLG1CQUFtQjt3QkFDckJ3RCxXQUFXMUcsWUFBWSxHQUFHO29CQUM1QjtnQkFDRjtnQkFFQSwwQkFBMEI7Z0JBQzFCcUM7d0VBQVcsQ0FBQ3VFLE9BQVNBLEtBQUtqQyxHQUFHO2dGQUFDLENBQUM2QixJQUFPQSxFQUFFbEcsRUFBRSxLQUFLQSxLQUFLbUcsbUJBQW1CRDs7O2dCQUV2RSwwQkFBMEI7Z0JBQzFCLE1BQU0sRUFBRXhELEtBQUssRUFBRSxHQUFHLE1BQU14RCx1REFBUUEsQ0FBQytFLElBQUksQ0FBQyxZQUFZc0MsTUFBTSxDQUFDSCxZQUFZSSxFQUFFLENBQUMsTUFBTXhHO2dCQUU5RSxJQUFJMEMsT0FBTztvQkFDVCxJQUFJQSxNQUFNb0IsT0FBTyxJQUFJcEIsTUFBTW9CLE9BQU8sQ0FBQzJDLFFBQVEsQ0FBQyxpQkFBaUI7d0JBQzNENUQscUJBQXFCO3dCQUNyQixPQUFPdUQsV0FBVzFHLFlBQVk7d0JBQzlCLE1BQU0sRUFBRWdELE9BQU9nRSxVQUFVLEVBQUUsR0FBRyxNQUFNeEgsdURBQVFBLENBQUMrRSxJQUFJLENBQUMsWUFBWXNDLE1BQU0sQ0FBQ0gsWUFBWUksRUFBRSxDQUFDLE1BQU14Rzt3QkFDMUYsSUFBSTBHLFlBQVk7NEJBQ2QzRTtvRkFBVyxDQUFDdUUsT0FBU0EsS0FBS2pDLEdBQUc7NEZBQUMsQ0FBQzZCLElBQU9BLEVBQUVsRyxFQUFFLEtBQUtBLEtBQUtnRyxVQUFVRTs7OzRCQUM5RCxNQUFNUTt3QkFDUjtvQkFDRixPQUFPO3dCQUNMM0U7Z0ZBQVcsQ0FBQ3VFLE9BQVNBLEtBQUtqQyxHQUFHO3dGQUFDLENBQUM2QixJQUFPQSxFQUFFbEcsRUFBRSxLQUFLQSxLQUFLZ0csVUFBVUU7Ozt3QkFDOUQsTUFBTXhEO29CQUNSO2dCQUNGO2dCQUVBLE9BQU87WUFDVCxFQUFFLE9BQU9rQixLQUFLO2dCQUNaakIsU0FBU2lCLGVBQWVDLFFBQVFELElBQUlFLE9BQU8sR0FBRztnQkFDOUMsT0FBTztZQUNUO1FBQ0Y7MkRBQ0E7UUFBQ2hDO1FBQVNjO0tBQWtCO0lBRzlCLE1BQU0rRCxpQkFBaUIzSCxrREFBV0E7dURBQ2hDLE9BQU9nQixJQUFZNEc7WUFDakIsSUFBSTtnQkFDRixNQUFNWixVQUFVbEUsUUFBUW1FLElBQUk7MkVBQUMsQ0FBQ0MsSUFBTUEsRUFBRWxHLEVBQUUsS0FBS0E7O2dCQUM3QyxJQUFJLENBQUNnRyxTQUFTO29CQUNaLE9BQU87Z0JBQ1Q7Z0JBRUEsTUFBTWEsa0JBQWtCeEgsb0VBQWFBLENBQUN1SDtnQkFDdEMsTUFBTTFHLFVBQVUsSUFBSVA7Z0JBQ3BCLE1BQU1TLFFBQVEsSUFBSVQsS0FBS08sUUFBUUosT0FBTyxLQUFLK0csa0JBQWtCO2dCQUM3RCxNQUFNUixnQkFBZ0IvRyw4RUFBZUE7Z0JBRXJDLE1BQU02RyxtQkFBbUI7b0JBQ3ZCLEdBQUdILE9BQU87b0JBQ1ZwRyxRQUFRO29CQUNSTTtvQkFDQUU7b0JBQ0FYLFlBQVlNO29CQUNaTSxXQUFXLElBQUlWO29CQUNmWSxpQkFBaUI4RjtvQkFDakI1Riw0QkFBNEI0RjtnQkFDOUI7Z0JBRUF0RTttRUFBVyxDQUFDdUUsT0FBU0EsS0FBS2pDLEdBQUc7MkVBQUMsQ0FBQzZCLElBQU9BLEVBQUVsRyxFQUFFLEtBQUtBLEtBQUttRyxtQkFBbUJEOzs7Z0JBRXZFLE1BQU1FLGFBQWtCO29CQUN0QnhHLFFBQVE7b0JBQ1JPLFVBQVVELFFBQVEwRSxXQUFXO29CQUM3Qi9FLFFBQVFPLE1BQU13RSxXQUFXO29CQUN6QnBFLG9CQUFvQjZGO29CQUNwQjNGLCtCQUErQjJGO2dCQUNqQztnQkFFQSxJQUFJekQsbUJBQW1CO29CQUNyQndELFdBQVcxRyxZQUFZLEdBQUc7Z0JBQzVCO2dCQUVBLE1BQU0sRUFBRWdELEtBQUssRUFBRSxHQUFHLE1BQU14RCx1REFBUUEsQ0FBQytFLElBQUksQ0FBQyxZQUFZc0MsTUFBTSxDQUFDSCxZQUFZSSxFQUFFLENBQUMsTUFBTXhHO2dCQUU5RSxJQUFJMEMsT0FBTztvQkFDVCxJQUFJQSxNQUFNb0IsT0FBTyxJQUFJcEIsTUFBTW9CLE9BQU8sQ0FBQzJDLFFBQVEsQ0FBQyxpQkFBaUI7d0JBQzNENUQscUJBQXFCO3dCQUNyQixPQUFPdUQsV0FBVzFHLFlBQVk7d0JBQzlCLE1BQU0sRUFBRWdELE9BQU9nRSxVQUFVLEVBQUUsR0FBRyxNQUFNeEgsdURBQVFBLENBQUMrRSxJQUFJLENBQUMsWUFBWXNDLE1BQU0sQ0FBQ0gsWUFBWUksRUFBRSxDQUFDLE1BQU14Rzt3QkFDMUYsSUFBSTBHLFlBQVk7NEJBQ2QzRTsrRUFBVyxDQUFDdUUsT0FBU0EsS0FBS2pDLEdBQUc7dUZBQUMsQ0FBQzZCLElBQU9BLEVBQUVsRyxFQUFFLEtBQUtBLEtBQUtnRyxVQUFVRTs7OzRCQUM5RCxNQUFNUTt3QkFDUjtvQkFDRixPQUFPO3dCQUNMM0U7MkVBQVcsQ0FBQ3VFLE9BQVNBLEtBQUtqQyxHQUFHO21GQUFDLENBQUM2QixJQUFPQSxFQUFFbEcsRUFBRSxLQUFLQSxLQUFLZ0csVUFBVUU7Ozt3QkFDOUQsTUFBTXhEO29CQUNSO2dCQUNGO2dCQUVBLE9BQU87WUFDVCxFQUFFLE9BQU9rQixLQUFLO2dCQUNaakIsU0FBU2lCLGVBQWVDLFFBQVFELElBQUlFLE9BQU8sR0FBRztnQkFDOUMsT0FBTztZQUNUO1FBQ0Y7c0RBQ0E7UUFBQ2hDO1FBQVNjO0tBQWtCO0lBRzlCLHNFQUFzRTtJQUN0RSxNQUFNa0Usb0JBQW9COUgsa0RBQVdBOzBEQUNuQyxPQUFPZ0IsSUFBWStHO1lBQ2pCLElBQUk7Z0JBQ0YsTUFBTWYsVUFBVWxFLFFBQVFtRSxJQUFJOzhFQUFDLENBQUNDLElBQU1BLEVBQUVsRyxFQUFFLEtBQUtBOztnQkFDN0MsSUFBSSxDQUFDZ0csU0FBUztvQkFDWixPQUFPO3dCQUFFZ0IsU0FBUzt3QkFBT3RFLE9BQU87b0JBQW9CO2dCQUN0RDtnQkFFQSxNQUFNMkQsZ0JBQWdCL0csOEVBQWVBO2dCQUVyQyx3RUFBd0U7Z0JBQ3hFLElBQUkwRyxRQUFRekYsZUFBZSxLQUFLOEYsZUFBZTtvQkFDN0MsT0FBTzt3QkFBRVcsU0FBUzt3QkFBT3RFLE9BQU87b0JBQTJDO2dCQUM3RTtnQkFFQSxxREFBcUQ7Z0JBQ3JELElBQUlxRSxhQUFhLEtBQUtBLGFBQWEsS0FBSztvQkFDdEMsT0FBTzt3QkFBRUMsU0FBUzt3QkFBT3RFLE9BQU87b0JBQThDO2dCQUNoRjtnQkFFQSx5QkFBeUI7Z0JBQ3pCLE1BQU1ZLE1BQU0sSUFBSTNEO2dCQUNoQixNQUFNc0gsV0FBVyxJQUFJdEgsS0FBSzJELElBQUl4RCxPQUFPLEtBQUtpSCxhQUFhLEtBQUs7Z0JBRTVELE1BQU1aLG1CQUFtQjtvQkFDdkIsR0FBR0gsT0FBTztvQkFDVjVGLE9BQU82RztvQkFDUDVHLFdBQVcsSUFBSVY7Z0JBQ2pCO2dCQUVBLDBCQUEwQjtnQkFDMUJvQztzRUFBVyxDQUFDdUUsT0FBU0EsS0FBS2pDLEdBQUc7OEVBQUMsQ0FBQzZCLElBQU9BLEVBQUVsRyxFQUFFLEtBQUtBLEtBQUttRyxtQkFBbUJEOzs7Z0JBRXZFLDBCQUEwQjtnQkFDMUIsTUFBTSxFQUFFeEQsS0FBSyxFQUFFLEdBQUcsTUFBTXhELHVEQUFRQSxDQUM3QitFLElBQUksQ0FBQyxZQUNMc0MsTUFBTSxDQUFDO29CQUFFMUcsUUFBUW9ILFNBQVNyQyxXQUFXO2dCQUFHLEdBQ3hDNEIsRUFBRSxDQUFDLE1BQU14RztnQkFFWixJQUFJMEMsT0FBTztvQkFDVCxvQ0FBb0M7b0JBQ3BDWDswRUFBVyxDQUFDdUUsT0FBU0EsS0FBS2pDLEdBQUc7a0ZBQUMsQ0FBQzZCLElBQU9BLEVBQUVsRyxFQUFFLEtBQUtBLEtBQUtnRyxVQUFVRTs7O29CQUM5RCxPQUFPO3dCQUFFYyxTQUFTO3dCQUFPdEUsT0FBTztvQkFBNEM7Z0JBQzlFO2dCQUVBLE9BQU87b0JBQUVzRSxTQUFTO29CQUFNdEUsT0FBTztnQkFBSztZQUN0QyxFQUFFLE9BQU9rQixLQUFLO2dCQUNaLE9BQU87b0JBQ0xvRCxTQUFTO29CQUNUdEUsT0FBT2tCLGVBQWVDLFFBQVFELElBQUlFLE9BQU8sR0FBRztnQkFDOUM7WUFDRjtRQUNGO3lEQUNBO1FBQUNoQztLQUFRO0lBR1gsNkJBQTZCO0lBQzdCLE1BQU1vRiwwQkFBMEJsSSxrREFBV0E7Z0VBQUMsT0FBTzRCLE1BQWNFO1lBQy9ELElBQUk7Z0JBQ0YsTUFBTSxFQUFFNEIsS0FBSyxFQUFFLEdBQUcsTUFBTXhELHVEQUFRQSxDQUFDK0UsSUFBSSxDQUFDLGlCQUFpQmtELE1BQU0sQ0FBQztvQkFDNURuSCxJQUFJTCxLQUFLMkQsR0FBRyxHQUFHOEQsUUFBUTtvQkFDdkJ2RyxXQUFXRDtvQkFDWEUsYUFBYUE7Z0JBQ2Y7Z0JBRUEsSUFBSTRCLE9BQU8sTUFBTUE7Z0JBQ2pCLE9BQU87WUFDVCxFQUFFLE9BQU9rQixLQUFLO2dCQUNaakIsU0FBU2lCLGVBQWVDLFFBQVFELElBQUlFLE9BQU8sR0FBRztnQkFDOUMsT0FBTztZQUNUO1FBQ0Y7K0RBQUcsRUFBRTtJQUVMLE1BQU11RCxjQUFjckksa0RBQVdBO29EQUFDLE9BQU9nQjtZQUNyQyxJQUFJO2dCQUNGLE1BQU0sRUFBRTBDLEtBQUssRUFBRSxHQUFHLE1BQU14RCx1REFBUUEsQ0FBQytFLElBQUksQ0FBQyxpQkFBaUJxRCxNQUFNLEdBQUdkLEVBQUUsQ0FBQyxNQUFNeEc7Z0JBQ3pFLElBQUkwQyxPQUFPLE1BQU1BO2dCQUNqQixPQUFPO1lBQ1QsRUFBRSxPQUFPa0IsS0FBSztnQkFDWmpCLFNBQVNpQixlQUFlQyxRQUFRRCxJQUFJRSxPQUFPLEdBQUc7Z0JBQzlDLE9BQU87WUFDVDtRQUNGO21EQUFHLEVBQUU7SUFFTCxNQUFNeUQsa0JBQWtCdkksa0RBQVdBO3dEQUFDLE9BQU80QixNQUFjTyxPQUFlTCxhQUFxQk07WUFDM0YsSUFBSTtnQkFDRixNQUFNLEVBQUVzQixLQUFLLEVBQUUsR0FBRyxNQUFNeEQsdURBQVFBLENBQUMrRSxJQUFJLENBQUMsaUJBQWlCa0QsTUFBTSxDQUFDO29CQUM1RG5ILElBQUlMLEtBQUsyRCxHQUFHLEdBQUc4RCxRQUFRO29CQUN2QnZHLFdBQVdEO29CQUNYTyxPQUFPQTtvQkFDUEwsYUFBYUE7b0JBQ2JPLG1CQUFtQkQ7Z0JBQ3JCO2dCQUVBLElBQUlzQixPQUFPLE1BQU1BO2dCQUNqQixPQUFPO1lBQ1QsRUFBRSxPQUFPa0IsS0FBSztnQkFDWmpCLFNBQVNpQixlQUFlQyxRQUFRRCxJQUFJRSxPQUFPLEdBQUc7Z0JBQzlDLE9BQU87WUFDVDtRQUNGO3VEQUFHLEVBQUU7SUFFTCxNQUFNMEQscUJBQXFCeEksa0RBQVdBOzJEQUFDLE9BQU9nQjtZQUM1QyxJQUFJO2dCQUNGLE1BQU0sRUFBRTBDLEtBQUssRUFBRSxHQUFHLE1BQU14RCx1REFBUUEsQ0FBQytFLElBQUksQ0FBQyxpQkFBaUJxRCxNQUFNLEdBQUdkLEVBQUUsQ0FBQyxNQUFNeEc7Z0JBQ3pFLElBQUkwQyxPQUFPLE1BQU1BO2dCQUNqQixPQUFPO1lBQ1QsRUFBRSxPQUFPa0IsS0FBSztnQkFDWmpCLFNBQVNpQixlQUFlQyxRQUFRRCxJQUFJRSxPQUFPLEdBQUc7Z0JBQzlDLE9BQU87WUFDVDtRQUNGOzBEQUFHLEVBQUU7SUFFTCxNQUFNMkQsaUJBQWlCekksa0RBQVdBO3VEQUFDLE9BQU80QixNQUFjRTtZQUN0RCxJQUFJO2dCQUNGLE1BQU0sRUFBRTRCLEtBQUssRUFBRSxHQUFHLE1BQU14RCx1REFBUUEsQ0FBQytFLElBQUksQ0FBQyxpQkFBaUJrRCxNQUFNLENBQUM7b0JBQzVEbkgsSUFBSUwsS0FBSzJELEdBQUcsR0FBRzhELFFBQVE7b0JBQ3ZCdkcsV0FBV0Q7b0JBQ1hFO2dCQUNGO2dCQUVBLElBQUk0QixPQUFPLE1BQU1BO2dCQUNqQixPQUFPO1lBQ1QsRUFBRSxPQUFPa0IsS0FBSztnQkFDWmpCLFNBQVNpQixlQUFlQyxRQUFRRCxJQUFJRSxPQUFPLEdBQUc7Z0JBQzlDLE9BQU87WUFDVDtRQUNGO3NEQUFHLEVBQUU7SUFFTCxNQUFNNEQsb0JBQW9CMUksa0RBQVdBOzBEQUFDLE9BQU9nQjtZQUMzQyxJQUFJO2dCQUNGLE1BQU0sRUFBRTBDLEtBQUssRUFBRSxHQUFHLE1BQU14RCx1REFBUUEsQ0FBQytFLElBQUksQ0FBQyxpQkFBaUJxRCxNQUFNLEdBQUdkLEVBQUUsQ0FBQyxNQUFNeEc7Z0JBQ3pFLElBQUkwQyxPQUFPLE1BQU1BO2dCQUNqQixPQUFPO1lBQ1QsRUFBRSxPQUFPa0IsS0FBSztnQkFDWmpCLFNBQVNpQixlQUFlQyxRQUFRRCxJQUFJRSxPQUFPLEdBQUc7Z0JBQzlDLE9BQU87WUFDVDtRQUNGO3lEQUFHLEVBQUU7SUFFTCxNQUFNNkQsaUJBQWlCM0ksa0RBQVdBO3VEQUFDLE9BQU9nQjtZQUN4QyxJQUFJO2dCQUNGLE1BQU0sRUFBRTBDLEtBQUssRUFBRSxHQUFHLE1BQU14RCx1REFBUUEsQ0FBQytFLElBQUksQ0FBQyxhQUFhcUQsTUFBTSxHQUFHZCxFQUFFLENBQUMsTUFBTXhHO2dCQUNyRSxJQUFJMEMsT0FBTyxNQUFNQTtnQkFDakIsT0FBTztZQUNULEVBQUUsT0FBT2tCLEtBQUs7Z0JBQ1pqQixTQUFTaUIsZUFBZUMsUUFBUUQsSUFBSUUsT0FBTyxHQUFHO2dCQUM5QyxPQUFPO1lBQ1Q7UUFDRjtzREFBRyxFQUFFO0lBRUwsT0FBTztRQUNMLE9BQU87UUFDUGhDO1FBQ0FFO1FBQ0FFO1FBQ0FFO1FBQ0FFO1FBRUEsUUFBUTtRQUNSRTtRQUNBRTtRQUVBLGFBQWE7UUFDYnFEO1FBQ0FZO1FBQ0FHO1FBQ0FJO1FBQ0FHO1FBQ0FFO1FBQ0FDO1FBQ0FDO1FBQ0FDO1FBQ0FDO1FBQ0FDLGFBQWEsSUFBTXpFLFlBQVlwRCxXQUFXO0lBQzVDO0FBQ0Y7R0FqaUJ3QjhCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG1lbWV0XFxEZXNrdG9wXFxkb3JtXzIxXFxzcmNcXGhvb2tzXFx1c2VTdXBhYmFzZURhdGEudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXHJcblxyXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VDYWxsYmFjaywgdXNlUmVmIH0gZnJvbSBcInJlYWN0XCJcclxuaW1wb3J0IHsgc3VwYWJhc2UgfSBmcm9tIFwiQC9zcmMvbGliL3N1cGFiYXNlXCJcclxuaW1wb3J0IHR5cGUgeyBEYXRhYmFzZSB9IGZyb20gXCJAL3NyYy9saWIvc3VwYWJhc2VcIlxyXG5pbXBvcnQgU3Vic2NyaXB0aW9uTWFuYWdlciBmcm9tIFwiQC9zcmMvbGliL3N1YnNjcmlwdGlvbk1hbmFnZXJcIlxyXG5pbXBvcnQgTWFjaGluZVN0YXR1c01hbmFnZXIgZnJvbSBcIkAvc3JjL2xpYi9tYWNoaW5lU3RhdHVzTWFuYWdlclwiXHJcbmltcG9ydCBwYXJzZUR1cmF0aW9uIGZyb20gXCJAL3NyYy91dGlscy9wYXJzZUR1cmF0aW9uXCJcclxuaW1wb3J0IHsgZ2V0RGV2aWNlVXNlcklkIH0gZnJvbSBcIkAvc3JjL3V0aWxzL3VzZXJJZGVudGlmaWNhdGlvblwiXHJcblxyXG4vLyBUeXBlIGRlZmluaXRpb25zXHJcbmV4cG9ydCBpbnRlcmZhY2UgTWFjaGluZSB7XHJcbiAgaWQ6IHN0cmluZ1xyXG4gIG5hbWU6IHN0cmluZ1xyXG4gIHN0YXR1czogXCJmcmVlXCIgfCBcInJ1bm5pbmdcIiB8IFwiZmluaXNoZWRHcmFjZVwiXHJcbiAgc3RhcnRBdD86IERhdGVcclxuICBlbmRBdD86IERhdGVcclxuICBncmFjZUVuZEF0PzogRGF0ZVxyXG4gIHVwZGF0ZWRBdDogRGF0ZVxyXG4gIHN0YXJ0ZWRCeVVzZXJJZD86IHN0cmluZ1xyXG4gIHN0YXJ0ZWRCeURldmljZUZpbmdlcnByaW50Pzogc3RyaW5nXHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgTm9pc2VFbnRyeSB7XHJcbiAgaWQ6IHN0cmluZ1xyXG4gIHVzZXI6IHN0cmluZ1xyXG4gIGRlc2NyaXB0aW9uOiBzdHJpbmdcclxuICB0aW1lc3RhbXA6IERhdGVcclxuICBsYXN0UmVwb3J0ZWQ6IERhdGVcclxufVxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBBbm5vdW5jZW1lbnRFbnRyeSB7XHJcbiAgaWQ6IHN0cmluZ1xyXG4gIHVzZXI6IHN0cmluZ1xyXG4gIHRpdGxlOiBzdHJpbmdcclxuICBkZXNjcmlwdGlvbjogc3RyaW5nXHJcbiAgdHlwZTogc3RyaW5nXHJcbiAgdGltZXN0YW1wOiBEYXRlXHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgU3VibGV0RW50cnkge1xyXG4gIGlkOiBzdHJpbmdcclxuICB1c2VyOiBzdHJpbmdcclxuICBkdXJhdGlvbjogc3RyaW5nXHJcbiAgdGltZXN0YW1wOiBEYXRlXHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgSGVscE1lRW50cnkge1xyXG4gIGlkOiBzdHJpbmdcclxuICB1c2VyOiBzdHJpbmdcclxuICBkZXNjcmlwdGlvbjogc3RyaW5nXHJcbiAgdGltZXN0YW1wOiBEYXRlXHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgSW5jaWRlbnQge1xyXG4gIGlkOiBzdHJpbmdcclxuICBtYWNoaW5lSWQ6IHN0cmluZ1xyXG4gIHRpbWVzdGFtcDogRGF0ZVxyXG4gIHR5cGU6IHN0cmluZ1xyXG59XHJcblxyXG4vLyBIZWxwZXIgZnVuY3Rpb25zIHRvIGNvbnZlcnQgYmV0d2VlbiBEQiBhbmQgYXBwIGZvcm1hdHNcclxuY29uc3QgZGJUb01hY2hpbmUgPSAocm93OiBEYXRhYmFzZVtcInB1YmxpY1wiXVtcIlRhYmxlc1wiXVtcIm1hY2hpbmVzXCJdW1wiUm93XCJdKTogTWFjaGluZSA9PiB7XHJcbiAgY29uc3QgZ3JhY2VFbmRBdCA9IHJvdy5ncmFjZV9lbmRfYXRcclxuICAgID8gbmV3IERhdGUocm93LmdyYWNlX2VuZF9hdClcclxuICAgIDogcm93LnN0YXR1cyA9PT0gXCJmaW5pc2hlZEdyYWNlXCIgJiYgcm93LmVuZF9hdFxyXG4gICAgICA/IG5ldyBEYXRlKG5ldyBEYXRlKHJvdy5lbmRfYXQpLmdldFRpbWUoKSArIDUgKiA2MCAqIDEwMDApXHJcbiAgICAgIDogdW5kZWZpbmVkXHJcblxyXG4gIHJldHVybiB7XHJcbiAgICBpZDogcm93LmlkLFxyXG4gICAgbmFtZTogcm93Lm5hbWUsXHJcbiAgICBzdGF0dXM6IHJvdy5zdGF0dXMsXHJcbiAgICBzdGFydEF0OiByb3cuc3RhcnRfYXQgPyBuZXcgRGF0ZShyb3cuc3RhcnRfYXQpIDogdW5kZWZpbmVkLFxyXG4gICAgZW5kQXQ6IHJvdy5lbmRfYXQgPyBuZXcgRGF0ZShyb3cuZW5kX2F0KSA6IHVuZGVmaW5lZCxcclxuICAgIGdyYWNlRW5kQXQ6IGdyYWNlRW5kQXQsXHJcbiAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKHJvdy51cGRhdGVkX2F0KSxcclxuICAgIHN0YXJ0ZWRCeVVzZXJJZDogKHJvdyBhcyBhbnkpLnN0YXJ0ZWRfYnlfdXNlcl9pZCB8fCB1bmRlZmluZWQsXHJcbiAgICBzdGFydGVkQnlEZXZpY2VGaW5nZXJwcmludDogKHJvdyBhcyBhbnkpLnN0YXJ0ZWRfYnlfZGV2aWNlX2ZpbmdlcnByaW50IHx8IHVuZGVmaW5lZCxcclxuICB9XHJcbn1cclxuXHJcbmNvbnN0IGRiVG9Ob2lzZSA9IChyb3c6IGFueSk6IE5vaXNlRW50cnkgPT4gKHtcclxuICBpZDogcm93LmlkLFxyXG4gIHVzZXI6IHJvdy51c2VyX25hbWUsXHJcbiAgZGVzY3JpcHRpb246IHJvdy5kZXNjcmlwdGlvbiB8fCBcIk5vaXNlIHJlcG9ydGVkXCIsXHJcbiAgdGltZXN0YW1wOiBuZXcgRGF0ZShyb3cudGltZXN0YW1wKSxcclxuICBsYXN0UmVwb3J0ZWQ6IG5ldyBEYXRlKHJvdy5sYXN0X3JlcG9ydGVkKSxcclxufSlcclxuXHJcbmNvbnN0IGRiVG9Bbm5vdW5jZW1lbnQgPSAocm93OiBhbnkpOiBBbm5vdW5jZW1lbnRFbnRyeSA9PiAoe1xyXG4gIGlkOiByb3cuaWQsXHJcbiAgdXNlcjogcm93LnVzZXJfbmFtZSxcclxuICB0aXRsZTogcm93LnRpdGxlLFxyXG4gIGRlc2NyaXB0aW9uOiByb3cuZGVzY3JpcHRpb24sXHJcbiAgdHlwZTogcm93LmFubm91bmNlbWVudF90eXBlLFxyXG4gIHRpbWVzdGFtcDogbmV3IERhdGUocm93LnRpbWVzdGFtcCksXHJcbn0pXHJcblxyXG5jb25zdCBkYlRvU3VibGV0ID0gKHJvdzogRGF0YWJhc2VbXCJwdWJsaWNcIl1bXCJUYWJsZXNcIl1bXCJzdWJsZXRzXCJdW1wiUm93XCJdKTogU3VibGV0RW50cnkgPT4gKHtcclxuICBpZDogcm93LmlkLFxyXG4gIHVzZXI6IHJvdy51c2VyX25hbWUsXHJcbiAgZHVyYXRpb246IHJvdy5kdXJhdGlvbixcclxuICB0aW1lc3RhbXA6IG5ldyBEYXRlKHJvdy50aW1lc3RhbXApLFxyXG59KVxyXG5cclxuY29uc3QgZGJUb0hlbHBNZSA9IChyb3c6IERhdGFiYXNlW1wicHVibGljXCJdW1wiVGFibGVzXCJdW1wiaGVscF9yZXF1ZXN0c1wiXVtcIlJvd1wiXSk6IEhlbHBNZUVudHJ5ID0+ICh7XHJcbiAgaWQ6IHJvdy5pZCxcclxuICB1c2VyOiByb3cudXNlcl9uYW1lLFxyXG4gIGRlc2NyaXB0aW9uOiByb3cuZGVzY3JpcHRpb24sXHJcbiAgdGltZXN0YW1wOiBuZXcgRGF0ZShyb3cudGltZXN0YW1wKSxcclxufSlcclxuXHJcbmNvbnN0IGRiVG9JbmNpZGVudCA9IChyb3c6IERhdGFiYXNlW1wicHVibGljXCJdW1wiVGFibGVzXCJdW1wiaW5jaWRlbnRzXCJdW1wiUm93XCJdKTogSW5jaWRlbnQgPT4gKHtcclxuICBpZDogcm93LmlkLFxyXG4gIG1hY2hpbmVJZDogcm93Lm1hY2hpbmVfaWQsXHJcbiAgdGltZXN0YW1wOiBuZXcgRGF0ZShyb3cudGltZXN0YW1wKSxcclxuICB0eXBlOiByb3cuaW5jaWRlbnRfdHlwZSxcclxufSlcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHVzZVN1cGFiYXNlRGF0YSgpIHtcclxuICBjb25zdCBbbGF1bmRyeSwgc2V0TGF1bmRyeV0gPSB1c2VTdGF0ZTxNYWNoaW5lW10+KFtdKVxyXG4gIGNvbnN0IFtub2lzZSwgc2V0Tm9pc2VdID0gdXNlU3RhdGU8Tm9pc2VFbnRyeVtdPihbXSlcclxuICBjb25zdCBbYW5ub3VuY2VtZW50cywgc2V0QW5ub3VuY2VtZW50c10gPSB1c2VTdGF0ZTxBbm5vdW5jZW1lbnRFbnRyeVtdPihbXSlcclxuICBjb25zdCBbaGVscE1lLCBzZXRIZWxwTWVdID0gdXNlU3RhdGU8SGVscE1lRW50cnlbXT4oW10pXHJcbiAgY29uc3QgW2luY2lkZW50cywgc2V0SW5jaWRlbnRzXSA9IHVzZVN0YXRlPEluY2lkZW50W10+KFtdKVxyXG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKVxyXG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbClcclxuICBjb25zdCBbaGFzR3JhY2VFbmRDb2x1bW4sIHNldEhhc0dyYWNlRW5kQ29sdW1uXSA9IHVzZVN0YXRlKHRydWUpXHJcblxyXG4gIC8vIFJlZnMgdG8gcHJldmVudCByZWN1cnNpdmUgY2FsbHMgYW5kIG11bHRpcGxlIHNldHVwc1xyXG4gIGNvbnN0IGlzTG9hZGluZ0RhdGFSZWYgPSB1c2VSZWYoZmFsc2UpXHJcbiAgY29uc3QgbGFzdExvYWRUaW1lUmVmID0gdXNlUmVmPG51bWJlcj4oMClcclxuICBjb25zdCBzdWJzY3JpcHRpb25TZXR1cFJlZiA9IHVzZVJlZihmYWxzZSlcclxuICBjb25zdCBzdGF0dXNNYW5hZ2VyU2V0dXBSZWYgPSB1c2VSZWYoZmFsc2UpXHJcbiAgY29uc3QgaXNNb3VudGVkUmVmID0gdXNlUmVmKHRydWUpXHJcblxyXG4gIC8vIERlYm91bmNlZCBkYXRhIGxvYWRpbmcgZnVuY3Rpb24gd2l0aCBzZWxlY3RpdmUgbG9hZGluZ1xyXG4gIGNvbnN0IGxvYWRBbGxEYXRhID0gdXNlQ2FsbGJhY2soYXN5bmMgKHNwZWNpZmljVGFibGU/OiBzdHJpbmcsIGZvcmNlUmVmcmVzaCA9IGZhbHNlKSA9PiB7XHJcbiAgICBjb25zdCBub3cgPSBEYXRlLm5vdygpXHJcbiAgICAvLyBTa2lwIGRlYm91bmNlIGZvciBtYW51YWwgcmVmcmVzaCAoZm9yY2VSZWZyZXNoID0gdHJ1ZSlcclxuICAgIGlmICghZm9yY2VSZWZyZXNoICYmIChpc0xvYWRpbmdEYXRhUmVmLmN1cnJlbnQgfHwgbm93IC0gbGFzdExvYWRUaW1lUmVmLmN1cnJlbnQgPCAxMDAwKSkge1xyXG4gICAgICBjb25zb2xlLmxvZyhcIuKPre+4jyBTa2lwcGluZyBkYXRhIGxvYWQgKHRvbyByZWNlbnQgb3IgaW4gcHJvZ3Jlc3MpXCIpXHJcbiAgICAgIHJldHVyblxyXG4gICAgfVxyXG5cclxuICAgIGlmICghaXNNb3VudGVkUmVmLmN1cnJlbnQpIHtcclxuICAgICAgY29uc29sZS5sb2coXCLij63vuI8gU2tpcHBpbmcgZGF0YSBsb2FkIChjb21wb25lbnQgdW5tb3VudGVkKVwiKVxyXG4gICAgICByZXR1cm5cclxuICAgIH1cclxuXHJcbiAgICBpc0xvYWRpbmdEYXRhUmVmLmN1cnJlbnQgPSB0cnVlXHJcbiAgICBsYXN0TG9hZFRpbWVSZWYuY3VycmVudCA9IG5vd1xyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKGDwn5SEIExvYWRpbmcgZGF0YSBmcm9tIFN1cGFiYXNlJHtzcGVjaWZpY1RhYmxlID8gYCAoJHtzcGVjaWZpY1RhYmxlfSlgIDogXCJcIn0uLi5gKVxyXG4gICAgICBzZXRFcnJvcihudWxsKVxyXG5cclxuICAgICAgLy8gSWYgc3BlY2lmaWMgdGFibGUgaXMgcHJvdmlkZWQsIG9ubHkgbG9hZCB0aGF0IHRhYmxlXHJcbiAgICAgIGlmIChzcGVjaWZpY1RhYmxlKSB7XHJcbiAgICAgICAgYXdhaXQgbG9hZFNwZWNpZmljVGFibGUoc3BlY2lmaWNUYWJsZSlcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICAvLyBMb2FkIGFsbCBkYXRhXHJcbiAgICAgICAgc2V0SXNMb2FkaW5nKHRydWUpXHJcbiAgICAgICAgYXdhaXQgbG9hZEFsbFRhYmxlcygpXHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnNvbGUubG9nKFwi4pyFIERhdGEgbG9hZGVkIHN1Y2Nlc3NmdWxseVwiKVxyXG4gICAgfSBjYXRjaCAoZXJyKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCLinYwgRXJyb3IgbG9hZGluZyBkYXRhOlwiLCBlcnIpXHJcbiAgICAgIGlmIChpc01vdW50ZWRSZWYuY3VycmVudCkge1xyXG4gICAgICAgIHNldEVycm9yKGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiBcIkZhaWxlZCB0byBsb2FkIGRhdGFcIilcclxuICAgICAgfVxyXG4gICAgfSBmaW5hbGx5IHtcclxuICAgICAgaWYgKGlzTW91bnRlZFJlZi5jdXJyZW50ICYmICFzcGVjaWZpY1RhYmxlKSB7XHJcbiAgICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKVxyXG4gICAgICB9XHJcbiAgICAgIGlzTG9hZGluZ0RhdGFSZWYuY3VycmVudCA9IGZhbHNlXHJcbiAgICB9XHJcbiAgfSwgW10pXHJcblxyXG4gIC8vIEhlbHBlciBmdW5jdGlvbiB0byBsb2FkIHNwZWNpZmljIHRhYmxlXHJcbiAgY29uc3QgbG9hZFNwZWNpZmljVGFibGUgPSBhc3luYyAodGFibGU6IHN0cmluZykgPT4ge1xyXG4gICAgc3dpdGNoICh0YWJsZSkge1xyXG4gICAgICBjYXNlIFwibWFjaGluZXNcIjpcclxuICAgICAgICBjb25zdCBtYWNoaW5lc1Jlc3VsdCA9IGF3YWl0IHN1cGFiYXNlLmZyb20oXCJtYWNoaW5lc1wiKS5zZWxlY3QoXCIqXCIpLm9yZGVyKFwibmFtZVwiKVxyXG4gICAgICAgIGlmIChtYWNoaW5lc1Jlc3VsdC5lcnJvcikgdGhyb3cgbWFjaGluZXNSZXN1bHQuZXJyb3JcclxuICAgICAgICBzZXRMYXVuZHJ5KG1hY2hpbmVzUmVzdWx0LmRhdGE/Lm1hcChkYlRvTWFjaGluZSkgfHwgW10pXHJcbiAgICAgICAgYnJlYWtcclxuXHJcbiAgICAgIGNhc2UgXCJub2lzZV9yZXBvcnRzXCI6XHJcbiAgICAgICAgY29uc3Qgbm9pc2VSZXN1bHQgPSBhd2FpdCBzdXBhYmFzZVxyXG4gICAgICAgICAgLmZyb20oXCJub2lzZV9yZXBvcnRzXCIpXHJcbiAgICAgICAgICAuc2VsZWN0KFwiKlwiKVxyXG4gICAgICAgICAgLm9yZGVyKFwidGltZXN0YW1wXCIsIHsgYXNjZW5kaW5nOiBmYWxzZSB9KVxyXG4gICAgICAgICAgLmxpbWl0KDUwKVxyXG4gICAgICAgIGlmIChub2lzZVJlc3VsdC5lcnJvcikgdGhyb3cgbm9pc2VSZXN1bHQuZXJyb3JcclxuICAgICAgICBzZXROb2lzZShub2lzZVJlc3VsdC5kYXRhPy5tYXAoZGJUb05vaXNlKSB8fCBbXSlcclxuICAgICAgICBicmVha1xyXG5cclxuICAgICAgY2FzZSBcImFubm91bmNlbWVudHNcIjpcclxuICAgICAgICBjb25zdCBhbm5vdW5jZW1lbnRzUmVzdWx0ID0gYXdhaXQgc3VwYWJhc2VcclxuICAgICAgICAgIC5mcm9tKFwiYW5ub3VuY2VtZW50c1wiKVxyXG4gICAgICAgICAgLnNlbGVjdChcIipcIilcclxuICAgICAgICAgIC5vcmRlcihcInRpbWVzdGFtcFwiLCB7IGFzY2VuZGluZzogZmFsc2UgfSlcclxuICAgICAgICAgIC5saW1pdCg1MClcclxuICAgICAgICBpZiAoYW5ub3VuY2VtZW50c1Jlc3VsdC5lcnJvcikgdGhyb3cgYW5ub3VuY2VtZW50c1Jlc3VsdC5lcnJvclxyXG4gICAgICAgIHNldEFubm91bmNlbWVudHMoYW5ub3VuY2VtZW50c1Jlc3VsdC5kYXRhPy5tYXAoZGJUb0Fubm91bmNlbWVudCkgfHwgW10pXHJcbiAgICAgICAgYnJlYWtcclxuXHJcbiAgICAgIGNhc2UgXCJoZWxwX3JlcXVlc3RzXCI6XHJcbiAgICAgICAgY29uc3QgaGVscFJlc3VsdCA9IGF3YWl0IHN1cGFiYXNlXHJcbiAgICAgICAgICAuZnJvbShcImhlbHBfcmVxdWVzdHNcIilcclxuICAgICAgICAgIC5zZWxlY3QoXCIqXCIpXHJcbiAgICAgICAgICAuZ3RlKFwidGltZXN0YW1wXCIsIG5ldyBEYXRlKERhdGUubm93KCkgLSAyNCAqIDYwICogNjAgKiAxMDAwKS50b0lTT1N0cmluZygpKVxyXG4gICAgICAgICAgLm9yZGVyKFwidGltZXN0YW1wXCIsIHsgYXNjZW5kaW5nOiBmYWxzZSB9KVxyXG4gICAgICAgICAgLmxpbWl0KDUwKVxyXG4gICAgICAgIGlmIChoZWxwUmVzdWx0LmVycm9yKSB0aHJvdyBoZWxwUmVzdWx0LmVycm9yXHJcbiAgICAgICAgc2V0SGVscE1lKGhlbHBSZXN1bHQuZGF0YT8ubWFwKGRiVG9IZWxwTWUpIHx8IFtdKVxyXG4gICAgICAgIGJyZWFrXHJcblxyXG4gICAgICBjYXNlIFwiaW5jaWRlbnRzXCI6XHJcbiAgICAgICAgY29uc3QgaW5jaWRlbnRzUmVzdWx0ID0gYXdhaXQgc3VwYWJhc2VcclxuICAgICAgICAgIC5mcm9tKFwiaW5jaWRlbnRzXCIpXHJcbiAgICAgICAgICAuc2VsZWN0KFwiKlwiKVxyXG4gICAgICAgICAgLm9yZGVyKFwidGltZXN0YW1wXCIsIHsgYXNjZW5kaW5nOiBmYWxzZSB9KVxyXG4gICAgICAgICAgLmxpbWl0KDUwKVxyXG4gICAgICAgIGlmIChpbmNpZGVudHNSZXN1bHQuZXJyb3IpIHRocm93IGluY2lkZW50c1Jlc3VsdC5lcnJvclxyXG4gICAgICAgIHNldEluY2lkZW50cyhpbmNpZGVudHNSZXN1bHQuZGF0YT8ubWFwKGRiVG9JbmNpZGVudCkgfHwgW10pXHJcbiAgICAgICAgYnJlYWtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8vIEhlbHBlciBmdW5jdGlvbiB0byBsb2FkIGFsbCB0YWJsZXNcclxuICBjb25zdCBsb2FkQWxsVGFibGVzID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgY29uc3QgdGltZW91dFByb21pc2UgPSBuZXcgUHJvbWlzZSgoXywgcmVqZWN0KSA9PiBzZXRUaW1lb3V0KCgpID0+IHJlamVjdChuZXcgRXJyb3IoXCJSZXF1ZXN0IHRpbWVvdXRcIikpLCAxNTAwMCkpXHJcblxyXG4gICAgY29uc3QgZGF0YVByb21pc2UgPSBQcm9taXNlLmFsbChbXHJcbiAgICAgIHN1cGFiYXNlLmZyb20oXCJtYWNoaW5lc1wiKS5zZWxlY3QoXCIqXCIpLm9yZGVyKFwibmFtZVwiKSxcclxuICAgICAgc3VwYWJhc2UuZnJvbShcIm5vaXNlX3JlcG9ydHNcIikuc2VsZWN0KFwiKlwiKS5vcmRlcihcInRpbWVzdGFtcFwiLCB7IGFzY2VuZGluZzogZmFsc2UgfSkubGltaXQoNTApLFxyXG4gICAgICBzdXBhYmFzZS5mcm9tKFwiYW5ub3VuY2VtZW50c1wiKS5zZWxlY3QoXCIqXCIpLm9yZGVyKFwidGltZXN0YW1wXCIsIHsgYXNjZW5kaW5nOiBmYWxzZSB9KS5saW1pdCg1MCksXHJcbiAgICAgIHN1cGFiYXNlXHJcbiAgICAgICAgLmZyb20oXCJoZWxwX3JlcXVlc3RzXCIpXHJcbiAgICAgICAgLnNlbGVjdChcIipcIilcclxuICAgICAgICAuZ3RlKFwidGltZXN0YW1wXCIsIG5ldyBEYXRlKERhdGUubm93KCkgLSAyNCAqIDYwICogNjAgKiAxMDAwKS50b0lTT1N0cmluZygpKVxyXG4gICAgICAgIC5vcmRlcihcInRpbWVzdGFtcFwiLCB7IGFzY2VuZGluZzogZmFsc2UgfSlcclxuICAgICAgICAubGltaXQoNTApLFxyXG4gICAgICBzdXBhYmFzZS5mcm9tKFwiaW5jaWRlbnRzXCIpLnNlbGVjdChcIipcIikub3JkZXIoXCJ0aW1lc3RhbXBcIiwgeyBhc2NlbmRpbmc6IGZhbHNlIH0pLmxpbWl0KDUwKSxcclxuICAgIF0pXHJcblxyXG4gICAgY29uc3QgW21hY2hpbmVzUmVzdWx0LCBub2lzZVJlc3VsdCwgYW5ub3VuY2VtZW50c1Jlc3VsdCwgaGVscFJlc3VsdCwgaW5jaWRlbnRzUmVzdWx0XSA9IChhd2FpdCBQcm9taXNlLnJhY2UoW1xyXG4gICAgICBkYXRhUHJvbWlzZSxcclxuICAgICAgdGltZW91dFByb21pc2UsXHJcbiAgICBdKSkgYXMgYW55W11cclxuXHJcbiAgICBpZiAoIWlzTW91bnRlZFJlZi5jdXJyZW50KSByZXR1cm5cclxuXHJcbiAgICBpZiAobWFjaGluZXNSZXN1bHQuZXJyb3IpIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGxvYWQgbWFjaGluZXM6ICR7bWFjaGluZXNSZXN1bHQuZXJyb3IubWVzc2FnZX1gKVxyXG4gICAgaWYgKG5vaXNlUmVzdWx0LmVycm9yKSB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBsb2FkIG5vaXNlIHJlcG9ydHM6ICR7bm9pc2VSZXN1bHQuZXJyb3IubWVzc2FnZX1gKVxyXG4gICAgaWYgKGFubm91bmNlbWVudHNSZXN1bHQuZXJyb3IpIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGxvYWQgYW5ub3VuY2VtZW50czogJHthbm5vdW5jZW1lbnRzUmVzdWx0LmVycm9yLm1lc3NhZ2V9YClcclxuICAgIGlmIChoZWxwUmVzdWx0LmVycm9yKSB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBsb2FkIGhlbHAgcmVxdWVzdHM6ICR7aGVscFJlc3VsdC5lcnJvci5tZXNzYWdlfWApXHJcbiAgICBpZiAoaW5jaWRlbnRzUmVzdWx0LmVycm9yKSB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBsb2FkIGluY2lkZW50czogJHtpbmNpZGVudHNSZXN1bHQuZXJyb3IubWVzc2FnZX1gKVxyXG5cclxuICAgIHNldExhdW5kcnkobWFjaGluZXNSZXN1bHQuZGF0YT8ubWFwKGRiVG9NYWNoaW5lKSB8fCBbXSlcclxuICAgIHNldE5vaXNlKG5vaXNlUmVzdWx0LmRhdGE/Lm1hcChkYlRvTm9pc2UpIHx8IFtdKVxyXG4gICAgc2V0QW5ub3VuY2VtZW50cyhhbm5vdW5jZW1lbnRzUmVzdWx0LmRhdGE/Lm1hcChkYlRvQW5ub3VuY2VtZW50KSB8fCBbXSlcclxuICAgIHNldEhlbHBNZShoZWxwUmVzdWx0LmRhdGE/Lm1hcChkYlRvSGVscE1lKSB8fCBbXSlcclxuICAgIHNldEluY2lkZW50cyhpbmNpZGVudHNSZXN1bHQuZGF0YT8ubWFwKGRiVG9JbmNpZGVudCkgfHwgW10pXHJcbiAgfVxyXG5cclxuICAvLyBTZXQgdXAgcmVhbC10aW1lIHN1YnNjcmlwdGlvbnMgd2l0aCB0YWJsZS1zcGVjaWZpYyB1cGRhdGVzXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmIChzdWJzY3JpcHRpb25TZXR1cFJlZi5jdXJyZW50KSB7XHJcbiAgICAgIHJldHVyblxyXG4gICAgfVxyXG5cclxuICAgIHN1YnNjcmlwdGlvblNldHVwUmVmLmN1cnJlbnQgPSB0cnVlXHJcbiAgICBjb25zb2xlLmxvZyhcIvCflIQgU2V0dGluZyB1cCBzdWJzY3JpcHRpb24gbWFuYWdlci4uLlwiKVxyXG5cclxuICAgIGNvbnN0IHN1YnNjcmlwdGlvbk1hbmFnZXIgPSBTdWJzY3JpcHRpb25NYW5hZ2VyLmdldEluc3RhbmNlKClcclxuXHJcbiAgICAvLyBFbmhhbmNlZCBjYWxsYmFjayB0aGF0IGhhbmRsZXMgc3BlY2lmaWMgdGFibGUgdXBkYXRlc1xyXG4gICAgY29uc3QgaGFuZGxlUmVhbHRpbWVVcGRhdGUgPSAodGFibGU/OiBzdHJpbmcsIGV2ZW50Pzogc3RyaW5nKSA9PiB7XHJcbiAgICAgIGlmICh0YWJsZSAmJiB0YWJsZSAhPT0gXCJwb2xsaW5nXCIpIHtcclxuICAgICAgICAvLyBMb2FkIG9ubHkgdGhlIHNwZWNpZmljIHRhYmxlIHRoYXQgY2hhbmdlZCBmb3IgZmFzdGVyIHVwZGF0ZXNcclxuICAgICAgICBsb2FkQWxsRGF0YSh0YWJsZSlcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICAvLyBGYWxsYmFjayB0byBsb2FkaW5nIGFsbCBkYXRhXHJcbiAgICAgICAgbG9hZEFsbERhdGEoKVxyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgc3Vic2NyaXB0aW9uTWFuYWdlci5hZGRDYWxsYmFjayhoYW5kbGVSZWFsdGltZVVwZGF0ZSlcclxuXHJcbiAgICByZXR1cm4gKCkgPT4ge1xyXG4gICAgICBjb25zb2xlLmxvZyhcIvCfp7kgQ2xlYW5pbmcgdXAgc3Vic2NyaXB0aW9uIG1hbmFnZXIuLi5cIilcclxuICAgICAgc3Vic2NyaXB0aW9uTWFuYWdlci5yZW1vdmVDYWxsYmFjayhoYW5kbGVSZWFsdGltZVVwZGF0ZSlcclxuICAgICAgc3Vic2NyaXB0aW9uU2V0dXBSZWYuY3VycmVudCA9IGZhbHNlXHJcbiAgICB9XHJcbiAgfSwgW2xvYWRBbGxEYXRhXSlcclxuXHJcbiAgLy8gU2V0IHVwIG1hY2hpbmUgc3RhdHVzIG1vbml0b3JpbmcgKG9ubHkgb25jZSlcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKHN0YXR1c01hbmFnZXJTZXR1cFJlZi5jdXJyZW50KSB7XHJcbiAgICAgIHJldHVyblxyXG4gICAgfVxyXG5cclxuICAgIHN0YXR1c01hbmFnZXJTZXR1cFJlZi5jdXJyZW50ID0gdHJ1ZVxyXG4gICAgY29uc29sZS5sb2coXCLwn5SEIFNldHRpbmcgdXAgbWFjaGluZSBzdGF0dXMgbWFuYWdlci4uLlwiKVxyXG5cclxuICAgIGNvbnN0IHN0YXR1c01hbmFnZXIgPSBNYWNoaW5lU3RhdHVzTWFuYWdlci5nZXRJbnN0YW5jZSgpXHJcbiAgICBzdGF0dXNNYW5hZ2VyLnN0YXJ0U3RhdHVzTW9uaXRvcmluZygpXHJcblxyXG4gICAgcmV0dXJuICgpID0+IHtcclxuICAgICAgY29uc29sZS5sb2coXCLwn6e5IENsZWFuaW5nIHVwIG1hY2hpbmUgc3RhdHVzIG1hbmFnZXIuLi5cIilcclxuICAgICAgc3RhdHVzTWFuYWdlci5zdG9wU3RhdHVzTW9uaXRvcmluZygpXHJcbiAgICAgIHN0YXR1c01hbmFnZXJTZXR1cFJlZi5jdXJyZW50ID0gZmFsc2VcclxuICAgIH1cclxuICB9LCBbXSlcclxuXHJcbiAgLy8gSW5pdGlhbCBkYXRhIGxvYWQgYW5kIGNsZWFudXBcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaXNNb3VudGVkUmVmLmN1cnJlbnQgPSB0cnVlXHJcbiAgICBsb2FkQWxsRGF0YSgpXHJcblxyXG4gICAgcmV0dXJuICgpID0+IHtcclxuICAgICAgaXNNb3VudGVkUmVmLmN1cnJlbnQgPSBmYWxzZVxyXG4gICAgfVxyXG4gIH0sIFtdKSAvLyBPbmx5IHJ1biBvbmNlIG9uIG1vdW50XHJcblxyXG4gIC8vIE1hY2hpbmUgb3BlcmF0aW9ucyB3aXRoIG9wdGltaXN0aWMgdXBkYXRlc1xyXG4gIGNvbnN0IHRvZ2dsZU1hY2hpbmVTdGF0dXMgPSB1c2VDYWxsYmFjayhcclxuICAgIGFzeW5jIChpZDogc3RyaW5nKSA9PiB7XHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgY29uc3QgbWFjaGluZSA9IGxhdW5kcnkuZmluZCgobSkgPT4gbS5pZCA9PT0gaWQpXHJcbiAgICAgICAgaWYgKCFtYWNoaW5lKSB7XHJcbiAgICAgICAgICByZXR1cm4gZmFsc2VcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGxldCBvcHRpbWlzdGljVXBkYXRlOiBNYWNoaW5lXHJcbiAgICAgICAgbGV0IHVwZGF0ZURhdGE6IGFueVxyXG5cclxuICAgICAgICBpZiAobWFjaGluZS5zdGF0dXMgPT09IFwiZnJlZVwiKSB7XHJcbiAgICAgICAgICBjb25zdCBzdGFydEF0ID0gbmV3IERhdGUoKVxyXG4gICAgICAgICAgY29uc3QgZW5kQXQgPSBuZXcgRGF0ZShzdGFydEF0LmdldFRpbWUoKSArIDYwICogNjAgKiAxMDAwKVxyXG4gICAgICAgICAgY29uc3QgY3VycmVudFVzZXJJZCA9IGdldERldmljZVVzZXJJZCgpXHJcblxyXG4gICAgICAgICAgb3B0aW1pc3RpY1VwZGF0ZSA9IHtcclxuICAgICAgICAgICAgLi4ubWFjaGluZSxcclxuICAgICAgICAgICAgc3RhdHVzOiBcInJ1bm5pbmdcIixcclxuICAgICAgICAgICAgc3RhcnRBdCxcclxuICAgICAgICAgICAgZW5kQXQsXHJcbiAgICAgICAgICAgIGdyYWNlRW5kQXQ6IHVuZGVmaW5lZCxcclxuICAgICAgICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpLFxyXG4gICAgICAgICAgICBzdGFydGVkQnlVc2VySWQ6IGN1cnJlbnRVc2VySWQsXHJcbiAgICAgICAgICAgIHN0YXJ0ZWRCeURldmljZUZpbmdlcnByaW50OiBjdXJyZW50VXNlcklkLCAvLyBVc2luZyBzYW1lIHZhbHVlIGZvciBub3dcclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICB1cGRhdGVEYXRhID0ge1xyXG4gICAgICAgICAgICBzdGF0dXM6IFwicnVubmluZ1wiLFxyXG4gICAgICAgICAgICBzdGFydF9hdDogc3RhcnRBdC50b0lTT1N0cmluZygpLFxyXG4gICAgICAgICAgICBlbmRfYXQ6IGVuZEF0LnRvSVNPU3RyaW5nKCksXHJcbiAgICAgICAgICAgIHN0YXJ0ZWRfYnlfdXNlcl9pZDogY3VycmVudFVzZXJJZCxcclxuICAgICAgICAgICAgc3RhcnRlZF9ieV9kZXZpY2VfZmluZ2VycHJpbnQ6IGN1cnJlbnRVc2VySWQsXHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgaWYgKGhhc0dyYWNlRW5kQ29sdW1uKSB7XHJcbiAgICAgICAgICAgIHVwZGF0ZURhdGEuZ3JhY2VfZW5kX2F0ID0gbnVsbFxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICBvcHRpbWlzdGljVXBkYXRlID0ge1xyXG4gICAgICAgICAgICAuLi5tYWNoaW5lLFxyXG4gICAgICAgICAgICBzdGF0dXM6IFwiZnJlZVwiLFxyXG4gICAgICAgICAgICBzdGFydEF0OiB1bmRlZmluZWQsXHJcbiAgICAgICAgICAgIGVuZEF0OiB1bmRlZmluZWQsXHJcbiAgICAgICAgICAgIGdyYWNlRW5kQXQ6IHVuZGVmaW5lZCxcclxuICAgICAgICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpLFxyXG4gICAgICAgICAgICBzdGFydGVkQnlVc2VySWQ6IHVuZGVmaW5lZCxcclxuICAgICAgICAgICAgc3RhcnRlZEJ5RGV2aWNlRmluZ2VycHJpbnQ6IHVuZGVmaW5lZCxcclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICB1cGRhdGVEYXRhID0ge1xyXG4gICAgICAgICAgICBzdGF0dXM6IFwiZnJlZVwiLFxyXG4gICAgICAgICAgICBzdGFydF9hdDogbnVsbCxcclxuICAgICAgICAgICAgZW5kX2F0OiBudWxsLFxyXG4gICAgICAgICAgICBzdGFydGVkX2J5X3VzZXJfaWQ6IG51bGwsXHJcbiAgICAgICAgICAgIHN0YXJ0ZWRfYnlfZGV2aWNlX2ZpbmdlcnByaW50OiBudWxsLFxyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIGlmIChoYXNHcmFjZUVuZENvbHVtbikge1xyXG4gICAgICAgICAgICB1cGRhdGVEYXRhLmdyYWNlX2VuZF9hdCA9IG51bGxcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8vIEFwcGx5IG9wdGltaXN0aWMgdXBkYXRlXHJcbiAgICAgICAgc2V0TGF1bmRyeSgocHJldikgPT4gcHJldi5tYXAoKG0pID0+IChtLmlkID09PSBpZCA/IG9wdGltaXN0aWNVcGRhdGUgOiBtKSkpXHJcblxyXG4gICAgICAgIC8vIFNlbmQgdXBkYXRlIHRvIGRhdGFiYXNlXHJcbiAgICAgICAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuZnJvbShcIm1hY2hpbmVzXCIpLnVwZGF0ZSh1cGRhdGVEYXRhKS5lcShcImlkXCIsIGlkKVxyXG5cclxuICAgICAgICBpZiAoZXJyb3IpIHtcclxuICAgICAgICAgIGlmIChlcnJvci5tZXNzYWdlICYmIGVycm9yLm1lc3NhZ2UuaW5jbHVkZXMoXCJncmFjZV9lbmRfYXRcIikpIHtcclxuICAgICAgICAgICAgc2V0SGFzR3JhY2VFbmRDb2x1bW4oZmFsc2UpXHJcbiAgICAgICAgICAgIGRlbGV0ZSB1cGRhdGVEYXRhLmdyYWNlX2VuZF9hdFxyXG4gICAgICAgICAgICBjb25zdCB7IGVycm9yOiByZXRyeUVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5mcm9tKFwibWFjaGluZXNcIikudXBkYXRlKHVwZGF0ZURhdGEpLmVxKFwiaWRcIiwgaWQpXHJcbiAgICAgICAgICAgIGlmIChyZXRyeUVycm9yKSB7XHJcbiAgICAgICAgICAgICAgc2V0TGF1bmRyeSgocHJldikgPT4gcHJldi5tYXAoKG0pID0+IChtLmlkID09PSBpZCA/IG1hY2hpbmUgOiBtKSkpXHJcbiAgICAgICAgICAgICAgdGhyb3cgcmV0cnlFcnJvclxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICBzZXRMYXVuZHJ5KChwcmV2KSA9PiBwcmV2Lm1hcCgobSkgPT4gKG0uaWQgPT09IGlkID8gbWFjaGluZSA6IG0pKSlcclxuICAgICAgICAgICAgdGhyb3cgZXJyb3JcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIHJldHVybiB0cnVlXHJcbiAgICAgIH0gY2F0Y2ggKGVycikge1xyXG4gICAgICAgIHNldEVycm9yKGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiBcIkZhaWxlZCB0byB1cGRhdGUgbWFjaGluZVwiKVxyXG4gICAgICAgIHJldHVybiBmYWxzZVxyXG4gICAgICB9XHJcbiAgICB9LFxyXG4gICAgW2xhdW5kcnksIGhhc0dyYWNlRW5kQ29sdW1uXSxcclxuICApXHJcblxyXG4gIGNvbnN0IHJlc2VydmVNYWNoaW5lID0gdXNlQ2FsbGJhY2soXHJcbiAgICBhc3luYyAoaWQ6IHN0cmluZywgZHVyYXRpb25TdHI6IHN0cmluZykgPT4ge1xyXG4gICAgICB0cnkge1xyXG4gICAgICAgIGNvbnN0IG1hY2hpbmUgPSBsYXVuZHJ5LmZpbmQoKG0pID0+IG0uaWQgPT09IGlkKVxyXG4gICAgICAgIGlmICghbWFjaGluZSkge1xyXG4gICAgICAgICAgcmV0dXJuIGZhbHNlXHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBjb25zdCBkdXJhdGlvblNlY29uZHMgPSBwYXJzZUR1cmF0aW9uKGR1cmF0aW9uU3RyKVxyXG4gICAgICAgIGNvbnN0IHN0YXJ0QXQgPSBuZXcgRGF0ZSgpXHJcbiAgICAgICAgY29uc3QgZW5kQXQgPSBuZXcgRGF0ZShzdGFydEF0LmdldFRpbWUoKSArIGR1cmF0aW9uU2Vjb25kcyAqIDEwMDApXHJcbiAgICAgICAgY29uc3QgY3VycmVudFVzZXJJZCA9IGdldERldmljZVVzZXJJZCgpXHJcblxyXG4gICAgICAgIGNvbnN0IG9wdGltaXN0aWNVcGRhdGUgPSB7XHJcbiAgICAgICAgICAuLi5tYWNoaW5lLFxyXG4gICAgICAgICAgc3RhdHVzOiBcInJ1bm5pbmdcIiBhcyBjb25zdCxcclxuICAgICAgICAgIHN0YXJ0QXQsXHJcbiAgICAgICAgICBlbmRBdCxcclxuICAgICAgICAgIGdyYWNlRW5kQXQ6IHVuZGVmaW5lZCxcclxuICAgICAgICAgIHVwZGF0ZWRBdDogbmV3IERhdGUoKSxcclxuICAgICAgICAgIHN0YXJ0ZWRCeVVzZXJJZDogY3VycmVudFVzZXJJZCxcclxuICAgICAgICAgIHN0YXJ0ZWRCeURldmljZUZpbmdlcnByaW50OiBjdXJyZW50VXNlcklkLFxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgc2V0TGF1bmRyeSgocHJldikgPT4gcHJldi5tYXAoKG0pID0+IChtLmlkID09PSBpZCA/IG9wdGltaXN0aWNVcGRhdGUgOiBtKSkpXHJcblxyXG4gICAgICAgIGNvbnN0IHVwZGF0ZURhdGE6IGFueSA9IHtcclxuICAgICAgICAgIHN0YXR1czogXCJydW5uaW5nXCIsXHJcbiAgICAgICAgICBzdGFydF9hdDogc3RhcnRBdC50b0lTT1N0cmluZygpLFxyXG4gICAgICAgICAgZW5kX2F0OiBlbmRBdC50b0lTT1N0cmluZygpLFxyXG4gICAgICAgICAgc3RhcnRlZF9ieV91c2VyX2lkOiBjdXJyZW50VXNlcklkLFxyXG4gICAgICAgICAgc3RhcnRlZF9ieV9kZXZpY2VfZmluZ2VycHJpbnQ6IGN1cnJlbnRVc2VySWQsXHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBpZiAoaGFzR3JhY2VFbmRDb2x1bW4pIHtcclxuICAgICAgICAgIHVwZGF0ZURhdGEuZ3JhY2VfZW5kX2F0ID0gbnVsbFxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuZnJvbShcIm1hY2hpbmVzXCIpLnVwZGF0ZSh1cGRhdGVEYXRhKS5lcShcImlkXCIsIGlkKVxyXG5cclxuICAgICAgICBpZiAoZXJyb3IpIHtcclxuICAgICAgICAgIGlmIChlcnJvci5tZXNzYWdlICYmIGVycm9yLm1lc3NhZ2UuaW5jbHVkZXMoXCJncmFjZV9lbmRfYXRcIikpIHtcclxuICAgICAgICAgICAgc2V0SGFzR3JhY2VFbmRDb2x1bW4oZmFsc2UpXHJcbiAgICAgICAgICAgIGRlbGV0ZSB1cGRhdGVEYXRhLmdyYWNlX2VuZF9hdFxyXG4gICAgICAgICAgICBjb25zdCB7IGVycm9yOiByZXRyeUVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5mcm9tKFwibWFjaGluZXNcIikudXBkYXRlKHVwZGF0ZURhdGEpLmVxKFwiaWRcIiwgaWQpXHJcbiAgICAgICAgICAgIGlmIChyZXRyeUVycm9yKSB7XHJcbiAgICAgICAgICAgICAgc2V0TGF1bmRyeSgocHJldikgPT4gcHJldi5tYXAoKG0pID0+IChtLmlkID09PSBpZCA/IG1hY2hpbmUgOiBtKSkpXHJcbiAgICAgICAgICAgICAgdGhyb3cgcmV0cnlFcnJvclxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICBzZXRMYXVuZHJ5KChwcmV2KSA9PiBwcmV2Lm1hcCgobSkgPT4gKG0uaWQgPT09IGlkID8gbWFjaGluZSA6IG0pKSlcclxuICAgICAgICAgICAgdGhyb3cgZXJyb3JcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIHJldHVybiB0cnVlXHJcbiAgICAgIH0gY2F0Y2ggKGVycikge1xyXG4gICAgICAgIHNldEVycm9yKGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiBcIkZhaWxlZCB0byByZXNlcnZlIG1hY2hpbmVcIilcclxuICAgICAgICByZXR1cm4gZmFsc2VcclxuICAgICAgfVxyXG4gICAgfSxcclxuICAgIFtsYXVuZHJ5LCBoYXNHcmFjZUVuZENvbHVtbl0sXHJcbiAgKVxyXG5cclxuICAvLyBOZXcgZnVuY3Rpb24gdG8gYWRqdXN0IG1hY2hpbmUgdGltZSAob25seSBmb3IgbWFjaGluZXMgeW91IHN0YXJ0ZWQpXHJcbiAgY29uc3QgYWRqdXN0TWFjaGluZVRpbWUgPSB1c2VDYWxsYmFjayhcclxuICAgIGFzeW5jIChpZDogc3RyaW5nLCBuZXdNaW51dGVzOiBudW1iZXIpID0+IHtcclxuICAgICAgdHJ5IHtcclxuICAgICAgICBjb25zdCBtYWNoaW5lID0gbGF1bmRyeS5maW5kKChtKSA9PiBtLmlkID09PSBpZClcclxuICAgICAgICBpZiAoIW1hY2hpbmUpIHtcclxuICAgICAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogXCJNYWNoaW5lIG5vdCBmb3VuZFwiIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGNvbnN0IGN1cnJlbnRVc2VySWQgPSBnZXREZXZpY2VVc2VySWQoKVxyXG5cclxuICAgICAgICAvLyBDaGVjayBvd25lcnNoaXAgLSBvbmx5IHRoZSB1c2VyIHdobyBzdGFydGVkIHRoZSBtYWNoaW5lIGNhbiBhZGp1c3QgaXRcclxuICAgICAgICBpZiAobWFjaGluZS5zdGFydGVkQnlVc2VySWQgIT09IGN1cnJlbnRVc2VySWQpIHtcclxuICAgICAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogXCJZb3UgY2FuIG9ubHkgYWRqdXN0IG1hY2hpbmVzIHlvdSBzdGFydGVkXCIgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gVmFsaWRhdGUgdGltZSByYW5nZSAoMS0xMjAgbWludXRlcyBmb3IgYWRqdXN0bWVudClcclxuICAgICAgICBpZiAobmV3TWludXRlcyA8IDEgfHwgbmV3TWludXRlcyA+IDEyMCkge1xyXG4gICAgICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiBcIlBsZWFzZSBlbnRlciBhIG51bWJlciBiZXR3ZWVuIDEtMTIwIG1pbnV0ZXNcIiB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBDYWxjdWxhdGUgbmV3IGVuZCB0aW1lXHJcbiAgICAgICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKVxyXG4gICAgICAgIGNvbnN0IG5ld0VuZEF0ID0gbmV3IERhdGUobm93LmdldFRpbWUoKSArIG5ld01pbnV0ZXMgKiA2MCAqIDEwMDApXHJcblxyXG4gICAgICAgIGNvbnN0IG9wdGltaXN0aWNVcGRhdGUgPSB7XHJcbiAgICAgICAgICAuLi5tYWNoaW5lLFxyXG4gICAgICAgICAgZW5kQXQ6IG5ld0VuZEF0LFxyXG4gICAgICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpLFxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gQXBwbHkgb3B0aW1pc3RpYyB1cGRhdGVcclxuICAgICAgICBzZXRMYXVuZHJ5KChwcmV2KSA9PiBwcmV2Lm1hcCgobSkgPT4gKG0uaWQgPT09IGlkID8gb3B0aW1pc3RpY1VwZGF0ZSA6IG0pKSlcclxuXHJcbiAgICAgICAgLy8gU2VuZCB1cGRhdGUgdG8gZGF0YWJhc2VcclxuICAgICAgICBjb25zdCB7IGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxyXG4gICAgICAgICAgLmZyb20oXCJtYWNoaW5lc1wiKVxyXG4gICAgICAgICAgLnVwZGF0ZSh7IGVuZF9hdDogbmV3RW5kQXQudG9JU09TdHJpbmcoKSB9KVxyXG4gICAgICAgICAgLmVxKFwiaWRcIiwgaWQpXHJcblxyXG4gICAgICAgIGlmIChlcnJvcikge1xyXG4gICAgICAgICAgLy8gUmV2ZXJ0IG9wdGltaXN0aWMgdXBkYXRlIG9uIGVycm9yXHJcbiAgICAgICAgICBzZXRMYXVuZHJ5KChwcmV2KSA9PiBwcmV2Lm1hcCgobSkgPT4gKG0uaWQgPT09IGlkID8gbWFjaGluZSA6IG0pKSlcclxuICAgICAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogXCJVbmFibGUgdG8gdXBkYXRlIHRpbWVyLiBQbGVhc2UgdHJ5IGFnYWluLlwiIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUsIGVycm9yOiBudWxsIH1cclxuICAgICAgfSBjYXRjaCAoZXJyKSB7XHJcbiAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxyXG4gICAgICAgICAgZXJyb3I6IGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiBcIlVuYWJsZSB0byB1cGRhdGUgdGltZXIuIFBsZWFzZSB0cnkgYWdhaW4uXCJcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH0sXHJcbiAgICBbbGF1bmRyeV0sXHJcbiAgKVxyXG5cclxuICAvLyBTaW1wbGlmaWVkIENSVUQgb3BlcmF0aW9uc1xyXG4gIGNvbnN0IGFkZE5vaXNlV2l0aERlc2NyaXB0aW9uID0gdXNlQ2FsbGJhY2soYXN5bmMgKHVzZXI6IHN0cmluZywgZGVzY3JpcHRpb246IHN0cmluZykgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuZnJvbShcIm5vaXNlX3JlcG9ydHNcIikuaW5zZXJ0KHtcclxuICAgICAgICBpZDogRGF0ZS5ub3coKS50b1N0cmluZygpLFxyXG4gICAgICAgIHVzZXJfbmFtZTogdXNlcixcclxuICAgICAgICBkZXNjcmlwdGlvbjogZGVzY3JpcHRpb24sXHJcbiAgICAgIH0pXHJcblxyXG4gICAgICBpZiAoZXJyb3IpIHRocm93IGVycm9yXHJcbiAgICAgIHJldHVybiB0cnVlXHJcbiAgICB9IGNhdGNoIChlcnIpIHtcclxuICAgICAgc2V0RXJyb3IoZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6IFwiRmFpbGVkIHRvIGFkZCBub2lzZSByZXBvcnRcIilcclxuICAgICAgcmV0dXJuIGZhbHNlXHJcbiAgICB9XHJcbiAgfSwgW10pXHJcblxyXG4gIGNvbnN0IGRlbGV0ZU5vaXNlID0gdXNlQ2FsbGJhY2soYXN5bmMgKGlkOiBzdHJpbmcpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHsgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlLmZyb20oXCJub2lzZV9yZXBvcnRzXCIpLmRlbGV0ZSgpLmVxKFwiaWRcIiwgaWQpXHJcbiAgICAgIGlmIChlcnJvcikgdGhyb3cgZXJyb3JcclxuICAgICAgcmV0dXJuIHRydWVcclxuICAgIH0gY2F0Y2ggKGVycikge1xyXG4gICAgICBzZXRFcnJvcihlcnIgaW5zdGFuY2VvZiBFcnJvciA/IGVyci5tZXNzYWdlIDogXCJGYWlsZWQgdG8gZGVsZXRlIG5vaXNlIHJlcG9ydFwiKVxyXG4gICAgICByZXR1cm4gZmFsc2VcclxuICAgIH1cclxuICB9LCBbXSlcclxuXHJcbiAgY29uc3QgYWRkQW5ub3VuY2VtZW50ID0gdXNlQ2FsbGJhY2soYXN5bmMgKHVzZXI6IHN0cmluZywgdGl0bGU6IHN0cmluZywgZGVzY3JpcHRpb246IHN0cmluZywgdHlwZTogc3RyaW5nKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCB7IGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5mcm9tKFwiYW5ub3VuY2VtZW50c1wiKS5pbnNlcnQoe1xyXG4gICAgICAgIGlkOiBEYXRlLm5vdygpLnRvU3RyaW5nKCksXHJcbiAgICAgICAgdXNlcl9uYW1lOiB1c2VyLFxyXG4gICAgICAgIHRpdGxlOiB0aXRsZSxcclxuICAgICAgICBkZXNjcmlwdGlvbjogZGVzY3JpcHRpb24sXHJcbiAgICAgICAgYW5ub3VuY2VtZW50X3R5cGU6IHR5cGUsXHJcbiAgICAgIH0pXHJcblxyXG4gICAgICBpZiAoZXJyb3IpIHRocm93IGVycm9yXHJcbiAgICAgIHJldHVybiB0cnVlXHJcbiAgICB9IGNhdGNoIChlcnIpIHtcclxuICAgICAgc2V0RXJyb3IoZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6IFwiRmFpbGVkIHRvIGFkZCBhbm5vdW5jZW1lbnRcIilcclxuICAgICAgcmV0dXJuIGZhbHNlXHJcbiAgICB9XHJcbiAgfSwgW10pXHJcblxyXG4gIGNvbnN0IGRlbGV0ZUFubm91bmNlbWVudCA9IHVzZUNhbGxiYWNrKGFzeW5jIChpZDogc3RyaW5nKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCB7IGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5mcm9tKFwiYW5ub3VuY2VtZW50c1wiKS5kZWxldGUoKS5lcShcImlkXCIsIGlkKVxyXG4gICAgICBpZiAoZXJyb3IpIHRocm93IGVycm9yXHJcbiAgICAgIHJldHVybiB0cnVlXHJcbiAgICB9IGNhdGNoIChlcnIpIHtcclxuICAgICAgc2V0RXJyb3IoZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6IFwiRmFpbGVkIHRvIGRlbGV0ZSBhbm5vdW5jZW1lbnRcIilcclxuICAgICAgcmV0dXJuIGZhbHNlXHJcbiAgICB9XHJcbiAgfSwgW10pXHJcblxyXG4gIGNvbnN0IGFkZEhlbHBSZXF1ZXN0ID0gdXNlQ2FsbGJhY2soYXN5bmMgKHVzZXI6IHN0cmluZywgZGVzY3JpcHRpb246IHN0cmluZykgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuZnJvbShcImhlbHBfcmVxdWVzdHNcIikuaW5zZXJ0KHtcclxuICAgICAgICBpZDogRGF0ZS5ub3coKS50b1N0cmluZygpLFxyXG4gICAgICAgIHVzZXJfbmFtZTogdXNlcixcclxuICAgICAgICBkZXNjcmlwdGlvbixcclxuICAgICAgfSlcclxuXHJcbiAgICAgIGlmIChlcnJvcikgdGhyb3cgZXJyb3JcclxuICAgICAgcmV0dXJuIHRydWVcclxuICAgIH0gY2F0Y2ggKGVycikge1xyXG4gICAgICBzZXRFcnJvcihlcnIgaW5zdGFuY2VvZiBFcnJvciA/IGVyci5tZXNzYWdlIDogXCJGYWlsZWQgdG8gYWRkIGhlbHAgcmVxdWVzdFwiKVxyXG4gICAgICByZXR1cm4gZmFsc2VcclxuICAgIH1cclxuICB9LCBbXSlcclxuXHJcbiAgY29uc3QgZGVsZXRlSGVscFJlcXVlc3QgPSB1c2VDYWxsYmFjayhhc3luYyAoaWQ6IHN0cmluZykgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuZnJvbShcImhlbHBfcmVxdWVzdHNcIikuZGVsZXRlKCkuZXEoXCJpZFwiLCBpZClcclxuICAgICAgaWYgKGVycm9yKSB0aHJvdyBlcnJvclxyXG4gICAgICByZXR1cm4gdHJ1ZVxyXG4gICAgfSBjYXRjaCAoZXJyKSB7XHJcbiAgICAgIHNldEVycm9yKGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiBcIkZhaWxlZCB0byBkZWxldGUgaGVscCByZXF1ZXN0XCIpXHJcbiAgICAgIHJldHVybiBmYWxzZVxyXG4gICAgfVxyXG4gIH0sIFtdKVxyXG5cclxuICBjb25zdCBkZWxldGVJbmNpZGVudCA9IHVzZUNhbGxiYWNrKGFzeW5jIChpZDogc3RyaW5nKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCB7IGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5mcm9tKFwiaW5jaWRlbnRzXCIpLmRlbGV0ZSgpLmVxKFwiaWRcIiwgaWQpXHJcbiAgICAgIGlmIChlcnJvcikgdGhyb3cgZXJyb3JcclxuICAgICAgcmV0dXJuIHRydWVcclxuICAgIH0gY2F0Y2ggKGVycikge1xyXG4gICAgICBzZXRFcnJvcihlcnIgaW5zdGFuY2VvZiBFcnJvciA/IGVyci5tZXNzYWdlIDogXCJGYWlsZWQgdG8gZGVsZXRlIGluY2lkZW50XCIpXHJcbiAgICAgIHJldHVybiBmYWxzZVxyXG4gICAgfVxyXG4gIH0sIFtdKVxyXG5cclxuICByZXR1cm4ge1xyXG4gICAgLy8gRGF0YVxyXG4gICAgbGF1bmRyeSxcclxuICAgIG5vaXNlLFxyXG4gICAgYW5ub3VuY2VtZW50cyxcclxuICAgIGhlbHBNZSxcclxuICAgIGluY2lkZW50cyxcclxuXHJcbiAgICAvLyBTdGF0ZVxyXG4gICAgaXNMb2FkaW5nLFxyXG4gICAgZXJyb3IsXHJcblxyXG4gICAgLy8gT3BlcmF0aW9uc1xyXG4gICAgdG9nZ2xlTWFjaGluZVN0YXR1cyxcclxuICAgIHJlc2VydmVNYWNoaW5lLFxyXG4gICAgYWRqdXN0TWFjaGluZVRpbWUsXHJcbiAgICBhZGROb2lzZVdpdGhEZXNjcmlwdGlvbixcclxuICAgIGRlbGV0ZU5vaXNlLFxyXG4gICAgYWRkQW5ub3VuY2VtZW50LFxyXG4gICAgZGVsZXRlQW5ub3VuY2VtZW50LFxyXG4gICAgYWRkSGVscFJlcXVlc3QsXHJcbiAgICBkZWxldGVIZWxwUmVxdWVzdCxcclxuICAgIGRlbGV0ZUluY2lkZW50LFxyXG4gICAgcmVmcmVzaERhdGE6ICgpID0+IGxvYWRBbGxEYXRhKHVuZGVmaW5lZCwgdHJ1ZSksXHJcbiAgfVxyXG59XHJcbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZUNhbGxiYWNrIiwidXNlUmVmIiwic3VwYWJhc2UiLCJTdWJzY3JpcHRpb25NYW5hZ2VyIiwiTWFjaGluZVN0YXR1c01hbmFnZXIiLCJwYXJzZUR1cmF0aW9uIiwiZ2V0RGV2aWNlVXNlcklkIiwiZGJUb01hY2hpbmUiLCJyb3ciLCJncmFjZUVuZEF0IiwiZ3JhY2VfZW5kX2F0IiwiRGF0ZSIsInN0YXR1cyIsImVuZF9hdCIsImdldFRpbWUiLCJ1bmRlZmluZWQiLCJpZCIsIm5hbWUiLCJzdGFydEF0Iiwic3RhcnRfYXQiLCJlbmRBdCIsInVwZGF0ZWRBdCIsInVwZGF0ZWRfYXQiLCJzdGFydGVkQnlVc2VySWQiLCJzdGFydGVkX2J5X3VzZXJfaWQiLCJzdGFydGVkQnlEZXZpY2VGaW5nZXJwcmludCIsInN0YXJ0ZWRfYnlfZGV2aWNlX2ZpbmdlcnByaW50IiwiZGJUb05vaXNlIiwidXNlciIsInVzZXJfbmFtZSIsImRlc2NyaXB0aW9uIiwidGltZXN0YW1wIiwibGFzdFJlcG9ydGVkIiwibGFzdF9yZXBvcnRlZCIsImRiVG9Bbm5vdW5jZW1lbnQiLCJ0aXRsZSIsInR5cGUiLCJhbm5vdW5jZW1lbnRfdHlwZSIsImRiVG9TdWJsZXQiLCJkdXJhdGlvbiIsImRiVG9IZWxwTWUiLCJkYlRvSW5jaWRlbnQiLCJtYWNoaW5lSWQiLCJtYWNoaW5lX2lkIiwiaW5jaWRlbnRfdHlwZSIsInVzZVN1cGFiYXNlRGF0YSIsImxhdW5kcnkiLCJzZXRMYXVuZHJ5Iiwibm9pc2UiLCJzZXROb2lzZSIsImFubm91bmNlbWVudHMiLCJzZXRBbm5vdW5jZW1lbnRzIiwiaGVscE1lIiwic2V0SGVscE1lIiwiaW5jaWRlbnRzIiwic2V0SW5jaWRlbnRzIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsImhhc0dyYWNlRW5kQ29sdW1uIiwic2V0SGFzR3JhY2VFbmRDb2x1bW4iLCJpc0xvYWRpbmdEYXRhUmVmIiwibGFzdExvYWRUaW1lUmVmIiwic3Vic2NyaXB0aW9uU2V0dXBSZWYiLCJzdGF0dXNNYW5hZ2VyU2V0dXBSZWYiLCJpc01vdW50ZWRSZWYiLCJsb2FkQWxsRGF0YSIsInNwZWNpZmljVGFibGUiLCJmb3JjZVJlZnJlc2giLCJub3ciLCJjdXJyZW50IiwiY29uc29sZSIsImxvZyIsImxvYWRTcGVjaWZpY1RhYmxlIiwibG9hZEFsbFRhYmxlcyIsImVyciIsIkVycm9yIiwibWVzc2FnZSIsInRhYmxlIiwibWFjaGluZXNSZXN1bHQiLCJmcm9tIiwic2VsZWN0Iiwib3JkZXIiLCJkYXRhIiwibWFwIiwibm9pc2VSZXN1bHQiLCJhc2NlbmRpbmciLCJsaW1pdCIsImFubm91bmNlbWVudHNSZXN1bHQiLCJoZWxwUmVzdWx0IiwiZ3RlIiwidG9JU09TdHJpbmciLCJpbmNpZGVudHNSZXN1bHQiLCJ0aW1lb3V0UHJvbWlzZSIsIlByb21pc2UiLCJfIiwicmVqZWN0Iiwic2V0VGltZW91dCIsImRhdGFQcm9taXNlIiwiYWxsIiwicmFjZSIsInN1YnNjcmlwdGlvbk1hbmFnZXIiLCJnZXRJbnN0YW5jZSIsImhhbmRsZVJlYWx0aW1lVXBkYXRlIiwiZXZlbnQiLCJhZGRDYWxsYmFjayIsInJlbW92ZUNhbGxiYWNrIiwic3RhdHVzTWFuYWdlciIsInN0YXJ0U3RhdHVzTW9uaXRvcmluZyIsInN0b3BTdGF0dXNNb25pdG9yaW5nIiwidG9nZ2xlTWFjaGluZVN0YXR1cyIsIm1hY2hpbmUiLCJmaW5kIiwibSIsIm9wdGltaXN0aWNVcGRhdGUiLCJ1cGRhdGVEYXRhIiwiY3VycmVudFVzZXJJZCIsInByZXYiLCJ1cGRhdGUiLCJlcSIsImluY2x1ZGVzIiwicmV0cnlFcnJvciIsInJlc2VydmVNYWNoaW5lIiwiZHVyYXRpb25TdHIiLCJkdXJhdGlvblNlY29uZHMiLCJhZGp1c3RNYWNoaW5lVGltZSIsIm5ld01pbnV0ZXMiLCJzdWNjZXNzIiwibmV3RW5kQXQiLCJhZGROb2lzZVdpdGhEZXNjcmlwdGlvbiIsImluc2VydCIsInRvU3RyaW5nIiwiZGVsZXRlTm9pc2UiLCJkZWxldGUiLCJhZGRBbm5vdW5jZW1lbnQiLCJkZWxldGVBbm5vdW5jZW1lbnQiLCJhZGRIZWxwUmVxdWVzdCIsImRlbGV0ZUhlbHBSZXF1ZXN0IiwiZGVsZXRlSW5jaWRlbnQiLCJyZWZyZXNoRGF0YSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useSupabaseData.tsx\n"));

/***/ })

});