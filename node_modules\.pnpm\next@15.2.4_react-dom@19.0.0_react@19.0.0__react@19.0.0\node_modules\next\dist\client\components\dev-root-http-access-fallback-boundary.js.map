{"version": 3, "sources": ["../../../src/client/components/dev-root-http-access-fallback-boundary.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { HTTPAccessFallbackBoundary } from './http-access-fallback/error-boundary'\n\n// TODO: error on using forbidden and unauthorized in root layout\nexport function bailOnRootNotFound() {\n  throw new Error('notFound() is not allowed to use in root layout')\n}\n\nfunction NotAllowedRootHTTPFallbackError() {\n  bailOnRootNotFound()\n  return null\n}\n\nexport function DevRootHTTPAccessFallbackBoundary({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  return (\n    <HTTPAccessFallbackBoundary notFound={<NotAllowedRootHTTPFallbackError />}>\n      {children}\n    </HTTPAccessFallbackBoundary>\n  )\n}\n"], "names": ["DevRootHTTPAccessFallbackBoundary", "bailOnRootNotFound", "Error", "NotAllowedRootHTTPFallbackError", "children", "HTTPAccessFallbackBoundary", "notFound"], "mappings": "AAAA;;;;;;;;;;;;;;;;IAegBA,iCAAiC;eAAjCA;;IATAC,kBAAkB;eAAlBA;;;;;gEAJE;+BACyB;AAGpC,SAASA;IACd,MAAM,qBAA4D,CAA5D,IAAIC,MAAM,oDAAV,qBAAA;eAAA;oBAAA;sBAAA;IAA2D;AACnE;AAEA,SAASC;IACPF;IACA,OAAO;AACT;AAEO,SAASD,kCAAkC,KAIjD;IAJiD,IAAA,EAChDI,QAAQ,EAGT,GAJiD;IAKhD,qBACE,qBAACC,yCAA0B;QAACC,wBAAU,qBAACH;kBACpCC;;AAGP"}