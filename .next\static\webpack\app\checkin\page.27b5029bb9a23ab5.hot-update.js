"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/checkin/page",{

/***/ "(app-pages-browser)/./app/checkin/page.tsx":
/*!******************************!*\
  !*** ./app/checkin/page.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CheckInPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var qrcode_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! qrcode.react */ \"(app-pages-browser)/./node_modules/.pnpm/qrcode.react@4.2.0_react@19.0.0/node_modules/qrcode.react/lib/esm/index.js\");\n/* harmony import */ var _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/src/hooks/useSupabaseData */ \"(app-pages-browser)/./src/hooks/useSupabaseData.tsx\");\n/* harmony import */ var _src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/src/hooks/useCountdown */ \"(app-pages-browser)/./src/hooks/useCountdown.tsx\");\n/* harmony import */ var _src_utils_machineOwnership__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/src/utils/machineOwnership */ \"(app-pages-browser)/./src/utils/machineOwnership.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction CheckInPage() {\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const machineId = searchParams.get(\"machine\");\n    const { laundry, toggleMachineStatus, reserveMachine, adjustMachineTime, isLoading, refreshData } = (0,_src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    const [machine, setMachine] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [actionResult, setActionResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [customTime, setCustomTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"60\");\n    const [adjustTime, setAdjustTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isAdjusting, setIsAdjusting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [adjustResult, setAdjustResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const countdown = (0,_src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(machine === null || machine === void 0 ? void 0 : machine.endAt, machine === null || machine === void 0 ? void 0 : machine.graceEndAt);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CheckInPage.useEffect\": ()=>{\n            if (!machineId) return;\n            const foundMachine = laundry.find({\n                \"CheckInPage.useEffect.foundMachine\": (m)=>m.id === machineId\n            }[\"CheckInPage.useEffect.foundMachine\"]);\n            setMachine(foundMachine || null);\n        }\n    }[\"CheckInPage.useEffect\"], [\n        machineId,\n        laundry\n    ]);\n    // Reduced auto-refresh to every 10 seconds to prevent conflicts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CheckInPage.useEffect\": ()=>{\n            const interval = setInterval({\n                \"CheckInPage.useEffect.interval\": ()=>{\n                    refreshData();\n                }\n            }[\"CheckInPage.useEffect.interval\"], 10000);\n            return ({\n                \"CheckInPage.useEffect\": ()=>clearInterval(interval)\n            })[\"CheckInPage.useEffect\"];\n        }\n    }[\"CheckInPage.useEffect\"], [\n        refreshData\n    ]);\n    const handleToggle = async ()=>{\n        if (!machineId || isProcessing) return;\n        setIsProcessing(true);\n        setActionResult(\"\");\n        let success = false;\n        if ((machine === null || machine === void 0 ? void 0 : machine.status) === \"free\" && useCustomTime) {\n            // Use custom time\n            success = await reserveMachine(machineId, \"\".concat(customTime, \" minutes\"));\n        } else {\n            // Use default toggle\n            success = await toggleMachineStatus(machineId);\n        }\n        if (success) {\n            setActionResult(\"Status updated successfully!\");\n            // Single refresh after 2 seconds to confirm the change\n            setTimeout(()=>refreshData(), 2000);\n        } else {\n            setActionResult(\"Error updating machine status\");\n        }\n        setIsProcessing(false);\n    };\n    const getStatusDisplay = ()=>{\n        if (!machine) return \"\";\n        switch(machine.status){\n            case \"free\":\n                return \"🟢 Available\";\n            case \"running\":\n                const hours = Math.floor(countdown.secondsLeft / 3600);\n                const minutes = Math.floor(countdown.secondsLeft % 3600 / 60);\n                const seconds = countdown.secondsLeft % 60;\n                return \"\\uD83D\\uDD35 In Use - \".concat(hours, \":\").concat(minutes.toString().padStart(2, \"0\"), \":\").concat(seconds.toString().padStart(2, \"0\"), \" left\");\n            case \"finishedGrace\":\n                // Calculate grace period time remaining\n                let graceTimeDisplay = \"5:00\";\n                if (machine.graceEndAt) {\n                    const graceMinutes = Math.floor(countdown.graceSecondsLeft / 60);\n                    const graceSeconds = countdown.graceSecondsLeft % 60;\n                    graceTimeDisplay = \"\".concat(graceMinutes, \":\").concat(graceSeconds.toString().padStart(2, \"0\"));\n                }\n                return \"⚠️ Please collect items - \".concat(graceTimeDisplay, \" left\");\n            default:\n                return \"Unknown\";\n        }\n    };\n    const getButtonText = ()=>{\n        if (isProcessing) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, this),\n                    \"Processing...\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 99,\n                columnNumber: 9\n            }, this);\n        }\n        switch(machine === null || machine === void 0 ? void 0 : machine.status){\n            case \"free\":\n                return useCustomTime ? \"▶️ Start Using Machine (\".concat(customTime, \" min)\") : \"▶️ Start Using Machine\";\n            case \"running\":\n                return \"⏹️ Stop Using Machine\";\n            case \"finishedGrace\":\n                return \"✅ I've Collected My Items\";\n            default:\n                return \"Update Status\";\n        }\n    };\n    const getButtonColor = ()=>{\n        if (isProcessing) return \"bg-gray-400 cursor-not-allowed\";\n        switch(machine === null || machine === void 0 ? void 0 : machine.status){\n            case \"free\":\n                return \"bg-blue-500 hover:bg-blue-600\";\n            case \"running\":\n                return \"bg-red-500 hover:bg-red-600\";\n            case \"finishedGrace\":\n                return \"bg-green-500 hover:bg-green-600\";\n            default:\n                return \"bg-gray-500\";\n        }\n    };\n    const handleBackToHome = ()=>{\n        router.push(\"/\");\n    };\n    const handleAdjustTime = async ()=>{\n        if (!machineId || isAdjusting) return;\n        const minutesNum = parseInt(adjustTime);\n        if (isNaN(minutesNum) || minutesNum < 1 || minutesNum > 120) {\n            setAdjustResult(\"Please enter a number between 1-120 minutes\");\n            return;\n        }\n        setIsAdjusting(true);\n        setAdjustResult(\"\");\n        const result = await adjustMachineTime(machineId, minutesNum);\n        if (result.success) {\n            setAdjustResult(\"Timer updated successfully\");\n            setAdjustTime(\"\");\n            // Refresh data after successful adjustment\n            setTimeout(()=>refreshData(), 1000);\n        } else {\n            setAdjustResult(result.error || \"Failed to update timer\");\n        }\n        setIsAdjusting(false);\n    };\n    // Custom display names for machines (same logic as LaundryCard)\n    const getDisplayName = (machine)=>{\n        if (!machine) return \"\";\n        const isWasher = machine.name.toLowerCase().includes(\"washer\");\n        if (isWasher) {\n            // Extract number from washer name (e.g., \"Washer 1\" -> \"1\")\n            const numberMatch = machine.name.match(/\\d+/);\n            const number = numberMatch ? numberMatch[0] : \"\";\n            return \"Washer \".concat(number);\n        } else {\n            // For dryers, use \"Dryer 5\" through \"Dryer 8\" based on original number\n            const numberMatch = machine.name.match(/\\d+/);\n            const originalNumber = numberMatch ? Number.parseInt(numberMatch[0]) : 0;\n            const newNumber = originalNumber + 4 // Map 1->5, 2->6, 3->7, 4->8\n            ;\n            return \"Dryer \".concat(newNumber);\n        }\n    };\n    if (isLoading && !machine) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-8 rounded-lg shadow text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-lg\",\n                        children: \"Getting machine information...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 186,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 185,\n            columnNumber: 7\n        }, this);\n    }\n    if (!machineId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-8 rounded-lg shadow text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-600 text-lg mb-4\",\n                        children: \"Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600\",\n                        children: \"No machine ID provided.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 197,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 196,\n            columnNumber: 7\n        }, this);\n    }\n    if (!machine) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-8 rounded-lg shadow text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-600 text-lg mb-4\",\n                        children: \"Machine Not Found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"The requested machine could not be found.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: refreshData,\n                        className: \"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded\",\n                        children: \"Retry Loading\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 208,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 207,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white p-8 rounded-lg shadow text-center max-w-md w-full mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-4\",\n                    children: \"Machine Check-In\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-600 mb-3\",\n                            children: \"Scan to access this page\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white p-3 rounded-lg border-2 border-gray-200 shadow-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(qrcode_react__WEBPACK_IMPORTED_MODULE_3__.QRCodeSVG, {\n                                    value: \"\".concat( true ? window.location.origin : 0, \"/checkin?machine=\").concat(machineId),\n                                    size: 80,\n                                    fgColor: \"#1A1F36\",\n                                    bgColor: \"#FFFFFF\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-lg font-medium\",\n                            children: getDisplayName(machine)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm mt-1 font-semibold \".concat(machine.status === \"free\" ? \"text-green-600\" : machine.status === \"running\" ? \"text-blue-600\" : machine.status === \"finishedGrace\" ? \"text-orange-600\" : \"text-red-600\", \" \").concat(machine.status === \"finishedGrace\" ? \"animate-pulse\" : \"\"),\n                            children: [\n                                \"Status: \",\n                                getStatusDisplay()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, this),\n                        machine.status === \"running\" && machine.endAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500 mt-1\",\n                            children: [\n                                \"Ends at: \",\n                                machine.endAt.toLocaleTimeString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 13\n                        }, this),\n                        machine.status === \"finishedGrace\" && machine.graceEndAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-orange-500 mt-1\",\n                            children: [\n                                \"Grace period ends at: \",\n                                machine.graceEndAt.toLocaleTimeString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 13\n                        }, this),\n                        machine.startedByUserId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-600 mt-2\",\n                            children: (0,_src_utils_machineOwnership__WEBPACK_IMPORTED_MODULE_6__.getOwnershipDisplay)(machine)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 9\n                }, this),\n                machine.status === \"free\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    id: \"useCustomTime\",\n                                    checked: useCustomTime,\n                                    onChange: (e)=>setUseCustomTime(e.target.checked),\n                                    className: \"mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"useCustomTime\",\n                                    className: \"text-sm\",\n                                    children: \"Set custom time\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 13\n                        }, this),\n                        useCustomTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"number\",\n                                    value: customTime,\n                                    onChange: (e)=>setCustomTime(e.target.value),\n                                    min: \"1\",\n                                    max: \"120\",\n                                    className: \"border rounded p-2 w-20 text-center mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"minutes\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 275,\n                    columnNumber: 11\n                }, this),\n                (0,_src_utils_machineOwnership__WEBPACK_IMPORTED_MODULE_6__.canAdjustTime)(machine) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium text-blue-800 mb-2\",\n                            children: \"Adjust Timer\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 308,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-blue-600 mb-3\",\n                            children: [\n                                \"Current: \",\n                                machine.endAt ? Math.max(0, Math.ceil((machine.endAt.getTime() - Date.now()) / (1000 * 60))) : 0,\n                                \" minutes remaining\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"number\",\n                                    value: adjustTime,\n                                    onChange: (e)=>setAdjustTime(e.target.value),\n                                    placeholder: \"Minutes (1-120)\",\n                                    min: \"1\",\n                                    max: \"120\",\n                                    disabled: isAdjusting,\n                                    className: \"flex-1 border rounded p-2 text-center text-sm\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleAdjustTime,\n                                    disabled: isAdjusting || !adjustTime,\n                                    className: \"bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1\",\n                                    children: [\n                                        isAdjusting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 border border-white border-t-transparent rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 19\n                                        }, this),\n                                        isAdjusting ? \"Updating...\" : \"Update Timer\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 13\n                        }, this),\n                        adjustResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs p-2 rounded \".concat(adjustResult.includes(\"successfully\") ? \"bg-green-100 text-green-800 border border-green-200\" : \"bg-red-100 text-red-800 border border-red-200\"),\n                            children: adjustResult\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 307,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleToggle,\n                    disabled: isProcessing,\n                    className: \"w-full p-3 rounded text-white font-medium mb-4 \".concat(getButtonColor()),\n                    children: getButtonText()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 348,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: refreshData,\n                    disabled: isLoading,\n                    className: \"w-full p-2 rounded border border-gray-300 text-gray-700 hover:bg-gray-50 mb-4\",\n                    children: \"Refresh Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 356,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleBackToHome,\n                    className: \"w-full p-2 rounded bg-accent hover:bg-teal-600 text-white font-medium mb-4 transition-colors duration-200\",\n                    children: \"← Back to Home\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 365,\n                    columnNumber: 9\n                }, this),\n                actionResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm mb-4 p-2 rounded \".concat(actionResult.includes(\"Error\") ? \"bg-red-100 text-red-700\" : \"bg-green-100 text-green-700\"),\n                    children: actionResult\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 373,\n                    columnNumber: 11\n                }, this),\n                machine.status === \"finishedGrace\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-orange-50 border border-orange-200 rounded p-3 text-sm text-orange-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium mb-1\",\n                            children: \"⚠️ Grace Period Active\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 384,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Please collect your items within the time limit. The machine will become available automatically when the grace period ends.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 383,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 221,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n        lineNumber: 220,\n        columnNumber: 5\n    }, this);\n}\n_s(CheckInPage, \"v+h7YXyy6gXcj/Rvi3VDQhkCD98=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        _src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n});\n_c = CheckInPage;\nvar _c;\n$RefreshReg$(_c, \"CheckInPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/checkin/page.tsx\n"));

/***/ })

});