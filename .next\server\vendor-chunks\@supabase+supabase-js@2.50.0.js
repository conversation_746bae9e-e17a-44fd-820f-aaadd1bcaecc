"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@supabase+supabase-js@2.50.0";
exports.ids = ["vendor-chunks/@supabase+supabase-js@2.50.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.0/node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.50.0/node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SupabaseClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_functions_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @supabase/functions-js */ \"(ssr)/./node_modules/.pnpm/@supabase+functions-js@2.4.4/node_modules/@supabase/functions-js/dist/module/FunctionsClient.js\");\n/* harmony import */ var _supabase_postgrest_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/postgrest-js */ \"(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs\");\n/* harmony import */ var _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/realtime-js */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/index.js\");\n/* harmony import */ var _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_supabase_realtime_js__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_storage_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @supabase/storage-js */ \"(ssr)/./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/StorageClient.js\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/constants */ \"(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.0/node_modules/@supabase/supabase-js/dist/module/lib/constants.js\");\n/* harmony import */ var _lib_fetch__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/fetch */ \"(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.0/node_modules/@supabase/supabase-js/dist/module/lib/fetch.js\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/helpers */ \"(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.0/node_modules/@supabase/supabase-js/dist/module/lib/helpers.js\");\n/* harmony import */ var _lib_SupabaseAuthClient__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./lib/SupabaseAuthClient */ \"(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.0/node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js\");\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\n\n\n\n\n\n\n\n/**\n * Supabase Client.\n *\n * An isomorphic Javascript client for interacting with Postgres.\n */\nclass SupabaseClient {\n    /**\n     * Create a new client for use in the browser.\n     * @param supabaseUrl The unique Supabase URL which is supplied when you create a new project in your project dashboard.\n     * @param supabaseKey The unique Supabase Key which is supplied when you create a new project in your project dashboard.\n     * @param options.db.schema You can switch in between schemas. The schema needs to be on the list of exposed schemas inside Supabase.\n     * @param options.auth.autoRefreshToken Set to \"true\" if you want to automatically refresh the token before expiring.\n     * @param options.auth.persistSession Set to \"true\" if you want to automatically save the user session into local storage.\n     * @param options.auth.detectSessionInUrl Set to \"true\" if you want to automatically detects OAuth grants in the URL and signs in the user.\n     * @param options.realtime Options passed along to realtime-js constructor.\n     * @param options.global.fetch A custom fetch implementation.\n     * @param options.global.headers Any additional headers to send with each network request.\n     */\n    constructor(supabaseUrl, supabaseKey, options) {\n        var _a, _b, _c;\n        this.supabaseUrl = supabaseUrl;\n        this.supabaseKey = supabaseKey;\n        if (!supabaseUrl)\n            throw new Error('supabaseUrl is required.');\n        if (!supabaseKey)\n            throw new Error('supabaseKey is required.');\n        const _supabaseUrl = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_2__.ensureTrailingSlash)(supabaseUrl);\n        const baseUrl = new URL(_supabaseUrl);\n        this.realtimeUrl = new URL('realtime/v1', baseUrl);\n        this.realtimeUrl.protocol = this.realtimeUrl.protocol.replace('http', 'ws');\n        this.authUrl = new URL('auth/v1', baseUrl);\n        this.storageUrl = new URL('storage/v1', baseUrl);\n        this.functionsUrl = new URL('functions/v1', baseUrl);\n        // default storage key uses the supabase project ref as a namespace\n        const defaultStorageKey = `sb-${baseUrl.hostname.split('.')[0]}-auth-token`;\n        const DEFAULTS = {\n            db: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_DB_OPTIONS,\n            realtime: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_REALTIME_OPTIONS,\n            auth: Object.assign(Object.assign({}, _lib_constants__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_AUTH_OPTIONS), { storageKey: defaultStorageKey }),\n            global: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_GLOBAL_OPTIONS,\n        };\n        const settings = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_2__.applySettingDefaults)(options !== null && options !== void 0 ? options : {}, DEFAULTS);\n        this.storageKey = (_a = settings.auth.storageKey) !== null && _a !== void 0 ? _a : '';\n        this.headers = (_b = settings.global.headers) !== null && _b !== void 0 ? _b : {};\n        if (!settings.accessToken) {\n            this.auth = this._initSupabaseAuthClient((_c = settings.auth) !== null && _c !== void 0 ? _c : {}, this.headers, settings.global.fetch);\n        }\n        else {\n            this.accessToken = settings.accessToken;\n            this.auth = new Proxy({}, {\n                get: (_, prop) => {\n                    throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(prop)} is not possible`);\n                },\n            });\n        }\n        this.fetch = (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_4__.fetchWithAuth)(supabaseKey, this._getAccessToken.bind(this), settings.global.fetch);\n        this.realtime = this._initRealtimeClient(Object.assign({ headers: this.headers, accessToken: this._getAccessToken.bind(this) }, settings.realtime));\n        this.rest = new _supabase_postgrest_js__WEBPACK_IMPORTED_MODULE_0__.PostgrestClient(new URL('rest/v1', baseUrl).href, {\n            headers: this.headers,\n            schema: settings.db.schema,\n            fetch: this.fetch,\n        });\n        if (!settings.accessToken) {\n            this._listenForAuthEvents();\n        }\n    }\n    /**\n     * Supabase Functions allows you to deploy and invoke edge functions.\n     */\n    get functions() {\n        return new _supabase_functions_js__WEBPACK_IMPORTED_MODULE_5__.FunctionsClient(this.functionsUrl.href, {\n            headers: this.headers,\n            customFetch: this.fetch,\n        });\n    }\n    /**\n     * Supabase Storage allows you to manage user-generated content, such as photos or videos.\n     */\n    get storage() {\n        return new _supabase_storage_js__WEBPACK_IMPORTED_MODULE_6__.StorageClient(this.storageUrl.href, this.headers, this.fetch);\n    }\n    /**\n     * Perform a query on a table or a view.\n     *\n     * @param relation - The table or view name to query\n     */\n    from(relation) {\n        return this.rest.from(relation);\n    }\n    // NOTE: signatures must be kept in sync with PostgrestClient.schema\n    /**\n     * Select a schema to query or perform an function (rpc) call.\n     *\n     * The schema needs to be on the list of exposed schemas inside Supabase.\n     *\n     * @param schema - The schema to query\n     */\n    schema(schema) {\n        return this.rest.schema(schema);\n    }\n    // NOTE: signatures must be kept in sync with PostgrestClient.rpc\n    /**\n     * Perform a function call.\n     *\n     * @param fn - The function name to call\n     * @param args - The arguments to pass to the function call\n     * @param options - Named parameters\n     * @param options.head - When set to `true`, `data` will not be returned.\n     * Useful if you only need the count.\n     * @param options.get - When set to `true`, the function will be called with\n     * read-only access mode.\n     * @param options.count - Count algorithm to use to count rows returned by the\n     * function. Only applicable for [set-returning\n     * functions](https://www.postgresql.org/docs/current/functions-srf.html).\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */\n    rpc(fn, args = {}, options = {}) {\n        return this.rest.rpc(fn, args, options);\n    }\n    /**\n     * Creates a Realtime channel with Broadcast, Presence, and Postgres Changes.\n     *\n     * @param {string} name - The name of the Realtime channel.\n     * @param {Object} opts - The options to pass to the Realtime channel.\n     *\n     */\n    channel(name, opts = { config: {} }) {\n        return this.realtime.channel(name, opts);\n    }\n    /**\n     * Returns all Realtime channels.\n     */\n    getChannels() {\n        return this.realtime.getChannels();\n    }\n    /**\n     * Unsubscribes and removes Realtime channel from Realtime client.\n     *\n     * @param {RealtimeChannel} channel - The name of the Realtime channel.\n     *\n     */\n    removeChannel(channel) {\n        return this.realtime.removeChannel(channel);\n    }\n    /**\n     * Unsubscribes and removes all Realtime channels from Realtime client.\n     */\n    removeAllChannels() {\n        return this.realtime.removeAllChannels();\n    }\n    _getAccessToken() {\n        var _a, _b;\n        return __awaiter(this, void 0, void 0, function* () {\n            if (this.accessToken) {\n                return yield this.accessToken();\n            }\n            const { data } = yield this.auth.getSession();\n            return (_b = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token) !== null && _b !== void 0 ? _b : null;\n        });\n    }\n    _initSupabaseAuthClient({ autoRefreshToken, persistSession, detectSessionInUrl, storage, storageKey, flowType, lock, debug, }, headers, fetch) {\n        const authHeaders = {\n            Authorization: `Bearer ${this.supabaseKey}`,\n            apikey: `${this.supabaseKey}`,\n        };\n        return new _lib_SupabaseAuthClient__WEBPACK_IMPORTED_MODULE_7__.SupabaseAuthClient({\n            url: this.authUrl.href,\n            headers: Object.assign(Object.assign({}, authHeaders), headers),\n            storageKey: storageKey,\n            autoRefreshToken,\n            persistSession,\n            detectSessionInUrl,\n            storage,\n            flowType,\n            lock,\n            debug,\n            fetch,\n            // auth checks if there is a custom authorizaiton header using this flag\n            // so it knows whether to return an error when getUser is called with no session\n            hasCustomAuthorizationHeader: 'Authorization' in this.headers,\n        });\n    }\n    _initRealtimeClient(options) {\n        return new _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_1__.RealtimeClient(this.realtimeUrl.href, Object.assign(Object.assign({}, options), { params: Object.assign({ apikey: this.supabaseKey }, options === null || options === void 0 ? void 0 : options.params) }));\n    }\n    _listenForAuthEvents() {\n        let data = this.auth.onAuthStateChange((event, session) => {\n            this._handleTokenChanged(event, 'CLIENT', session === null || session === void 0 ? void 0 : session.access_token);\n        });\n        return data;\n    }\n    _handleTokenChanged(event, source, token) {\n        if ((event === 'TOKEN_REFRESHED' || event === 'SIGNED_IN') &&\n            this.changedAccessToken !== token) {\n            this.changedAccessToken = token;\n        }\n        else if (event === 'SIGNED_OUT') {\n            this.realtime.setAuth();\n            if (source == 'STORAGE')\n                this.auth.signOut();\n            this.changedAccessToken = undefined;\n        }\n    }\n}\n//# sourceMappingURL=SupabaseClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.0/node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.0/node_modules/@supabase/supabase-js/dist/module/index.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.50.0/node_modules/@supabase/supabase-js/dist/module/index.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthAdminApi: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthAdminApi),\n/* harmony export */   AuthApiError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthApiError),\n/* harmony export */   AuthClient: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthClient),\n/* harmony export */   AuthError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthError),\n/* harmony export */   AuthImplicitGrantRedirectError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthImplicitGrantRedirectError),\n/* harmony export */   AuthInvalidCredentialsError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthInvalidCredentialsError),\n/* harmony export */   AuthInvalidJwtError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthInvalidJwtError),\n/* harmony export */   AuthInvalidTokenResponseError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthInvalidTokenResponseError),\n/* harmony export */   AuthPKCEGrantCodeExchangeError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthPKCEGrantCodeExchangeError),\n/* harmony export */   AuthRetryableFetchError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthRetryableFetchError),\n/* harmony export */   AuthSessionMissingError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthSessionMissingError),\n/* harmony export */   AuthUnknownError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthUnknownError),\n/* harmony export */   AuthWeakPasswordError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthWeakPasswordError),\n/* harmony export */   CustomAuthError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.CustomAuthError),\n/* harmony export */   FunctionRegion: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionRegion),\n/* harmony export */   FunctionsError: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionsError),\n/* harmony export */   FunctionsFetchError: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionsFetchError),\n/* harmony export */   FunctionsHttpError: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionsHttpError),\n/* harmony export */   FunctionsRelayError: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionsRelayError),\n/* harmony export */   GoTrueAdminApi: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.GoTrueAdminApi),\n/* harmony export */   GoTrueClient: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.GoTrueClient),\n/* harmony export */   NavigatorLockAcquireTimeoutError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.NavigatorLockAcquireTimeoutError),\n/* harmony export */   PostgrestError: () => (/* reexport safe */ _supabase_postgrest_js__WEBPACK_IMPORTED_MODULE_1__.PostgrestError),\n/* harmony export */   SIGN_OUT_SCOPES: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.SIGN_OUT_SCOPES),\n/* harmony export */   SupabaseClient: () => (/* reexport safe */ _SupabaseClient__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   isAuthApiError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthApiError),\n/* harmony export */   isAuthError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthError),\n/* harmony export */   isAuthImplicitGrantRedirectError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthImplicitGrantRedirectError),\n/* harmony export */   isAuthRetryableFetchError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthRetryableFetchError),\n/* harmony export */   isAuthSessionMissingError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthSessionMissingError),\n/* harmony export */   isAuthWeakPasswordError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthWeakPasswordError),\n/* harmony export */   lockInternals: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.lockInternals),\n/* harmony export */   navigatorLock: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.navigatorLock),\n/* harmony export */   processLock: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.processLock)\n/* harmony export */ });\n/* harmony import */ var _SupabaseClient__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./SupabaseClient */ \"(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.0/node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js\");\n/* harmony import */ var _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-js */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/index.js\");\n/* harmony import */ var _supabase_postgrest_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/postgrest-js */ \"(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs\");\n/* harmony import */ var _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/functions-js */ \"(ssr)/./node_modules/.pnpm/@supabase+functions-js@2.4.4/node_modules/@supabase/functions-js/dist/module/types.js\");\n/* harmony import */ var _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @supabase/realtime-js */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.10/node_modules/@supabase/realtime-js/dist/main/index.js\");\n/* harmony import */ var _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"PostgrestError\",\"FunctionsHttpError\",\"FunctionsFetchError\",\"FunctionsRelayError\",\"FunctionsError\",\"FunctionRegion\",\"SupabaseClient\",\"createClient\",\"AuthAdminApi\",\"AuthApiError\",\"AuthClient\",\"AuthError\",\"AuthImplicitGrantRedirectError\",\"AuthInvalidCredentialsError\",\"AuthInvalidJwtError\",\"AuthInvalidTokenResponseError\",\"AuthPKCEGrantCodeExchangeError\",\"AuthRetryableFetchError\",\"AuthSessionMissingError\",\"AuthUnknownError\",\"AuthWeakPasswordError\",\"CustomAuthError\",\"GoTrueAdminApi\",\"GoTrueClient\",\"NavigatorLockAcquireTimeoutError\",\"SIGN_OUT_SCOPES\",\"isAuthApiError\",\"isAuthError\",\"isAuthImplicitGrantRedirectError\",\"isAuthRetryableFetchError\",\"isAuthSessionMissingError\",\"isAuthWeakPasswordError\",\"lockInternals\",\"navigatorLock\",\"processLock\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n\n\n\n/**\n * Creates a new Supabase Client.\n */\nconst createClient = (supabaseUrl, supabaseKey, options) => {\n    return new _SupabaseClient__WEBPACK_IMPORTED_MODULE_4__[\"default\"](supabaseUrl, supabaseKey, options);\n};\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTAuMC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3N1cGFiYXNlLWpzL2Rpc3QvbW9kdWxlL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQThDO0FBQ1o7QUFDdUI7QUFDOEU7QUFDakc7QUFDdUI7QUFDN0Q7QUFDQTtBQUNBO0FBQ087QUFDUCxlQUFlLHVEQUFjO0FBQzdCO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbWVtZXRcXERlc2t0b3BcXGRvcm1fMjFcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZStzdXBhYmFzZS1qc0AyLjUwLjBcXG5vZGVfbW9kdWxlc1xcQHN1cGFiYXNlXFxzdXBhYmFzZS1qc1xcZGlzdFxcbW9kdWxlXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgU3VwYWJhc2VDbGllbnQgZnJvbSAnLi9TdXBhYmFzZUNsaWVudCc7XG5leHBvcnQgKiBmcm9tICdAc3VwYWJhc2UvYXV0aC1qcyc7XG5leHBvcnQgeyBQb3N0Z3Jlc3RFcnJvciwgfSBmcm9tICdAc3VwYWJhc2UvcG9zdGdyZXN0LWpzJztcbmV4cG9ydCB7IEZ1bmN0aW9uc0h0dHBFcnJvciwgRnVuY3Rpb25zRmV0Y2hFcnJvciwgRnVuY3Rpb25zUmVsYXlFcnJvciwgRnVuY3Rpb25zRXJyb3IsIEZ1bmN0aW9uUmVnaW9uLCB9IGZyb20gJ0BzdXBhYmFzZS9mdW5jdGlvbnMtanMnO1xuZXhwb3J0ICogZnJvbSAnQHN1cGFiYXNlL3JlYWx0aW1lLWpzJztcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU3VwYWJhc2VDbGllbnQgfSBmcm9tICcuL1N1cGFiYXNlQ2xpZW50Jztcbi8qKlxuICogQ3JlYXRlcyBhIG5ldyBTdXBhYmFzZSBDbGllbnQuXG4gKi9cbmV4cG9ydCBjb25zdCBjcmVhdGVDbGllbnQgPSAoc3VwYWJhc2VVcmwsIHN1cGFiYXNlS2V5LCBvcHRpb25zKSA9PiB7XG4gICAgcmV0dXJuIG5ldyBTdXBhYmFzZUNsaWVudChzdXBhYmFzZVVybCwgc3VwYWJhc2VLZXksIG9wdGlvbnMpO1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.0/node_modules/@supabase/supabase-js/dist/module/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.0/node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.50.0/node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseAuthClient: () => (/* binding */ SupabaseAuthClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-js */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/index.js\");\n\nclass SupabaseAuthClient extends _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthClient {\n    constructor(options) {\n        super(options);\n    }\n}\n//# sourceMappingURL=SupabaseAuthClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTAuMC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3N1cGFiYXNlLWpzL2Rpc3QvbW9kdWxlL2xpYi9TdXBhYmFzZUF1dGhDbGllbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBK0M7QUFDeEMsaUNBQWlDLHlEQUFVO0FBQ2xEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbWVtZXRcXERlc2t0b3BcXGRvcm1fMjFcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZStzdXBhYmFzZS1qc0AyLjUwLjBcXG5vZGVfbW9kdWxlc1xcQHN1cGFiYXNlXFxzdXBhYmFzZS1qc1xcZGlzdFxcbW9kdWxlXFxsaWJcXFN1cGFiYXNlQXV0aENsaWVudC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBdXRoQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL2F1dGgtanMnO1xuZXhwb3J0IGNsYXNzIFN1cGFiYXNlQXV0aENsaWVudCBleHRlbmRzIEF1dGhDbGllbnQge1xuICAgIGNvbnN0cnVjdG9yKG9wdGlvbnMpIHtcbiAgICAgICAgc3VwZXIob3B0aW9ucyk7XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9U3VwYWJhc2VBdXRoQ2xpZW50LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.0/node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.0/node_modules/@supabase/supabase-js/dist/module/lib/constants.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.50.0/node_modules/@supabase/supabase-js/dist/module/lib/constants.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_AUTH_OPTIONS: () => (/* binding */ DEFAULT_AUTH_OPTIONS),\n/* harmony export */   DEFAULT_DB_OPTIONS: () => (/* binding */ DEFAULT_DB_OPTIONS),\n/* harmony export */   DEFAULT_GLOBAL_OPTIONS: () => (/* binding */ DEFAULT_GLOBAL_OPTIONS),\n/* harmony export */   DEFAULT_HEADERS: () => (/* binding */ DEFAULT_HEADERS),\n/* harmony export */   DEFAULT_REALTIME_OPTIONS: () => (/* binding */ DEFAULT_REALTIME_OPTIONS)\n/* harmony export */ });\n/* harmony import */ var _version__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./version */ \"(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.0/node_modules/@supabase/supabase-js/dist/module/lib/version.js\");\n\nlet JS_ENV = '';\n// @ts-ignore\nif (typeof Deno !== 'undefined') {\n    JS_ENV = 'deno';\n}\nelse if (typeof document !== 'undefined') {\n    JS_ENV = 'web';\n}\nelse if (typeof navigator !== 'undefined' && navigator.product === 'ReactNative') {\n    JS_ENV = 'react-native';\n}\nelse {\n    JS_ENV = 'node';\n}\nconst DEFAULT_HEADERS = { 'X-Client-Info': `supabase-js-${JS_ENV}/${_version__WEBPACK_IMPORTED_MODULE_0__.version}` };\nconst DEFAULT_GLOBAL_OPTIONS = {\n    headers: DEFAULT_HEADERS,\n};\nconst DEFAULT_DB_OPTIONS = {\n    schema: 'public',\n};\nconst DEFAULT_AUTH_OPTIONS = {\n    autoRefreshToken: true,\n    persistSession: true,\n    detectSessionInUrl: true,\n    flowType: 'implicit',\n};\nconst DEFAULT_REALTIME_OPTIONS = {};\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTAuMC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3N1cGFiYXNlLWpzL2Rpc3QvbW9kdWxlL2xpYi9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQW9DO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTywwQkFBMEIsZ0NBQWdDLE9BQU8sR0FBRyw2Q0FBTyxDQUFDO0FBQzVFO0FBQ1A7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1AiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbWVtZXRcXERlc2t0b3BcXGRvcm1fMjFcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZStzdXBhYmFzZS1qc0AyLjUwLjBcXG5vZGVfbW9kdWxlc1xcQHN1cGFiYXNlXFxzdXBhYmFzZS1qc1xcZGlzdFxcbW9kdWxlXFxsaWJcXGNvbnN0YW50cy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB2ZXJzaW9uIH0gZnJvbSAnLi92ZXJzaW9uJztcbmxldCBKU19FTlYgPSAnJztcbi8vIEB0cy1pZ25vcmVcbmlmICh0eXBlb2YgRGVubyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICBKU19FTlYgPSAnZGVubyc7XG59XG5lbHNlIGlmICh0eXBlb2YgZG9jdW1lbnQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgSlNfRU5WID0gJ3dlYic7XG59XG5lbHNlIGlmICh0eXBlb2YgbmF2aWdhdG9yICE9PSAndW5kZWZpbmVkJyAmJiBuYXZpZ2F0b3IucHJvZHVjdCA9PT0gJ1JlYWN0TmF0aXZlJykge1xuICAgIEpTX0VOViA9ICdyZWFjdC1uYXRpdmUnO1xufVxuZWxzZSB7XG4gICAgSlNfRU5WID0gJ25vZGUnO1xufVxuZXhwb3J0IGNvbnN0IERFRkFVTFRfSEVBREVSUyA9IHsgJ1gtQ2xpZW50LUluZm8nOiBgc3VwYWJhc2UtanMtJHtKU19FTlZ9LyR7dmVyc2lvbn1gIH07XG5leHBvcnQgY29uc3QgREVGQVVMVF9HTE9CQUxfT1BUSU9OUyA9IHtcbiAgICBoZWFkZXJzOiBERUZBVUxUX0hFQURFUlMsXG59O1xuZXhwb3J0IGNvbnN0IERFRkFVTFRfREJfT1BUSU9OUyA9IHtcbiAgICBzY2hlbWE6ICdwdWJsaWMnLFxufTtcbmV4cG9ydCBjb25zdCBERUZBVUxUX0FVVEhfT1BUSU9OUyA9IHtcbiAgICBhdXRvUmVmcmVzaFRva2VuOiB0cnVlLFxuICAgIHBlcnNpc3RTZXNzaW9uOiB0cnVlLFxuICAgIGRldGVjdFNlc3Npb25JblVybDogdHJ1ZSxcbiAgICBmbG93VHlwZTogJ2ltcGxpY2l0Jyxcbn07XG5leHBvcnQgY29uc3QgREVGQVVMVF9SRUFMVElNRV9PUFRJT05TID0ge307XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jb25zdGFudHMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.0/node_modules/@supabase/supabase-js/dist/module/lib/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.0/node_modules/@supabase/supabase-js/dist/module/lib/fetch.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.50.0/node_modules/@supabase/supabase-js/dist/module/lib/fetch.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetchWithAuth: () => (/* binding */ fetchWithAuth),\n/* harmony export */   resolveFetch: () => (/* binding */ resolveFetch),\n/* harmony export */   resolveHeadersConstructor: () => (/* binding */ resolveHeadersConstructor)\n/* harmony export */ });\n/* harmony import */ var _supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/node-fetch */ \"(ssr)/./node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/lib/index.js\");\n/* harmony import */ var _supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0__);\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n// @ts-ignore\n\nconst resolveFetch = (customFetch) => {\n    let _fetch;\n    if (customFetch) {\n        _fetch = customFetch;\n    }\n    else if (typeof fetch === 'undefined') {\n        _fetch = (_supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0___default());\n    }\n    else {\n        _fetch = fetch;\n    }\n    return (...args) => _fetch(...args);\n};\nconst resolveHeadersConstructor = () => {\n    if (typeof Headers === 'undefined') {\n        return _supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0__.Headers;\n    }\n    return Headers;\n};\nconst fetchWithAuth = (supabaseKey, getAccessToken, customFetch) => {\n    const fetch = resolveFetch(customFetch);\n    const HeadersConstructor = resolveHeadersConstructor();\n    return (input, init) => __awaiter(void 0, void 0, void 0, function* () {\n        var _a;\n        const accessToken = (_a = (yield getAccessToken())) !== null && _a !== void 0 ? _a : supabaseKey;\n        let headers = new HeadersConstructor(init === null || init === void 0 ? void 0 : init.headers);\n        if (!headers.has('apikey')) {\n            headers.set('apikey', supabaseKey);\n        }\n        if (!headers.has('Authorization')) {\n            headers.set('Authorization', `Bearer ${accessToken}`);\n        }\n        return fetch(input, Object.assign(Object.assign({}, init), { headers }));\n    });\n};\n//# sourceMappingURL=fetch.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.0/node_modules/@supabase/supabase-js/dist/module/lib/fetch.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.0/node_modules/@supabase/supabase-js/dist/module/lib/helpers.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.50.0/node_modules/@supabase/supabase-js/dist/module/lib/helpers.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applySettingDefaults: () => (/* binding */ applySettingDefaults),\n/* harmony export */   ensureTrailingSlash: () => (/* binding */ ensureTrailingSlash),\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   uuid: () => (/* binding */ uuid)\n/* harmony export */ });\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nfunction uuid() {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n        var r = (Math.random() * 16) | 0, v = c == 'x' ? r : (r & 0x3) | 0x8;\n        return v.toString(16);\n    });\n}\nfunction ensureTrailingSlash(url) {\n    return url.endsWith('/') ? url : url + '/';\n}\nconst isBrowser = () => typeof window !== 'undefined';\nfunction applySettingDefaults(options, defaults) {\n    var _a, _b;\n    const { db: dbOptions, auth: authOptions, realtime: realtimeOptions, global: globalOptions, } = options;\n    const { db: DEFAULT_DB_OPTIONS, auth: DEFAULT_AUTH_OPTIONS, realtime: DEFAULT_REALTIME_OPTIONS, global: DEFAULT_GLOBAL_OPTIONS, } = defaults;\n    const result = {\n        db: Object.assign(Object.assign({}, DEFAULT_DB_OPTIONS), dbOptions),\n        auth: Object.assign(Object.assign({}, DEFAULT_AUTH_OPTIONS), authOptions),\n        realtime: Object.assign(Object.assign({}, DEFAULT_REALTIME_OPTIONS), realtimeOptions),\n        global: Object.assign(Object.assign(Object.assign({}, DEFAULT_GLOBAL_OPTIONS), globalOptions), { headers: Object.assign(Object.assign({}, ((_a = DEFAULT_GLOBAL_OPTIONS === null || DEFAULT_GLOBAL_OPTIONS === void 0 ? void 0 : DEFAULT_GLOBAL_OPTIONS.headers) !== null && _a !== void 0 ? _a : {})), ((_b = globalOptions === null || globalOptions === void 0 ? void 0 : globalOptions.headers) !== null && _b !== void 0 ? _b : {})) }),\n        accessToken: () => __awaiter(this, void 0, void 0, function* () { return ''; }),\n    };\n    if (options.accessToken) {\n        result.accessToken = options.accessToken;\n    }\n    else {\n        // hack around Required<>\n        delete result.accessToken;\n    }\n    return result;\n}\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.0/node_modules/@supabase/supabase-js/dist/module/lib/helpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.0/node_modules/@supabase/supabase-js/dist/module/lib/version.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.50.0/node_modules/@supabase/supabase-js/dist/module/lib/version.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\nconst version = '2.50.0';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTAuMC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3N1cGFiYXNlLWpzL2Rpc3QvbW9kdWxlL2xpYi92ZXJzaW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG1lbWV0XFxEZXNrdG9wXFxkb3JtXzIxXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAc3VwYWJhc2Urc3VwYWJhc2UtanNAMi41MC4wXFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxcc3VwYWJhc2UtanNcXGRpc3RcXG1vZHVsZVxcbGliXFx2ZXJzaW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCB2ZXJzaW9uID0gJzIuNTAuMCc7XG4vLyMgc291cmNlTWFwcGluZ1VSTD12ZXJzaW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.0/node_modules/@supabase/supabase-js/dist/module/lib/version.js\n");

/***/ })

};
;