{"version": 3, "sources": ["../../../src/client/dev/fouc.ts"], "sourcesContent": ["// This wrapper function is used to safely select the best available function\n// to schedule removal of the no-FOUC styles workaround. requestAnimationFrame\n// is the ideal choice, but when used in iframes, there are no guarantees that\n// the callback will actually be called, which could stall the promise returned\n// from displayContent.\n//\n// See: https://www.vector-logic.com/blog/posts/on-request-animation-frame-and-embedded-iframes\nconst safeCallbackQueue = (callback: () => void) => {\n  if (window.requestAnimationFrame && window.self === window.top) {\n    window.requestAnimationFrame(callback)\n  } else {\n    window.setTimeout(callback)\n  }\n}\n\n// This function is used to remove Next.js' no-FOUC styles workaround for using\n// `style-loader` in development. It must be called before hydration, or else\n// rendering won't have the correct computed values in effects.\nexport function displayContent(): Promise<void> {\n  return new Promise((resolve) => {\n    safeCallbackQueue(function () {\n      for (\n        var x = document.querySelectorAll('[data-next-hide-fouc]'),\n          i = x.length;\n        i--;\n\n      ) {\n        x[i].parentNode!.removeChild(x[i])\n      }\n      resolve()\n    })\n  })\n}\n"], "names": ["displayContent", "safeCallback<PERSON><PERSON>ue", "callback", "window", "requestAnimationFrame", "self", "top", "setTimeout", "Promise", "resolve", "x", "document", "querySelectorAll", "i", "length", "parentNode", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAAA,6EAA6E;AAC7E,8EAA8E;AAC9E,8EAA8E;AAC9E,+EAA+E;AAC/E,uBAAuB;AACvB,EAAE;AACF,+FAA+F;;;;;+BAY/EA;;;eAAAA;;;AAXhB,MAAMC,oBAAoB,CAACC;IACzB,IAAIC,OAAOC,qBAAqB,IAAID,OAAOE,IAAI,KAAKF,OAAOG,GAAG,EAAE;QAC9DH,OAAOC,qBAAqB,CAACF;IAC/B,OAAO;QACLC,OAAOI,UAAU,CAACL;IACpB;AACF;AAKO,SAASF;IACd,OAAO,IAAIQ,QAAQ,CAACC;QAClBR,kBAAkB;YAChB,IACE,IAAIS,IAAIC,SAASC,gBAAgB,CAAC,0BAChCC,IAAIH,EAAEI,MAAM,EACdD,KAEA;gBACAH,CAAC,CAACG,EAAE,CAACE,UAAU,CAAEC,WAAW,CAACN,CAAC,CAACG,EAAE;YACnC;YACAJ;QACF;IACF;AACF"}