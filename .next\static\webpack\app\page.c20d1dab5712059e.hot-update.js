"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useSupabaseData.tsx":
/*!***************************************!*\
  !*** ./src/hooks/useSupabaseData.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useSupabaseData)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/src/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _src_lib_subscriptionManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/src/lib/subscriptionManager */ \"(app-pages-browser)/./src/lib/subscriptionManager.ts\");\n/* harmony import */ var _src_lib_machineStatusManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/lib/machineStatusManager */ \"(app-pages-browser)/./src/lib/machineStatusManager.ts\");\n/* harmony import */ var _src_utils_parseDuration__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/src/utils/parseDuration */ \"(app-pages-browser)/./src/utils/parseDuration.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\n\n\n\n\n// Helper functions to convert between DB and app formats\nconst dbToMachine = (row)=>{\n    const graceEndAt = row.grace_end_at ? new Date(row.grace_end_at) : row.status === \"finishedGrace\" && row.end_at ? new Date(new Date(row.end_at).getTime() + 5 * 60 * 1000) : undefined;\n    return {\n        id: row.id,\n        name: row.name,\n        status: row.status,\n        startAt: row.start_at ? new Date(row.start_at) : undefined,\n        endAt: row.end_at ? new Date(row.end_at) : undefined,\n        graceEndAt: graceEndAt,\n        updatedAt: new Date(row.updated_at)\n    };\n};\nconst dbToNoise = (row)=>({\n        id: row.id,\n        user: row.user_name,\n        description: row.description || \"Noise reported\",\n        timestamp: new Date(row.timestamp),\n        lastReported: new Date(row.last_reported)\n    });\nconst dbToAnnouncement = (row)=>({\n        id: row.id,\n        user: row.user_name,\n        title: row.title,\n        description: row.description,\n        type: row.announcement_type,\n        timestamp: new Date(row.timestamp)\n    });\nconst dbToSublet = (row)=>({\n        id: row.id,\n        user: row.user_name,\n        duration: row.duration,\n        timestamp: new Date(row.timestamp)\n    });\nconst dbToHelpMe = (row)=>({\n        id: row.id,\n        user: row.user_name,\n        description: row.description,\n        timestamp: new Date(row.timestamp)\n    });\nconst dbToIncident = (row)=>({\n        id: row.id,\n        machineId: row.machine_id,\n        timestamp: new Date(row.timestamp),\n        type: row.incident_type\n    });\nfunction useSupabaseData() {\n    _s();\n    const [laundry, setLaundry] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [noise, setNoise] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [announcements, setAnnouncements] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [helpMe, setHelpMe] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [incidents, setIncidents] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [hasGraceEndColumn, setHasGraceEndColumn] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    // Refs to prevent recursive calls and multiple setups\n    const isLoadingDataRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const lastLoadTimeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const subscriptionSetupRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const statusManagerSetupRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const isMountedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(true);\n    // Debounced data loading function with selective loading\n    const loadAllData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[loadAllData]\": async function(specificTable) {\n            let forceRefresh = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n            const now = Date.now();\n            // Skip debounce for manual refresh (forceRefresh = true)\n            if (!forceRefresh && (isLoadingDataRef.current || now - lastLoadTimeRef.current < 1000)) {\n                console.log(\"⏭️ Skipping data load (too recent or in progress)\");\n                return;\n            }\n            if (!isMountedRef.current) {\n                console.log(\"⏭️ Skipping data load (component unmounted)\");\n                return;\n            }\n            isLoadingDataRef.current = true;\n            lastLoadTimeRef.current = now;\n            try {\n                console.log(\"\\uD83D\\uDD04 Loading data from Supabase\".concat(specificTable ? \" (\".concat(specificTable, \")\") : \"\", \"...\"));\n                setError(null);\n                // If specific table is provided, only load that table\n                if (specificTable) {\n                    await loadSpecificTable(specificTable);\n                } else {\n                    // Load all data\n                    setIsLoading(true);\n                    await loadAllTables();\n                }\n                console.log(\"✅ Data loaded successfully\");\n            } catch (err) {\n                console.error(\"❌ Error loading data:\", err);\n                if (isMountedRef.current) {\n                    setError(err instanceof Error ? err.message : \"Failed to load data\");\n                }\n            } finally{\n                if (isMountedRef.current && !specificTable) {\n                    setIsLoading(false);\n                }\n                isLoadingDataRef.current = false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[loadAllData]\"], []);\n    // Helper function to load specific table\n    const loadSpecificTable = async (table)=>{\n        switch(table){\n            case \"machines\":\n                var _machinesResult_data;\n                const machinesResult = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").select(\"*\").order(\"name\");\n                if (machinesResult.error) throw machinesResult.error;\n                setLaundry(((_machinesResult_data = machinesResult.data) === null || _machinesResult_data === void 0 ? void 0 : _machinesResult_data.map(dbToMachine)) || []);\n                break;\n            case \"noise_reports\":\n                var _noiseResult_data;\n                const noiseResult = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"noise_reports\").select(\"*\").order(\"timestamp\", {\n                    ascending: false\n                }).limit(50);\n                if (noiseResult.error) throw noiseResult.error;\n                setNoise(((_noiseResult_data = noiseResult.data) === null || _noiseResult_data === void 0 ? void 0 : _noiseResult_data.map(dbToNoise)) || []);\n                break;\n            case \"announcements\":\n                var _announcementsResult_data;\n                const announcementsResult = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"announcements\").select(\"*\").order(\"timestamp\", {\n                    ascending: false\n                }).limit(50);\n                if (announcementsResult.error) throw announcementsResult.error;\n                setAnnouncements(((_announcementsResult_data = announcementsResult.data) === null || _announcementsResult_data === void 0 ? void 0 : _announcementsResult_data.map(dbToAnnouncement)) || []);\n                break;\n            case \"help_requests\":\n                var _helpResult_data;\n                const helpResult = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"help_requests\").select(\"*\").gte(\"timestamp\", new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()).order(\"timestamp\", {\n                    ascending: false\n                }).limit(50);\n                if (helpResult.error) throw helpResult.error;\n                setHelpMe(((_helpResult_data = helpResult.data) === null || _helpResult_data === void 0 ? void 0 : _helpResult_data.map(dbToHelpMe)) || []);\n                break;\n            case \"incidents\":\n                var _incidentsResult_data;\n                const incidentsResult = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"incidents\").select(\"*\").order(\"timestamp\", {\n                    ascending: false\n                }).limit(50);\n                if (incidentsResult.error) throw incidentsResult.error;\n                setIncidents(((_incidentsResult_data = incidentsResult.data) === null || _incidentsResult_data === void 0 ? void 0 : _incidentsResult_data.map(dbToIncident)) || []);\n                break;\n        }\n    };\n    // Helper function to load all tables\n    const loadAllTables = async ()=>{\n        var _machinesResult_data, _noiseResult_data, _announcementsResult_data, _helpResult_data, _incidentsResult_data;\n        const timeoutPromise = new Promise((_, reject)=>setTimeout(()=>reject(new Error(\"Request timeout\")), 15000));\n        const dataPromise = Promise.all([\n            _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").select(\"*\").order(\"name\"),\n            _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"noise_reports\").select(\"*\").order(\"timestamp\", {\n                ascending: false\n            }).limit(50),\n            _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"announcements\").select(\"*\").order(\"timestamp\", {\n                ascending: false\n            }).limit(50),\n            _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"help_requests\").select(\"*\").gte(\"timestamp\", new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()).order(\"timestamp\", {\n                ascending: false\n            }).limit(50),\n            _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"incidents\").select(\"*\").order(\"timestamp\", {\n                ascending: false\n            }).limit(50)\n        ]);\n        const [machinesResult, noiseResult, announcementsResult, helpResult, incidentsResult] = await Promise.race([\n            dataPromise,\n            timeoutPromise\n        ]);\n        if (!isMountedRef.current) return;\n        if (machinesResult.error) throw new Error(\"Failed to load machines: \".concat(machinesResult.error.message));\n        if (noiseResult.error) throw new Error(\"Failed to load noise reports: \".concat(noiseResult.error.message));\n        if (announcementsResult.error) throw new Error(\"Failed to load announcements: \".concat(announcementsResult.error.message));\n        if (helpResult.error) throw new Error(\"Failed to load help requests: \".concat(helpResult.error.message));\n        if (incidentsResult.error) throw new Error(\"Failed to load incidents: \".concat(incidentsResult.error.message));\n        setLaundry(((_machinesResult_data = machinesResult.data) === null || _machinesResult_data === void 0 ? void 0 : _machinesResult_data.map(dbToMachine)) || []);\n        setNoise(((_noiseResult_data = noiseResult.data) === null || _noiseResult_data === void 0 ? void 0 : _noiseResult_data.map(dbToNoise)) || []);\n        setAnnouncements(((_announcementsResult_data = announcementsResult.data) === null || _announcementsResult_data === void 0 ? void 0 : _announcementsResult_data.map(dbToAnnouncement)) || []);\n        setHelpMe(((_helpResult_data = helpResult.data) === null || _helpResult_data === void 0 ? void 0 : _helpResult_data.map(dbToHelpMe)) || []);\n        setIncidents(((_incidentsResult_data = incidentsResult.data) === null || _incidentsResult_data === void 0 ? void 0 : _incidentsResult_data.map(dbToIncident)) || []);\n    };\n    // Set up real-time subscriptions with table-specific updates\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseData.useEffect\": ()=>{\n            if (subscriptionSetupRef.current) {\n                return;\n            }\n            subscriptionSetupRef.current = true;\n            console.log(\"🔄 Setting up subscription manager...\");\n            const subscriptionManager = _src_lib_subscriptionManager__WEBPACK_IMPORTED_MODULE_2__[\"default\"].getInstance();\n            // Enhanced callback that handles specific table updates\n            const handleRealtimeUpdate = {\n                \"useSupabaseData.useEffect.handleRealtimeUpdate\": (table, event)=>{\n                    if (table && table !== \"polling\") {\n                        // Load only the specific table that changed for faster updates\n                        loadAllData(table);\n                    } else {\n                        // Fallback to loading all data\n                        loadAllData();\n                    }\n                }\n            }[\"useSupabaseData.useEffect.handleRealtimeUpdate\"];\n            subscriptionManager.addCallback(handleRealtimeUpdate);\n            return ({\n                \"useSupabaseData.useEffect\": ()=>{\n                    console.log(\"🧹 Cleaning up subscription manager...\");\n                    subscriptionManager.removeCallback(handleRealtimeUpdate);\n                    subscriptionSetupRef.current = false;\n                }\n            })[\"useSupabaseData.useEffect\"];\n        }\n    }[\"useSupabaseData.useEffect\"], [\n        loadAllData\n    ]);\n    // Set up machine status monitoring (only once)\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseData.useEffect\": ()=>{\n            if (statusManagerSetupRef.current) {\n                return;\n            }\n            statusManagerSetupRef.current = true;\n            console.log(\"🔄 Setting up machine status manager...\");\n            const statusManager = _src_lib_machineStatusManager__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getInstance();\n            statusManager.startStatusMonitoring();\n            return ({\n                \"useSupabaseData.useEffect\": ()=>{\n                    console.log(\"🧹 Cleaning up machine status manager...\");\n                    statusManager.stopStatusMonitoring();\n                    statusManagerSetupRef.current = false;\n                }\n            })[\"useSupabaseData.useEffect\"];\n        }\n    }[\"useSupabaseData.useEffect\"], []);\n    // Initial data load and cleanup\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseData.useEffect\": ()=>{\n            isMountedRef.current = true;\n            loadAllData();\n            return ({\n                \"useSupabaseData.useEffect\": ()=>{\n                    isMountedRef.current = false;\n                }\n            })[\"useSupabaseData.useEffect\"];\n        }\n    }[\"useSupabaseData.useEffect\"], []) // Only run once on mount\n    ;\n    // Machine operations with optimistic updates\n    const toggleMachineStatus = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[toggleMachineStatus]\": async (id)=>{\n            try {\n                const machine = laundry.find({\n                    \"useSupabaseData.useCallback[toggleMachineStatus].machine\": (m)=>m.id === id\n                }[\"useSupabaseData.useCallback[toggleMachineStatus].machine\"]);\n                if (!machine) {\n                    return false;\n                }\n                let optimisticUpdate;\n                let updateData;\n                if (machine.status === \"free\") {\n                    const startAt = new Date();\n                    const endAt = new Date(startAt.getTime() + 60 * 60 * 1000);\n                    optimisticUpdate = {\n                        ...machine,\n                        status: \"running\",\n                        startAt,\n                        endAt,\n                        graceEndAt: undefined,\n                        updatedAt: new Date()\n                    };\n                    updateData = {\n                        status: \"running\",\n                        start_at: startAt.toISOString(),\n                        end_at: endAt.toISOString()\n                    };\n                    if (hasGraceEndColumn) {\n                        updateData.grace_end_at = null;\n                    }\n                } else {\n                    optimisticUpdate = {\n                        ...machine,\n                        status: \"free\",\n                        startAt: undefined,\n                        endAt: undefined,\n                        graceEndAt: undefined,\n                        updatedAt: new Date()\n                    };\n                    updateData = {\n                        status: \"free\",\n                        start_at: null,\n                        end_at: null\n                    };\n                    if (hasGraceEndColumn) {\n                        updateData.grace_end_at = null;\n                    }\n                }\n                // Apply optimistic update\n                setLaundry({\n                    \"useSupabaseData.useCallback[toggleMachineStatus]\": (prev)=>prev.map({\n                            \"useSupabaseData.useCallback[toggleMachineStatus]\": (m)=>m.id === id ? optimisticUpdate : m\n                        }[\"useSupabaseData.useCallback[toggleMachineStatus]\"])\n                }[\"useSupabaseData.useCallback[toggleMachineStatus]\"]);\n                // Send update to database\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").update(updateData).eq(\"id\", id);\n                if (error) {\n                    if (error.message && error.message.includes(\"grace_end_at\")) {\n                        setHasGraceEndColumn(false);\n                        delete updateData.grace_end_at;\n                        const { error: retryError } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").update(updateData).eq(\"id\", id);\n                        if (retryError) {\n                            setLaundry({\n                                \"useSupabaseData.useCallback[toggleMachineStatus]\": (prev)=>prev.map({\n                                        \"useSupabaseData.useCallback[toggleMachineStatus]\": (m)=>m.id === id ? machine : m\n                                    }[\"useSupabaseData.useCallback[toggleMachineStatus]\"])\n                            }[\"useSupabaseData.useCallback[toggleMachineStatus]\"]);\n                            throw retryError;\n                        }\n                    } else {\n                        setLaundry({\n                            \"useSupabaseData.useCallback[toggleMachineStatus]\": (prev)=>prev.map({\n                                    \"useSupabaseData.useCallback[toggleMachineStatus]\": (m)=>m.id === id ? machine : m\n                                }[\"useSupabaseData.useCallback[toggleMachineStatus]\"])\n                        }[\"useSupabaseData.useCallback[toggleMachineStatus]\"]);\n                        throw error;\n                    }\n                }\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to update machine\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[toggleMachineStatus]\"], [\n        laundry,\n        hasGraceEndColumn\n    ]);\n    const reserveMachine = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[reserveMachine]\": async (id, durationStr)=>{\n            try {\n                const machine = laundry.find({\n                    \"useSupabaseData.useCallback[reserveMachine].machine\": (m)=>m.id === id\n                }[\"useSupabaseData.useCallback[reserveMachine].machine\"]);\n                if (!machine) {\n                    return false;\n                }\n                const durationSeconds = (0,_src_utils_parseDuration__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(durationStr);\n                const startAt = new Date();\n                const endAt = new Date(startAt.getTime() + durationSeconds * 1000);\n                const optimisticUpdate = {\n                    ...machine,\n                    status: \"running\",\n                    startAt,\n                    endAt,\n                    graceEndAt: undefined,\n                    updatedAt: new Date()\n                };\n                setLaundry({\n                    \"useSupabaseData.useCallback[reserveMachine]\": (prev)=>prev.map({\n                            \"useSupabaseData.useCallback[reserveMachine]\": (m)=>m.id === id ? optimisticUpdate : m\n                        }[\"useSupabaseData.useCallback[reserveMachine]\"])\n                }[\"useSupabaseData.useCallback[reserveMachine]\"]);\n                const updateData = {\n                    status: \"running\",\n                    start_at: startAt.toISOString(),\n                    end_at: endAt.toISOString()\n                };\n                if (hasGraceEndColumn) {\n                    updateData.grace_end_at = null;\n                }\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").update(updateData).eq(\"id\", id);\n                if (error) {\n                    if (error.message && error.message.includes(\"grace_end_at\")) {\n                        setHasGraceEndColumn(false);\n                        delete updateData.grace_end_at;\n                        const { error: retryError } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").update(updateData).eq(\"id\", id);\n                        if (retryError) {\n                            setLaundry({\n                                \"useSupabaseData.useCallback[reserveMachine]\": (prev)=>prev.map({\n                                        \"useSupabaseData.useCallback[reserveMachine]\": (m)=>m.id === id ? machine : m\n                                    }[\"useSupabaseData.useCallback[reserveMachine]\"])\n                            }[\"useSupabaseData.useCallback[reserveMachine]\"]);\n                            throw retryError;\n                        }\n                    } else {\n                        setLaundry({\n                            \"useSupabaseData.useCallback[reserveMachine]\": (prev)=>prev.map({\n                                    \"useSupabaseData.useCallback[reserveMachine]\": (m)=>m.id === id ? machine : m\n                                }[\"useSupabaseData.useCallback[reserveMachine]\"])\n                        }[\"useSupabaseData.useCallback[reserveMachine]\"]);\n                        throw error;\n                    }\n                }\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to reserve machine\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[reserveMachine]\"], [\n        laundry,\n        hasGraceEndColumn\n    ]);\n    // Simplified CRUD operations\n    const addNoiseWithDescription = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[addNoiseWithDescription]\": async (user, description)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"noise_reports\").insert({\n                    id: Date.now().toString(),\n                    user_name: user,\n                    description: description\n                });\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to add noise report\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[addNoiseWithDescription]\"], []);\n    const deleteNoise = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[deleteNoise]\": async (id)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"noise_reports\").delete().eq(\"id\", id);\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to delete noise report\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[deleteNoise]\"], []);\n    const addAnnouncement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[addAnnouncement]\": async (user, title, description, type)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"announcements\").insert({\n                    id: Date.now().toString(),\n                    user_name: user,\n                    title: title,\n                    description: description,\n                    announcement_type: type\n                });\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to add announcement\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[addAnnouncement]\"], []);\n    const deleteAnnouncement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[deleteAnnouncement]\": async (id)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"announcements\").delete().eq(\"id\", id);\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to delete announcement\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[deleteAnnouncement]\"], []);\n    const addHelpRequest = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[addHelpRequest]\": async (user, description)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"help_requests\").insert({\n                    id: Date.now().toString(),\n                    user_name: user,\n                    description\n                });\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to add help request\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[addHelpRequest]\"], []);\n    const deleteHelpRequest = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[deleteHelpRequest]\": async (id)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"help_requests\").delete().eq(\"id\", id);\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to delete help request\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[deleteHelpRequest]\"], []);\n    const deleteIncident = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[deleteIncident]\": async (id)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"incidents\").delete().eq(\"id\", id);\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to delete incident\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[deleteIncident]\"], []);\n    return {\n        // Data\n        laundry,\n        noise,\n        announcements,\n        helpMe,\n        incidents,\n        // State\n        isLoading,\n        error,\n        // Operations\n        toggleMachineStatus,\n        reserveMachine,\n        addNoiseWithDescription,\n        deleteNoise,\n        addAnnouncement,\n        deleteAnnouncement,\n        addHelpRequest,\n        deleteHelpRequest,\n        deleteIncident,\n        refreshData: loadAllData\n    };\n}\n_s(useSupabaseData, \"rddSyZFV65FZSCcOrY4YjCvlN0M=\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useSupabaseData.tsx\n"));

/***/ })

});