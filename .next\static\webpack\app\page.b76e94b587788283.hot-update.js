"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/machineStatusManager.ts":
/*!*****************************************!*\
  !*** ./src/lib/machineStatusManager.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n\nclass MachineStatusManager {\n    static getInstance() {\n        if (!MachineStatusManager.instance) {\n            MachineStatusManager.instance = new MachineStatusManager();\n        }\n        return MachineStatusManager.instance;\n    }\n    startStatusMonitoring() {\n        if (this.statusCheckInterval || this.isDestroyed) {\n            return;\n        }\n        console.log(\"🔄 Starting machine status monitoring...\");\n        // Check every 30 seconds (less aggressive)\n        this.statusCheckInterval = setInterval(()=>{\n            if (!this.isDestroyed) {\n                this.checkMachineStatuses();\n            }\n        }, 30000);\n        // Also check immediately\n        this.checkMachineStatuses();\n    }\n    stopStatusMonitoring() {\n        if (this.statusCheckInterval) {\n            clearInterval(this.statusCheckInterval);\n            this.statusCheckInterval = null;\n            console.log(\"⏹️ Stopped machine status monitoring\");\n        }\n    }\n    destroy() {\n        this.isDestroyed = true;\n        this.stopStatusMonitoring();\n    }\n    async checkMachineStatuses() {\n        if (this.isChecking || this.isDestroyed) {\n            return;\n        }\n        this.isChecking = true;\n        try {\n            const now = new Date();\n            const { data: machines, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"machines\").select(\"*\").in(\"status\", [\n                \"running\",\n                \"finishedGrace\"\n            ]);\n            if (error) {\n                console.error(\"❌ Error fetching machines for status check:\", error);\n                return;\n            }\n            if (!machines || machines.length === 0) {\n                return;\n            }\n            const updates = [];\n            for (const machine of machines){\n                if (this.isDestroyed) break;\n                const endAt = machine.end_at ? new Date(machine.end_at) : null;\n                let graceEndAt = null;\n                if (this.hasGraceEndColumn && machine.grace_end_at) {\n                    graceEndAt = new Date(machine.grace_end_at);\n                }\n                if (machine.status === \"running\" && endAt && now >= endAt) {\n                    const graceEnd = new Date(now.getTime() + 5 * 60 * 1000);\n                    const updateObj = {\n                        status: \"finishedGrace\"\n                    };\n                    if (this.hasGraceEndColumn) {\n                        updateObj.grace_end_at = graceEnd.toISOString();\n                    }\n                    updates.push({\n                        id: machine.id,\n                        updates: updateObj\n                    });\n                    console.log(\"⚠️ Machine \".concat(machine.name, \" transitioning to grace period\"));\n                } else if (machine.status === \"finishedGrace\") {\n                    const defaultGraceEnd = endAt ? new Date(endAt.getTime() + 5 * 60 * 1000) : null;\n                    const effectiveGraceEnd = graceEndAt || defaultGraceEnd;\n                    if (effectiveGraceEnd && now >= effectiveGraceEnd) {\n                        updates.push({\n                            id: machine.id,\n                            updates: {\n                                status: \"free\",\n                                start_at: null,\n                                end_at: null,\n                                started_by_user_id: null,\n                                started_by_device_fingerprint: null,\n                                ...this.hasGraceEndColumn ? {\n                                    grace_end_at: null\n                                } : {}\n                            }\n                        });\n                        console.log(\"✅ Machine \".concat(machine.name, \" grace period ended, now available\"));\n                    }\n                }\n            }\n            // Apply updates sequentially to avoid conflicts\n            for (const update of updates){\n                if (this.isDestroyed) break;\n                try {\n                    const { error: updateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"machines\").update(update.updates).eq(\"id\", update.id);\n                    if (updateError) {\n                        if (updateError.message && updateError.message.includes(\"grace_end_at\")) {\n                            console.log(\"⚠️ grace_end_at column not found, disabling this feature\");\n                            this.hasGraceEndColumn = false;\n                        }\n                        console.error(\"❌ Error updating machine \".concat(update.id, \":\"), updateError);\n                    }\n                } catch (err) {\n                    console.error(\"❌ Error updating machine \".concat(update.id, \":\"), err);\n                }\n                // Small delay between updates to prevent overwhelming the database\n                await new Promise((resolve)=>setTimeout(resolve, 100));\n            }\n            if (updates.length > 0) {\n                console.log(\"\\uD83D\\uDD04 Updated \".concat(updates.length, \" machine statuses\"));\n            }\n        } catch (error) {\n            console.error(\"❌ Error in machine status check:\", error);\n        } finally{\n            this.isChecking = false;\n        }\n    }\n    async triggerStatusCheck() {\n        if (!this.isDestroyed) {\n            await this.checkMachineStatuses();\n        }\n    }\n    constructor(){\n        this.statusCheckInterval = null;\n        this.isChecking = false;\n        this.hasGraceEndColumn = true;\n        this.isDestroyed = false;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MachineStatusManager);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/machineStatusManager.ts\n"));

/***/ })

});