{"version": 3, "sources": ["../../../src/client/components/promise-queue.ts"], "sourcesContent": ["/*\n    This is a simple promise queue that allows you to limit the number of concurrent promises\n    that are running at any given time. It's used to limit the number of concurrent\n    prefetch requests that are being made to the server but could be used for other\n    things as well.\n*/\nexport class PromiseQueue {\n  #maxConcurrency: number\n  #runningCount: number\n  #queue: Array<{\n    promiseFn: Promise<any>\n    task: () => void\n  }>\n\n  constructor(maxConcurrency = 5) {\n    this.#maxConcurrency = maxConcurrency\n    this.#runningCount = 0\n    this.#queue = []\n  }\n\n  enqueue<T>(promiseFn: () => Promise<T>): Promise<T> {\n    let taskResolve: (value: T | PromiseLike<T>) => void\n    let taskReject: (reason?: any) => void\n\n    const taskPromise = new Promise((resolve, reject) => {\n      taskResolve = resolve\n      taskReject = reject\n    }) as Promise<T>\n\n    const task = async () => {\n      try {\n        this.#runningCount++\n        const result = await promiseFn()\n        taskResolve(result)\n      } catch (error) {\n        taskReject(error)\n      } finally {\n        this.#runningCount--\n        this.#processNext()\n      }\n    }\n\n    const enqueueResult = { promiseFn: taskPromise, task }\n    // wonder if we should take a LIFO approach here\n    this.#queue.push(enqueueResult)\n    this.#processNext()\n\n    return taskPromise\n  }\n\n  bump(promiseFn: Promise<any>) {\n    const index = this.#queue.findIndex((item) => item.promiseFn === promiseFn)\n\n    if (index > -1) {\n      const bumpedItem = this.#queue.splice(index, 1)[0]\n      this.#queue.unshift(bumpedItem)\n      this.#processNext(true)\n    }\n  }\n\n  #processNext(forced = false) {\n    if (\n      (this.#runningCount < this.#maxConcurrency || forced) &&\n      this.#queue.length > 0\n    ) {\n      this.#queue.shift()?.task()\n    }\n  }\n}\n"], "names": ["PromiseQueue", "enqueue", "promiseFn", "taskResolve", "taskReject", "taskPromise", "Promise", "resolve", "reject", "task", "result", "error", "enqueueResult", "push", "bump", "index", "findIndex", "item", "bumpedItem", "splice", "unshift", "constructor", "maxConcurrency", "forced", "length", "shift"], "mappings": "AAAA;;;;;AAKA;;;;+BACaA;;;eAAAA;;;;;IACX,qFACA,iFACA,mEAmDA;AAtDK,MAAMA;IAcXC,QAAWC,SAA2B,EAAc;QAClD,IAAIC;QACJ,IAAIC;QAEJ,MAAMC,cAAc,IAAIC,QAAQ,CAACC,SAASC;YACxCL,cAAcI;YACdH,aAAaI;QACf;QAEA,MAAMC,OAAO;YACX,IAAI;gBACF,kCAAA,IAAI,EAAC,eAAA;gBACL,MAAMC,SAAS,MAAMR;gBACrBC,YAAYO;YACd,EAAE,OAAOC,OAAO;gBACdP,WAAWO;YACb,SAAU;gBACR,kCAAA,IAAI,EAAC,eAAA;gBACL,kCAAA,IAAI,EAAC,cAAA;YACP;QACF;QAEA,MAAMC,gBAAgB;YAAEV,WAAWG;YAAaI;QAAK;QACrD,gDAAgD;QAChD,kCAAA,IAAI,EAAC,QAAA,QAAOI,IAAI,CAACD;QACjB,kCAAA,IAAI,EAAC,cAAA;QAEL,OAAOP;IACT;IAEAS,KAAKZ,SAAuB,EAAE;QAC5B,MAAMa,QAAQ,kCAAA,IAAI,EAAC,QAAA,QAAOC,SAAS,CAAC,CAACC,OAASA,KAAKf,SAAS,KAAKA;QAEjE,IAAIa,QAAQ,CAAC,GAAG;YACd,MAAMG,aAAa,kCAAA,IAAI,EAAC,QAAA,QAAOC,MAAM,CAACJ,OAAO,EAAE,CAAC,EAAE;YAClD,kCAAA,IAAI,EAAC,QAAA,QAAOK,OAAO,CAACF;YACpB,kCAAA,IAAI,EAAC,cAAA,cAAa;QACpB;IACF;IA5CAG,YAAYC,iBAAiB,CAAC,CAAE;QA8ChC,4BAAA;mBAAA;;QArDA,4BAAA;;mBAAA,KAAA;;QACA,4BAAA;;mBAAA,KAAA;;QACA,4BAAA;;mBAAA,KAAA;;QAME,kCAAA,IAAI,EAAC,iBAAA,mBAAkBA;QACvB,kCAAA,IAAI,EAAC,eAAA,iBAAgB;QACrB,kCAAA,IAAI,EAAC,QAAA,UAAS,EAAE;IAClB;AAkDF;AARE,SAAA,YAAaC,MAAc;IAAdA,IAAAA,mBAAAA,SAAS;IACpB,IACE,AAAC,CAAA,kCAAA,IAAI,EAAC,eAAA,iBAAgB,kCAAA,IAAI,EAAC,iBAAA,oBAAmBA,MAAK,KACnD,kCAAA,IAAI,EAAC,QAAA,QAAOC,MAAM,GAAG,GACrB;YACA;SAAA,+CAAA,kCAAA,IAAI,EAAC,QAAA,QAAOC,KAAK,uBAAjB,6CAAqBhB,IAAI;IAC3B;AACF"}