"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useSupabaseData.tsx":
/*!***************************************!*\
  !*** ./src/hooks/useSupabaseData.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useSupabaseData)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/src/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _src_lib_subscriptionManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/src/lib/subscriptionManager */ \"(app-pages-browser)/./src/lib/subscriptionManager.ts\");\n/* harmony import */ var _src_lib_machineStatusManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/lib/machineStatusManager */ \"(app-pages-browser)/./src/lib/machineStatusManager.ts\");\n/* harmony import */ var _src_utils_parseDuration__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/src/utils/parseDuration */ \"(app-pages-browser)/./src/utils/parseDuration.tsx\");\n/* harmony import */ var _src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/src/utils/userIdentification */ \"(app-pages-browser)/./src/utils/userIdentification.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\n\n\n\n\n\n// Helper functions to convert between DB and app formats\nconst dbToMachine = (row)=>{\n    const graceEndAt = row.grace_end_at ? new Date(row.grace_end_at) : row.status === \"finishedGrace\" && row.end_at ? new Date(new Date(row.end_at).getTime() + 5 * 60 * 1000) : undefined;\n    return {\n        id: row.id,\n        name: row.name,\n        status: row.status,\n        startAt: row.start_at ? new Date(row.start_at) : undefined,\n        endAt: row.end_at ? new Date(row.end_at) : undefined,\n        graceEndAt: graceEndAt,\n        updatedAt: new Date(row.updated_at),\n        startedByUserId: row.started_by_user_id || undefined,\n        startedByDeviceFingerprint: row.started_by_device_fingerprint || undefined\n    };\n};\nconst dbToNoise = (row)=>({\n        id: row.id,\n        user: row.user_name,\n        description: row.description || \"Noise reported\",\n        timestamp: new Date(row.timestamp),\n        lastReported: new Date(row.last_reported)\n    });\nconst dbToAnnouncement = (row)=>({\n        id: row.id,\n        user: row.user_name,\n        title: row.title,\n        description: row.description,\n        type: row.announcement_type,\n        timestamp: new Date(row.timestamp)\n    });\nconst dbToSublet = (row)=>({\n        id: row.id,\n        user: row.user_name,\n        duration: row.duration,\n        timestamp: new Date(row.timestamp)\n    });\nconst dbToHelpMe = (row)=>({\n        id: row.id,\n        user: row.user_name,\n        description: row.description,\n        timestamp: new Date(row.timestamp)\n    });\nconst dbToIncident = (row)=>({\n        id: row.id,\n        machineId: row.machine_id,\n        timestamp: new Date(row.timestamp),\n        type: row.incident_type\n    });\nfunction useSupabaseData() {\n    _s();\n    const [laundry, setLaundry] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [noise, setNoise] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [announcements, setAnnouncements] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [helpMe, setHelpMe] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [incidents, setIncidents] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [hasGraceEndColumn, setHasGraceEndColumn] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    // Refs to prevent recursive calls and multiple setups\n    const isLoadingDataRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const lastLoadTimeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const subscriptionSetupRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const statusManagerSetupRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const isMountedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(true);\n    // Debounced data loading function with selective loading\n    const loadAllData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[loadAllData]\": async function(specificTable) {\n            let forceRefresh = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n            const now = Date.now();\n            // Skip debounce for manual refresh (forceRefresh = true)\n            if (!forceRefresh && (isLoadingDataRef.current || now - lastLoadTimeRef.current < 1000)) {\n                console.log(\"⏭️ Skipping data load (too recent or in progress)\");\n                return;\n            }\n            if (!isMountedRef.current) {\n                console.log(\"⏭️ Skipping data load (component unmounted)\");\n                return;\n            }\n            isLoadingDataRef.current = true;\n            lastLoadTimeRef.current = now;\n            try {\n                console.log(\"\\uD83D\\uDD04 Loading data from Supabase\".concat(specificTable ? \" (\".concat(specificTable, \")\") : \"\", \"...\"));\n                setError(null);\n                // If specific table is provided, only load that table\n                if (specificTable) {\n                    await loadSpecificTable(specificTable);\n                } else {\n                    // Load all data\n                    setIsLoading(true);\n                    await loadAllTables();\n                }\n                console.log(\"✅ Data loaded successfully\");\n            } catch (err) {\n                console.error(\"❌ Error loading data:\", err);\n                if (isMountedRef.current) {\n                    setError(err instanceof Error ? err.message : \"Failed to load data\");\n                }\n            } finally{\n                if (isMountedRef.current && !specificTable) {\n                    setIsLoading(false);\n                }\n                isLoadingDataRef.current = false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[loadAllData]\"], []);\n    // Helper function to load specific table\n    const loadSpecificTable = async (table)=>{\n        switch(table){\n            case \"machines\":\n                var _machinesResult_data;\n                const machinesResult = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").select(\"*\").order(\"name\");\n                if (machinesResult.error) throw machinesResult.error;\n                setLaundry(((_machinesResult_data = machinesResult.data) === null || _machinesResult_data === void 0 ? void 0 : _machinesResult_data.map(dbToMachine)) || []);\n                break;\n            case \"noise_reports\":\n                var _noiseResult_data;\n                const noiseResult = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"noise_reports\").select(\"*\").order(\"timestamp\", {\n                    ascending: false\n                }).limit(50);\n                if (noiseResult.error) throw noiseResult.error;\n                setNoise(((_noiseResult_data = noiseResult.data) === null || _noiseResult_data === void 0 ? void 0 : _noiseResult_data.map(dbToNoise)) || []);\n                break;\n            case \"announcements\":\n                var _announcementsResult_data;\n                const announcementsResult = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"announcements\").select(\"*\").order(\"timestamp\", {\n                    ascending: false\n                }).limit(50);\n                if (announcementsResult.error) throw announcementsResult.error;\n                setAnnouncements(((_announcementsResult_data = announcementsResult.data) === null || _announcementsResult_data === void 0 ? void 0 : _announcementsResult_data.map(dbToAnnouncement)) || []);\n                break;\n            case \"help_requests\":\n                var _helpResult_data;\n                const helpResult = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"help_requests\").select(\"*\").gte(\"timestamp\", new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()).order(\"timestamp\", {\n                    ascending: false\n                }).limit(50);\n                if (helpResult.error) throw helpResult.error;\n                setHelpMe(((_helpResult_data = helpResult.data) === null || _helpResult_data === void 0 ? void 0 : _helpResult_data.map(dbToHelpMe)) || []);\n                break;\n            case \"incidents\":\n                var _incidentsResult_data;\n                const incidentsResult = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"incidents\").select(\"*\").order(\"timestamp\", {\n                    ascending: false\n                }).limit(50);\n                if (incidentsResult.error) throw incidentsResult.error;\n                setIncidents(((_incidentsResult_data = incidentsResult.data) === null || _incidentsResult_data === void 0 ? void 0 : _incidentsResult_data.map(dbToIncident)) || []);\n                break;\n        }\n    };\n    // Helper function to load all tables\n    const loadAllTables = async ()=>{\n        var _machinesResult_data, _noiseResult_data, _announcementsResult_data, _helpResult_data, _incidentsResult_data;\n        const timeoutPromise = new Promise((_, reject)=>setTimeout(()=>reject(new Error(\"Request timeout\")), 15000));\n        const dataPromise = Promise.all([\n            _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").select(\"*\").order(\"name\"),\n            _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"noise_reports\").select(\"*\").order(\"timestamp\", {\n                ascending: false\n            }).limit(50),\n            _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"announcements\").select(\"*\").order(\"timestamp\", {\n                ascending: false\n            }).limit(50),\n            _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"help_requests\").select(\"*\").gte(\"timestamp\", new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()).order(\"timestamp\", {\n                ascending: false\n            }).limit(50),\n            _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"incidents\").select(\"*\").order(\"timestamp\", {\n                ascending: false\n            }).limit(50)\n        ]);\n        const [machinesResult, noiseResult, announcementsResult, helpResult, incidentsResult] = await Promise.race([\n            dataPromise,\n            timeoutPromise\n        ]);\n        if (!isMountedRef.current) return;\n        if (machinesResult.error) throw new Error(\"Failed to load machines: \".concat(machinesResult.error.message));\n        if (noiseResult.error) throw new Error(\"Failed to load noise reports: \".concat(noiseResult.error.message));\n        if (announcementsResult.error) throw new Error(\"Failed to load announcements: \".concat(announcementsResult.error.message));\n        if (helpResult.error) throw new Error(\"Failed to load help requests: \".concat(helpResult.error.message));\n        if (incidentsResult.error) throw new Error(\"Failed to load incidents: \".concat(incidentsResult.error.message));\n        setLaundry(((_machinesResult_data = machinesResult.data) === null || _machinesResult_data === void 0 ? void 0 : _machinesResult_data.map(dbToMachine)) || []);\n        setNoise(((_noiseResult_data = noiseResult.data) === null || _noiseResult_data === void 0 ? void 0 : _noiseResult_data.map(dbToNoise)) || []);\n        setAnnouncements(((_announcementsResult_data = announcementsResult.data) === null || _announcementsResult_data === void 0 ? void 0 : _announcementsResult_data.map(dbToAnnouncement)) || []);\n        setHelpMe(((_helpResult_data = helpResult.data) === null || _helpResult_data === void 0 ? void 0 : _helpResult_data.map(dbToHelpMe)) || []);\n        setIncidents(((_incidentsResult_data = incidentsResult.data) === null || _incidentsResult_data === void 0 ? void 0 : _incidentsResult_data.map(dbToIncident)) || []);\n    };\n    // Set up real-time subscriptions with table-specific updates\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseData.useEffect\": ()=>{\n            if (subscriptionSetupRef.current) {\n                return;\n            }\n            subscriptionSetupRef.current = true;\n            console.log(\"🔄 Setting up subscription manager...\");\n            const subscriptionManager = _src_lib_subscriptionManager__WEBPACK_IMPORTED_MODULE_2__[\"default\"].getInstance();\n            // Enhanced callback that handles specific table updates\n            const handleRealtimeUpdate = {\n                \"useSupabaseData.useEffect.handleRealtimeUpdate\": (table, event)=>{\n                    if (table && table !== \"polling\") {\n                        // Load only the specific table that changed for faster updates\n                        loadAllData(table);\n                    } else {\n                        // Fallback to loading all data\n                        loadAllData();\n                    }\n                }\n            }[\"useSupabaseData.useEffect.handleRealtimeUpdate\"];\n            subscriptionManager.addCallback(handleRealtimeUpdate);\n            return ({\n                \"useSupabaseData.useEffect\": ()=>{\n                    console.log(\"🧹 Cleaning up subscription manager...\");\n                    subscriptionManager.removeCallback(handleRealtimeUpdate);\n                    subscriptionSetupRef.current = false;\n                }\n            })[\"useSupabaseData.useEffect\"];\n        }\n    }[\"useSupabaseData.useEffect\"], [\n        loadAllData\n    ]);\n    // Set up machine status monitoring (only once)\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseData.useEffect\": ()=>{\n            if (statusManagerSetupRef.current) {\n                return;\n            }\n            statusManagerSetupRef.current = true;\n            console.log(\"🔄 Setting up machine status manager...\");\n            const statusManager = _src_lib_machineStatusManager__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getInstance();\n            statusManager.startStatusMonitoring();\n            return ({\n                \"useSupabaseData.useEffect\": ()=>{\n                    console.log(\"🧹 Cleaning up machine status manager...\");\n                    statusManager.stopStatusMonitoring();\n                    statusManagerSetupRef.current = false;\n                }\n            })[\"useSupabaseData.useEffect\"];\n        }\n    }[\"useSupabaseData.useEffect\"], []);\n    // Initial data load and cleanup\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseData.useEffect\": ()=>{\n            isMountedRef.current = true;\n            loadAllData();\n            return ({\n                \"useSupabaseData.useEffect\": ()=>{\n                    isMountedRef.current = false;\n                }\n            })[\"useSupabaseData.useEffect\"];\n        }\n    }[\"useSupabaseData.useEffect\"], []) // Only run once on mount\n    ;\n    // Machine operations with optimistic updates\n    const toggleMachineStatus = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[toggleMachineStatus]\": async (id)=>{\n            try {\n                const machine = laundry.find({\n                    \"useSupabaseData.useCallback[toggleMachineStatus].machine\": (m)=>m.id === id\n                }[\"useSupabaseData.useCallback[toggleMachineStatus].machine\"]);\n                if (!machine) {\n                    return false;\n                }\n                let optimisticUpdate;\n                let updateData;\n                if (machine.status === \"free\") {\n                    const startAt = new Date();\n                    const endAt = new Date(startAt.getTime() + 60 * 60 * 1000);\n                    const currentUserId = (0,_src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_5__.getDeviceUserId)();\n                    optimisticUpdate = {\n                        ...machine,\n                        status: \"running\",\n                        startAt,\n                        endAt,\n                        graceEndAt: undefined,\n                        updatedAt: new Date(),\n                        startedByUserId: currentUserId,\n                        startedByDeviceFingerprint: currentUserId\n                    };\n                    updateData = {\n                        status: \"running\",\n                        start_at: startAt.toISOString(),\n                        end_at: endAt.toISOString(),\n                        started_by_user_id: currentUserId,\n                        started_by_device_fingerprint: currentUserId\n                    };\n                    if (hasGraceEndColumn) {\n                        updateData.grace_end_at = null;\n                    }\n                } else {\n                    optimisticUpdate = {\n                        ...machine,\n                        status: \"free\",\n                        startAt: undefined,\n                        endAt: undefined,\n                        graceEndAt: undefined,\n                        updatedAt: new Date(),\n                        startedByUserId: undefined,\n                        startedByDeviceFingerprint: undefined\n                    };\n                    updateData = {\n                        status: \"free\",\n                        start_at: null,\n                        end_at: null,\n                        started_by_user_id: null,\n                        started_by_device_fingerprint: null\n                    };\n                    if (hasGraceEndColumn) {\n                        updateData.grace_end_at = null;\n                    }\n                }\n                // Apply optimistic update\n                setLaundry({\n                    \"useSupabaseData.useCallback[toggleMachineStatus]\": (prev)=>prev.map({\n                            \"useSupabaseData.useCallback[toggleMachineStatus]\": (m)=>m.id === id ? optimisticUpdate : m\n                        }[\"useSupabaseData.useCallback[toggleMachineStatus]\"])\n                }[\"useSupabaseData.useCallback[toggleMachineStatus]\"]);\n                // Send update to database\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").update(updateData).eq(\"id\", id);\n                if (error) {\n                    if (error.message && error.message.includes(\"grace_end_at\")) {\n                        setHasGraceEndColumn(false);\n                        delete updateData.grace_end_at;\n                        const { error: retryError } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").update(updateData).eq(\"id\", id);\n                        if (retryError) {\n                            setLaundry({\n                                \"useSupabaseData.useCallback[toggleMachineStatus]\": (prev)=>prev.map({\n                                        \"useSupabaseData.useCallback[toggleMachineStatus]\": (m)=>m.id === id ? machine : m\n                                    }[\"useSupabaseData.useCallback[toggleMachineStatus]\"])\n                            }[\"useSupabaseData.useCallback[toggleMachineStatus]\"]);\n                            throw retryError;\n                        }\n                    } else {\n                        setLaundry({\n                            \"useSupabaseData.useCallback[toggleMachineStatus]\": (prev)=>prev.map({\n                                    \"useSupabaseData.useCallback[toggleMachineStatus]\": (m)=>m.id === id ? machine : m\n                                }[\"useSupabaseData.useCallback[toggleMachineStatus]\"])\n                        }[\"useSupabaseData.useCallback[toggleMachineStatus]\"]);\n                        throw error;\n                    }\n                }\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to update machine\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[toggleMachineStatus]\"], [\n        laundry,\n        hasGraceEndColumn\n    ]);\n    const reserveMachine = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[reserveMachine]\": async (id, durationStr)=>{\n            try {\n                const machine = laundry.find({\n                    \"useSupabaseData.useCallback[reserveMachine].machine\": (m)=>m.id === id\n                }[\"useSupabaseData.useCallback[reserveMachine].machine\"]);\n                if (!machine) {\n                    return false;\n                }\n                const durationSeconds = (0,_src_utils_parseDuration__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(durationStr);\n                const startAt = new Date();\n                const endAt = new Date(startAt.getTime() + durationSeconds * 1000);\n                const currentUserId = (0,_src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_5__.getDeviceUserId)();\n                const optimisticUpdate = {\n                    ...machine,\n                    status: \"running\",\n                    startAt,\n                    endAt,\n                    graceEndAt: undefined,\n                    updatedAt: new Date(),\n                    startedByUserId: currentUserId,\n                    startedByDeviceFingerprint: currentUserId\n                };\n                setLaundry({\n                    \"useSupabaseData.useCallback[reserveMachine]\": (prev)=>prev.map({\n                            \"useSupabaseData.useCallback[reserveMachine]\": (m)=>m.id === id ? optimisticUpdate : m\n                        }[\"useSupabaseData.useCallback[reserveMachine]\"])\n                }[\"useSupabaseData.useCallback[reserveMachine]\"]);\n                const updateData = {\n                    status: \"running\",\n                    start_at: startAt.toISOString(),\n                    end_at: endAt.toISOString(),\n                    started_by_user_id: currentUserId,\n                    started_by_device_fingerprint: currentUserId\n                };\n                if (hasGraceEndColumn) {\n                    updateData.grace_end_at = null;\n                }\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").update(updateData).eq(\"id\", id);\n                if (error) {\n                    if (error.message && error.message.includes(\"grace_end_at\")) {\n                        setHasGraceEndColumn(false);\n                        delete updateData.grace_end_at;\n                        const { error: retryError } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").update(updateData).eq(\"id\", id);\n                        if (retryError) {\n                            setLaundry({\n                                \"useSupabaseData.useCallback[reserveMachine]\": (prev)=>prev.map({\n                                        \"useSupabaseData.useCallback[reserveMachine]\": (m)=>m.id === id ? machine : m\n                                    }[\"useSupabaseData.useCallback[reserveMachine]\"])\n                            }[\"useSupabaseData.useCallback[reserveMachine]\"]);\n                            throw retryError;\n                        }\n                    } else {\n                        setLaundry({\n                            \"useSupabaseData.useCallback[reserveMachine]\": (prev)=>prev.map({\n                                    \"useSupabaseData.useCallback[reserveMachine]\": (m)=>m.id === id ? machine : m\n                                }[\"useSupabaseData.useCallback[reserveMachine]\"])\n                        }[\"useSupabaseData.useCallback[reserveMachine]\"]);\n                        throw error;\n                    }\n                }\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to reserve machine\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[reserveMachine]\"], [\n        laundry,\n        hasGraceEndColumn\n    ]);\n    // New function to adjust machine time (only for machines you started)\n    const adjustMachineTime = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[adjustMachineTime]\": async (id, newMinutes)=>{\n            try {\n                const machine = laundry.find({\n                    \"useSupabaseData.useCallback[adjustMachineTime].machine\": (m)=>m.id === id\n                }[\"useSupabaseData.useCallback[adjustMachineTime].machine\"]);\n                if (!machine) {\n                    return {\n                        success: false,\n                        error: \"Machine not found\"\n                    };\n                }\n                const currentUserId = (0,_src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_5__.getDeviceUserId)();\n                // Check ownership - only the user who started the machine can adjust it\n                if (machine.startedByUserId !== currentUserId) {\n                    return {\n                        success: false,\n                        error: \"You can only adjust machines you started\"\n                    };\n                }\n                // Validate time range (1-120 minutes for adjustment)\n                if (newMinutes < 1 || newMinutes > 120) {\n                    return {\n                        success: false,\n                        error: \"Please enter a number between 1-120 minutes\"\n                    };\n                }\n                // Calculate new end time based on the new total duration from start time\n                const now = new Date();\n                const startTime = machine.startAt || now;\n                const newEndAt = new Date(startTime.getTime() + newMinutes * 60 * 1000);\n                const optimisticUpdate = {\n                    ...machine,\n                    endAt: newEndAt,\n                    updatedAt: new Date()\n                };\n                // Apply optimistic update\n                setLaundry({\n                    \"useSupabaseData.useCallback[adjustMachineTime]\": (prev)=>prev.map({\n                            \"useSupabaseData.useCallback[adjustMachineTime]\": (m)=>m.id === id ? optimisticUpdate : m\n                        }[\"useSupabaseData.useCallback[adjustMachineTime]\"])\n                }[\"useSupabaseData.useCallback[adjustMachineTime]\"]);\n                // Send update to database\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").update({\n                    end_at: newEndAt.toISOString()\n                }).eq(\"id\", id);\n                if (error) {\n                    // Revert optimistic update on error\n                    setLaundry({\n                        \"useSupabaseData.useCallback[adjustMachineTime]\": (prev)=>prev.map({\n                                \"useSupabaseData.useCallback[adjustMachineTime]\": (m)=>m.id === id ? machine : m\n                            }[\"useSupabaseData.useCallback[adjustMachineTime]\"])\n                    }[\"useSupabaseData.useCallback[adjustMachineTime]\"]);\n                    return {\n                        success: false,\n                        error: \"Unable to update timer. Please try again.\"\n                    };\n                }\n                return {\n                    success: true,\n                    error: null\n                };\n            } catch (err) {\n                return {\n                    success: false,\n                    error: err instanceof Error ? err.message : \"Unable to update timer. Please try again.\"\n                };\n            }\n        }\n    }[\"useSupabaseData.useCallback[adjustMachineTime]\"], [\n        laundry\n    ]);\n    // Simplified CRUD operations\n    const addNoiseWithDescription = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[addNoiseWithDescription]\": async (user, description)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"noise_reports\").insert({\n                    id: Date.now().toString(),\n                    user_name: user,\n                    description: description\n                });\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to add noise report\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[addNoiseWithDescription]\"], []);\n    const deleteNoise = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[deleteNoise]\": async (id)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"noise_reports\").delete().eq(\"id\", id);\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to delete noise report\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[deleteNoise]\"], []);\n    const addAnnouncement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[addAnnouncement]\": async (user, title, description, type)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"announcements\").insert({\n                    id: Date.now().toString(),\n                    user_name: user,\n                    title: title,\n                    description: description,\n                    announcement_type: type\n                });\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to add announcement\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[addAnnouncement]\"], []);\n    const deleteAnnouncement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[deleteAnnouncement]\": async (id)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"announcements\").delete().eq(\"id\", id);\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to delete announcement\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[deleteAnnouncement]\"], []);\n    const addHelpRequest = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[addHelpRequest]\": async (user, description)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"help_requests\").insert({\n                    id: Date.now().toString(),\n                    user_name: user,\n                    description\n                });\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to add help request\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[addHelpRequest]\"], []);\n    const deleteHelpRequest = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[deleteHelpRequest]\": async (id)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"help_requests\").delete().eq(\"id\", id);\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to delete help request\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[deleteHelpRequest]\"], []);\n    const deleteIncident = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[deleteIncident]\": async (id)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"incidents\").delete().eq(\"id\", id);\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to delete incident\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[deleteIncident]\"], []);\n    return {\n        // Data\n        laundry,\n        noise,\n        announcements,\n        helpMe,\n        incidents,\n        // State\n        isLoading,\n        error,\n        // Operations\n        toggleMachineStatus,\n        reserveMachine,\n        adjustMachineTime,\n        addNoiseWithDescription,\n        deleteNoise,\n        addAnnouncement,\n        deleteAnnouncement,\n        addHelpRequest,\n        deleteHelpRequest,\n        deleteIncident,\n        refreshData: ()=>loadAllData(undefined, true)\n    };\n}\n_s(useSupabaseData, \"2+7VgD2CuMwPZ4C28ZFRT/x3kEM=\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useSupabaseData.tsx\n"));

/***/ })

});