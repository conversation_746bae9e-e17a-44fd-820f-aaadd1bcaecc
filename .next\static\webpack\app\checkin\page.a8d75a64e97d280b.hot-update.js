"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/checkin/page",{

/***/ "(app-pages-browser)/./app/checkin/page.tsx":
/*!******************************!*\
  !*** ./app/checkin/page.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CheckInPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var qrcode_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! qrcode.react */ \"(app-pages-browser)/./node_modules/.pnpm/qrcode.react@4.2.0_react@19.0.0/node_modules/qrcode.react/lib/esm/index.js\");\n/* harmony import */ var _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/src/hooks/useSupabaseData */ \"(app-pages-browser)/./src/hooks/useSupabaseData.tsx\");\n/* harmony import */ var _src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/src/hooks/useCountdown */ \"(app-pages-browser)/./src/hooks/useCountdown.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction CheckInPage() {\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const machineId = searchParams.get(\"machine\");\n    const { laundry, toggleMachineStatus, reserveMachine, adjustMachineTime, isLoading, refreshData } = (0,_src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    const [machine, setMachine] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [actionResult, setActionResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [customTime, setCustomTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"60\");\n    const [useCustomTime, setUseCustomTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const countdown = (0,_src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(machine === null || machine === void 0 ? void 0 : machine.endAt, machine === null || machine === void 0 ? void 0 : machine.graceEndAt);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CheckInPage.useEffect\": ()=>{\n            if (!machineId) return;\n            const foundMachine = laundry.find({\n                \"CheckInPage.useEffect.foundMachine\": (m)=>m.id === machineId\n            }[\"CheckInPage.useEffect.foundMachine\"]);\n            setMachine(foundMachine || null);\n        }\n    }[\"CheckInPage.useEffect\"], [\n        machineId,\n        laundry\n    ]);\n    // Reduced auto-refresh to every 10 seconds to prevent conflicts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CheckInPage.useEffect\": ()=>{\n            const interval = setInterval({\n                \"CheckInPage.useEffect.interval\": ()=>{\n                    refreshData();\n                }\n            }[\"CheckInPage.useEffect.interval\"], 10000);\n            return ({\n                \"CheckInPage.useEffect\": ()=>clearInterval(interval)\n            })[\"CheckInPage.useEffect\"];\n        }\n    }[\"CheckInPage.useEffect\"], [\n        refreshData\n    ]);\n    const handleToggle = async ()=>{\n        if (!machineId || isProcessing) return;\n        setIsProcessing(true);\n        setActionResult(\"\");\n        let success = false;\n        if ((machine === null || machine === void 0 ? void 0 : machine.status) === \"free\" && useCustomTime) {\n            // Use custom time\n            success = await reserveMachine(machineId, \"\".concat(customTime, \" minutes\"));\n        } else {\n            // Use default toggle\n            success = await toggleMachineStatus(machineId);\n        }\n        if (success) {\n            setActionResult(\"Status updated successfully!\");\n            // Single refresh after 2 seconds to confirm the change\n            setTimeout(()=>refreshData(), 2000);\n        } else {\n            setActionResult(\"Error updating machine status\");\n        }\n        setIsProcessing(false);\n    };\n    const getStatusDisplay = ()=>{\n        if (!machine) return \"\";\n        switch(machine.status){\n            case \"free\":\n                return \"🟢 Available\";\n            case \"running\":\n                const hours = Math.floor(countdown.secondsLeft / 3600);\n                const minutes = Math.floor(countdown.secondsLeft % 3600 / 60);\n                const seconds = countdown.secondsLeft % 60;\n                return \"\\uD83D\\uDD35 In Use - \".concat(hours, \":\").concat(minutes.toString().padStart(2, \"0\"), \":\").concat(seconds.toString().padStart(2, \"0\"), \" left\");\n            case \"finishedGrace\":\n                // Calculate grace period time remaining\n                let graceTimeDisplay = \"5:00\";\n                if (machine.graceEndAt) {\n                    const graceMinutes = Math.floor(countdown.graceSecondsLeft / 60);\n                    const graceSeconds = countdown.graceSecondsLeft % 60;\n                    graceTimeDisplay = \"\".concat(graceMinutes, \":\").concat(graceSeconds.toString().padStart(2, \"0\"));\n                }\n                return \"⚠️ Please collect items - \".concat(graceTimeDisplay, \" left\");\n            default:\n                return \"Unknown\";\n        }\n    };\n    const getButtonText = ()=>{\n        if (isProcessing) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 11\n                    }, this),\n                    \"Processing...\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 97,\n                columnNumber: 9\n            }, this);\n        }\n        switch(machine === null || machine === void 0 ? void 0 : machine.status){\n            case \"free\":\n                return useCustomTime ? \"▶️ Start Using Machine (\".concat(customTime, \" min)\") : \"▶️ Start Using Machine\";\n            case \"running\":\n                return \"⏹️ Stop Using Machine\";\n            case \"finishedGrace\":\n                return \"✅ I've Collected My Items\";\n            default:\n                return \"Update Status\";\n        }\n    };\n    const getButtonColor = ()=>{\n        if (isProcessing) return \"bg-gray-400 cursor-not-allowed\";\n        switch(machine === null || machine === void 0 ? void 0 : machine.status){\n            case \"free\":\n                return \"bg-blue-500 hover:bg-blue-600\";\n            case \"running\":\n                return \"bg-red-500 hover:bg-red-600\";\n            case \"finishedGrace\":\n                return \"bg-green-500 hover:bg-green-600\";\n            default:\n                return \"bg-gray-500\";\n        }\n    };\n    const handleBackToHome = ()=>{\n        router.push(\"/\");\n    };\n    // Custom display names for machines (same logic as LaundryCard)\n    const getDisplayName = (machine)=>{\n        if (!machine) return \"\";\n        const isWasher = machine.name.toLowerCase().includes(\"washer\");\n        if (isWasher) {\n            // Extract number from washer name (e.g., \"Washer 1\" -> \"1\")\n            const numberMatch = machine.name.match(/\\d+/);\n            const number = numberMatch ? numberMatch[0] : \"\";\n            return \"Washer \".concat(number);\n        } else {\n            // For dryers, use \"Dryer 5\" through \"Dryer 8\" based on original number\n            const numberMatch = machine.name.match(/\\d+/);\n            const originalNumber = numberMatch ? Number.parseInt(numberMatch[0]) : 0;\n            const newNumber = originalNumber + 4 // Map 1->5, 2->6, 3->7, 4->8\n            ;\n            return \"Dryer \".concat(newNumber);\n        }\n    };\n    if (isLoading && !machine) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-8 rounded-lg shadow text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-lg\",\n                        children: \"Getting machine information...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 158,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 157,\n            columnNumber: 7\n        }, this);\n    }\n    if (!machineId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-8 rounded-lg shadow text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-600 text-lg mb-4\",\n                        children: \"Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600\",\n                        children: \"No machine ID provided.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 169,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 168,\n            columnNumber: 7\n        }, this);\n    }\n    if (!machine) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-8 rounded-lg shadow text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-600 text-lg mb-4\",\n                        children: \"Machine Not Found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"The requested machine could not be found.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: refreshData,\n                        className: \"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded\",\n                        children: \"Retry Loading\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 180,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 179,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white p-8 rounded-lg shadow text-center max-w-md w-full mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-4\",\n                    children: \"Machine Check-In\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-600 mb-3\",\n                            children: \"Scan to access this page\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white p-3 rounded-lg border-2 border-gray-200 shadow-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(qrcode_react__WEBPACK_IMPORTED_MODULE_3__.QRCodeSVG, {\n                                    value: \"\".concat( true ? window.location.origin : 0, \"/checkin?machine=\").concat(machineId),\n                                    size: 80,\n                                    fgColor: \"#1A1F36\",\n                                    bgColor: \"#FFFFFF\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-lg font-medium\",\n                            children: getDisplayName(machine)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm mt-1 font-semibold \".concat(machine.status === \"free\" ? \"text-green-600\" : machine.status === \"running\" ? \"text-blue-600\" : machine.status === \"finishedGrace\" ? \"text-orange-600\" : \"text-red-600\", \" \").concat(machine.status === \"finishedGrace\" ? \"animate-pulse\" : \"\"),\n                            children: [\n                                \"Status: \",\n                                getStatusDisplay()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 11\n                        }, this),\n                        machine.status === \"running\" && machine.endAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500 mt-1\",\n                            children: [\n                                \"Ends at: \",\n                                machine.endAt.toLocaleTimeString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 13\n                        }, this),\n                        machine.status === \"finishedGrace\" && machine.graceEndAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-orange-500 mt-1\",\n                            children: [\n                                \"Grace period ends at: \",\n                                machine.graceEndAt.toLocaleTimeString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 9\n                }, this),\n                machine.status === \"free\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    id: \"useCustomTime\",\n                                    checked: useCustomTime,\n                                    onChange: (e)=>setUseCustomTime(e.target.checked),\n                                    className: \"mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"useCustomTime\",\n                                    className: \"text-sm\",\n                                    children: \"Set custom time\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 13\n                        }, this),\n                        useCustomTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"number\",\n                                    value: customTime,\n                                    onChange: (e)=>setCustomTime(e.target.value),\n                                    min: \"1\",\n                                    max: \"120\",\n                                    className: \"border rounded p-2 w-20 text-center mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"minutes\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleToggle,\n                    disabled: isProcessing,\n                    className: \"w-full p-3 rounded text-white font-medium mb-4 \".concat(getButtonColor()),\n                    children: getButtonText()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: refreshData,\n                    disabled: isLoading,\n                    className: \"w-full p-2 rounded border border-gray-300 text-gray-700 hover:bg-gray-50 mb-4\",\n                    children: \"Refresh Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleBackToHome,\n                    className: \"w-full p-2 rounded bg-accent hover:bg-teal-600 text-white font-medium mb-4 transition-colors duration-200\",\n                    children: \"← Back to Home\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 9\n                }, this),\n                actionResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm mb-4 p-2 rounded \".concat(actionResult.includes(\"Error\") ? \"bg-red-100 text-red-700\" : \"bg-green-100 text-green-700\"),\n                    children: actionResult\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 295,\n                    columnNumber: 11\n                }, this),\n                machine.status === \"finishedGrace\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-orange-50 border border-orange-200 rounded p-3 text-sm text-orange-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium mb-1\",\n                            children: \"⚠️ Grace Period Active\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Please collect your items within the time limit. The machine will become available automatically when the grace period ends.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 305,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 193,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n        lineNumber: 192,\n        columnNumber: 5\n    }, this);\n}\n_s(CheckInPage, \"zfHmDKpxDnmMED9TwSdM/3RrJWo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        _src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n});\n_c = CheckInPage;\nvar _c;\n$RefreshReg$(_c, \"CheckInPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/checkin/page.tsx\n"));

/***/ })

});