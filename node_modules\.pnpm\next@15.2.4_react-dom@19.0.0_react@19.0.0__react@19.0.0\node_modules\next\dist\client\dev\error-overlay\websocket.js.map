{"version": 3, "sources": ["../../../../src/client/dev/error-overlay/websocket.ts"], "sourcesContent": ["// next-contentlayer is relying on this internal path\n// https://github.com/contentlayerdev/contentlayer/blob/2f491c540e1d3667577f57fa368b150bff427aaf/packages/next-contentlayer/src/hooks/useLiveReload.ts#L1\n// Drop this file if https://github.com/contentlayerdev/contentlayer/pull/649 is merged/released\nexport { addMessageListener } from '../../components/react-dev-overlay/pages/websocket'\n"], "names": ["addMessageListener"], "mappings": "AAAA,qDAAqD;AACrD,yJAAyJ;AACzJ,gGAAgG;;;;;+BACvFA;;;eAAAA,6BAAkB;;;2BAAQ"}