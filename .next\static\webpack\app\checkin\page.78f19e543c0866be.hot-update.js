"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/checkin/page",{

/***/ "(app-pages-browser)/./app/checkin/page.tsx":
/*!******************************!*\
  !*** ./app/checkin/page.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CheckInPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var qrcode_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! qrcode.react */ \"(app-pages-browser)/./node_modules/.pnpm/qrcode.react@4.2.0_react@19.0.0/node_modules/qrcode.react/lib/esm/index.js\");\n/* harmony import */ var _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/src/hooks/useSupabaseData */ \"(app-pages-browser)/./src/hooks/useSupabaseData.tsx\");\n/* harmony import */ var _src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/src/hooks/useCountdown */ \"(app-pages-browser)/./src/hooks/useCountdown.tsx\");\n/* harmony import */ var _src_utils_machineOwnership__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/src/utils/machineOwnership */ \"(app-pages-browser)/./src/utils/machineOwnership.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction CheckInPage() {\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const machineId = searchParams.get(\"machine\");\n    const { laundry, toggleMachineStatus, reserveMachine, adjustMachineTime, isLoading, refreshData } = (0,_src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    const [machine, setMachine] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [actionResult, setActionResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [customTime, setCustomTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"60\");\n    const [adjustTime, setAdjustTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isAdjusting, setIsAdjusting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [adjustResult, setAdjustResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const countdown = (0,_src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(machine === null || machine === void 0 ? void 0 : machine.endAt, machine === null || machine === void 0 ? void 0 : machine.graceEndAt);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CheckInPage.useEffect\": ()=>{\n            if (!machineId) return;\n            const foundMachine = laundry.find({\n                \"CheckInPage.useEffect.foundMachine\": (m)=>m.id === machineId\n            }[\"CheckInPage.useEffect.foundMachine\"]);\n            setMachine(foundMachine || null);\n        }\n    }[\"CheckInPage.useEffect\"], [\n        machineId,\n        laundry\n    ]);\n    // Reduced auto-refresh to every 10 seconds to prevent conflicts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CheckInPage.useEffect\": ()=>{\n            const interval = setInterval({\n                \"CheckInPage.useEffect.interval\": ()=>{\n                    refreshData();\n                }\n            }[\"CheckInPage.useEffect.interval\"], 10000);\n            return ({\n                \"CheckInPage.useEffect\": ()=>clearInterval(interval)\n            })[\"CheckInPage.useEffect\"];\n        }\n    }[\"CheckInPage.useEffect\"], [\n        refreshData\n    ]);\n    const handleToggle = async ()=>{\n        if (!machineId || isProcessing) return;\n        // Validate time input for free machines\n        if ((machine === null || machine === void 0 ? void 0 : machine.status) === \"free\") {\n            const timeNum = parseInt(customTime);\n            if (isNaN(timeNum) || timeNum < 15 || timeNum > 180) {\n                setActionResult(\"Please enter a valid time limit\");\n                return;\n            }\n        }\n        setIsProcessing(true);\n        setActionResult(\"\");\n        let success = false;\n        if ((machine === null || machine === void 0 ? void 0 : machine.status) === \"free\") {\n            // Always use custom time for starting machines\n            success = await reserveMachine(machineId, \"\".concat(customTime, \" minutes\"));\n        } else {\n            // Use default toggle for stopping/collecting\n            success = await toggleMachineStatus(machineId);\n        }\n        if (success) {\n            setActionResult(\"Status updated successfully!\");\n            // Single refresh after 2 seconds to confirm the change\n            setTimeout(()=>refreshData(), 2000);\n        } else {\n            setActionResult(\"Error updating machine status\");\n        }\n        setIsProcessing(false);\n    };\n    const getStatusDisplay = ()=>{\n        if (!machine) return \"\";\n        switch(machine.status){\n            case \"free\":\n                return \"🟢 Available\";\n            case \"running\":\n                const hours = Math.floor(countdown.secondsLeft / 3600);\n                const minutes = Math.floor(countdown.secondsLeft % 3600 / 60);\n                const seconds = countdown.secondsLeft % 60;\n                return \"\\uD83D\\uDD35 In Use - \".concat(hours, \":\").concat(minutes.toString().padStart(2, \"0\"), \":\").concat(seconds.toString().padStart(2, \"0\"), \" left\");\n            case \"finishedGrace\":\n                // Calculate grace period time remaining\n                let graceTimeDisplay = \"5:00\";\n                if (machine.graceEndAt) {\n                    const graceMinutes = Math.floor(countdown.graceSecondsLeft / 60);\n                    const graceSeconds = countdown.graceSecondsLeft % 60;\n                    graceTimeDisplay = \"\".concat(graceMinutes, \":\").concat(graceSeconds.toString().padStart(2, \"0\"));\n                }\n                return \"⚠️ Please collect items - \".concat(graceTimeDisplay, \" left\");\n            default:\n                return \"Unknown\";\n        }\n    };\n    const getButtonText = ()=>{\n        if (isProcessing) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, this),\n                    \"Processing...\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 108,\n                columnNumber: 9\n            }, this);\n        }\n        switch(machine === null || machine === void 0 ? void 0 : machine.status){\n            case \"free\":\n                return \"▶️ Start Using Machine (\".concat(customTime, \" min)\");\n            case \"running\":\n                return \"⏹️ Stop Using Machine\";\n            case \"finishedGrace\":\n                return \"✅ I've Collected My Items\";\n            default:\n                return \"Update Status\";\n        }\n    };\n    const getButtonColor = ()=>{\n        if (isProcessing) return \"bg-gray-400 cursor-not-allowed\";\n        switch(machine === null || machine === void 0 ? void 0 : machine.status){\n            case \"free\":\n                return \"bg-blue-500 hover:bg-blue-600\";\n            case \"running\":\n                return \"bg-red-500 hover:bg-red-600\";\n            case \"finishedGrace\":\n                return \"bg-green-500 hover:bg-green-600\";\n            default:\n                return \"bg-gray-500\";\n        }\n    };\n    const handleBackToHome = ()=>{\n        router.push(\"/\");\n    };\n    const handleAdjustTime = async ()=>{\n        if (!machineId || isAdjusting) return;\n        const minutesNum = parseInt(adjustTime);\n        if (isNaN(minutesNum) || minutesNum < 1 || minutesNum > 120) {\n            setAdjustResult(\"Please enter a number between 1-120 minutes\");\n            return;\n        }\n        setIsAdjusting(true);\n        setAdjustResult(\"\");\n        const result = await adjustMachineTime(machineId, minutesNum);\n        if (result.success) {\n            setAdjustResult(\"Timer updated successfully\");\n            setAdjustTime(\"\");\n            // Refresh data after successful adjustment\n            setTimeout(()=>refreshData(), 1000);\n        } else {\n            setAdjustResult(result.error || \"Failed to update timer\");\n        }\n        setIsAdjusting(false);\n    };\n    // Custom display names for machines (same logic as LaundryCard)\n    const getDisplayName = (machine)=>{\n        if (!machine) return \"\";\n        const isWasher = machine.name.toLowerCase().includes(\"washer\");\n        if (isWasher) {\n            // Extract number from washer name (e.g., \"Washer 1\" -> \"1\")\n            const numberMatch = machine.name.match(/\\d+/);\n            const number = numberMatch ? numberMatch[0] : \"\";\n            return \"Washer \".concat(number);\n        } else {\n            // For dryers, use \"Dryer 5\" through \"Dryer 8\" based on original number\n            const numberMatch = machine.name.match(/\\d+/);\n            const originalNumber = numberMatch ? Number.parseInt(numberMatch[0]) : 0;\n            const newNumber = originalNumber + 4 // Map 1->5, 2->6, 3->7, 4->8\n            ;\n            return \"Dryer \".concat(newNumber);\n        }\n    };\n    if (isLoading && !machine) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-8 rounded-lg shadow text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-lg\",\n                        children: \"Getting machine information...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 195,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 194,\n            columnNumber: 7\n        }, this);\n    }\n    if (!machineId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-8 rounded-lg shadow text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-600 text-lg mb-4\",\n                        children: \"Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600\",\n                        children: \"No machine ID provided.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 206,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 205,\n            columnNumber: 7\n        }, this);\n    }\n    if (!machine) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-8 rounded-lg shadow text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-600 text-lg mb-4\",\n                        children: \"Machine Not Found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"The requested machine could not be found.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: refreshData,\n                        className: \"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded\",\n                        children: \"Retry Loading\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 217,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 216,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white p-8 rounded-lg shadow text-center max-w-md w-full mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-4\",\n                    children: \"Machine Check-In\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-600 mb-3\",\n                            children: \"Scan to access this page\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white p-3 rounded-lg border-2 border-gray-200 shadow-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(qrcode_react__WEBPACK_IMPORTED_MODULE_3__.QRCodeSVG, {\n                                    value: \"\".concat( true ? window.location.origin : 0, \"/checkin?machine=\").concat(machineId),\n                                    size: 80,\n                                    fgColor: \"#1A1F36\",\n                                    bgColor: \"#FFFFFF\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-lg font-medium\",\n                            children: getDisplayName(machine)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm mt-1 font-semibold \".concat(machine.status === \"free\" ? \"text-green-600\" : machine.status === \"running\" ? \"text-blue-600\" : machine.status === \"finishedGrace\" ? \"text-orange-600\" : \"text-red-600\", \" \").concat(machine.status === \"finishedGrace\" ? \"animate-pulse\" : \"\"),\n                            children: [\n                                \"Status: \",\n                                getStatusDisplay()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, this),\n                        machine.status === \"running\" && machine.endAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500 mt-1\",\n                            children: [\n                                \"Ends at: \",\n                                machine.endAt.toLocaleTimeString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 13\n                        }, this),\n                        machine.status === \"finishedGrace\" && machine.graceEndAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-orange-500 mt-1\",\n                            children: [\n                                \"Grace period ends at: \",\n                                machine.graceEndAt.toLocaleTimeString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 13\n                        }, this),\n                        machine.startedByUserId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-600 mt-2\",\n                            children: (0,_src_utils_machineOwnership__WEBPACK_IMPORTED_MODULE_6__.getOwnershipDisplay)(machine)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 9\n                }, this),\n                machine.status === \"free\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium text-blue-800 mb-2\",\n                            children: \"Set Time Limit\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"number\",\n                                    value: customTime,\n                                    onChange: (e)=>setCustomTime(e.target.value),\n                                    placeholder: \"Enter minutes\",\n                                    className: \"border border-gray-300 rounded-md p-3 w-24 text-center text-lg font-medium focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-600 font-medium\",\n                                    children: \"minutes\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-blue-600 mt-2 text-center\",\n                            children: \"Add a time limit accordingly\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 11\n                }, this),\n                (0,_src_utils_machineOwnership__WEBPACK_IMPORTED_MODULE_6__.canAdjustTime)(machine) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium text-blue-800 mb-2\",\n                            children: \"Adjust Timer\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-blue-600 mb-3\",\n                            children: [\n                                \"Current: \",\n                                machine.endAt ? Math.max(0, Math.ceil((machine.endAt.getTime() - Date.now()) / (1000 * 60))) : 0,\n                                \" minutes remaining\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"number\",\n                                    value: adjustTime,\n                                    onChange: (e)=>setAdjustTime(e.target.value),\n                                    placeholder: \"Minutes (1-120)\",\n                                    min: \"1\",\n                                    max: \"120\",\n                                    disabled: isAdjusting,\n                                    className: \"flex-1 border rounded p-2 text-center text-sm\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleAdjustTime,\n                                    disabled: isAdjusting || !adjustTime,\n                                    className: \"bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1\",\n                                    children: [\n                                        isAdjusting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 border border-white border-t-transparent rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 19\n                                        }, this),\n                                        isAdjusting ? \"Updating...\" : \"Update Timer\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 13\n                        }, this),\n                        adjustResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs p-2 rounded \".concat(adjustResult.includes(\"successfully\") ? \"bg-green-100 text-green-800 border border-green-200\" : \"bg-red-100 text-red-800 border border-red-200\"),\n                            children: adjustResult\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleToggle,\n                    disabled: isProcessing,\n                    className: \"w-full p-3 rounded text-white font-medium mb-4 \".concat(getButtonColor()),\n                    children: getButtonText()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 345,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleBackToHome,\n                    className: \"w-full p-3 rounded-lg bg-gray-800 hover:bg-gray-900 text-white font-medium mb-4 transition-colors duration-200 shadow-md\",\n                    children: \"← Back to Home\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 354,\n                    columnNumber: 9\n                }, this),\n                actionResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm mb-4 p-2 rounded \".concat(actionResult.includes(\"Error\") ? \"bg-red-100 text-red-700\" : \"bg-green-100 text-green-700\"),\n                    children: actionResult\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 362,\n                    columnNumber: 11\n                }, this),\n                machine.status === \"finishedGrace\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-orange-50 border border-orange-200 rounded p-3 text-sm text-orange-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium mb-1\",\n                            children: \"⚠️ Grace Period Active\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 373,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Please collect your items within the time limit. The machine will become available automatically when the grace period ends.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 374,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 372,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 230,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n        lineNumber: 229,\n        columnNumber: 5\n    }, this);\n}\n_s(CheckInPage, \"v+h7YXyy6gXcj/Rvi3VDQhkCD98=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        _src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n});\n_c = CheckInPage;\nvar _c;\n$RefreshReg$(_c, \"CheckInPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/checkin/page.tsx\n"));

/***/ })

});