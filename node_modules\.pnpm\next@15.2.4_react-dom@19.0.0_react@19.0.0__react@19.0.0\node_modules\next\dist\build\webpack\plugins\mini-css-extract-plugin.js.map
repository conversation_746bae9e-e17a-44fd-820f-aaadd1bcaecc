{"version": 3, "sources": ["../../../../src/build/webpack/plugins/mini-css-extract-plugin.ts"], "sourcesContent": ["// @ts-ignore: TODO: remove when webpack 5 is stable\nimport MiniCssExtractPlugin from 'next/dist/compiled/mini-css-extract-plugin'\n\nexport default class NextMiniCssExtractPlugin extends MiniCssExtractPlugin {\n  __next_css_remove = true\n}\n"], "names": ["NextMiniCssExtractPlugin", "MiniCssExtractPlugin", "__next_css_remove"], "mappings": "AAAA,oDAAoD;;;;;+BAGpD;;;eAAqBA;;;6EAFY;;;;;;AAElB,MAAMA,iCAAiCC,6BAAoB;;QAA3D,qBACbC,oBAAoB;;AACtB"}