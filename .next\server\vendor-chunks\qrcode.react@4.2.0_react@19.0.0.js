"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/qrcode.react@4.2.0_react@19.0.0";
exports.ids = ["vendor-chunks/qrcode.react@4.2.0_react@19.0.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/qrcode.react@4.2.0_react@19.0.0/node_modules/qrcode.react/lib/esm/index.js":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/qrcode.react@4.2.0_react@19.0.0/node_modules/qrcode.react/lib/esm/index.js ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QRCodeCanvas: () => (/* binding */ QRCodeCanvas),\n/* harmony export */   QRCodeSVG: () => (/* binding */ QRCodeSVG)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\nvar __defProp = Object.defineProperty;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\n\n// src/index.tsx\n\n\n// src/third-party/qrcodegen/index.ts\n/**\n * @license QR Code generator library (TypeScript)\n * Copyright (c) Project Nayuki.\n * SPDX-License-Identifier: MIT\n */\nvar qrcodegen;\n((qrcodegen2) => {\n  const _QrCode = class _QrCode {\n    /*-- Constructor (low level) and fields --*/\n    // Creates a new QR Code with the given version number,\n    // error correction level, data codeword bytes, and mask number.\n    // This is a low-level API that most users should not use directly.\n    // A mid-level API is the encodeSegments() function.\n    constructor(version, errorCorrectionLevel, dataCodewords, msk) {\n      this.version = version;\n      this.errorCorrectionLevel = errorCorrectionLevel;\n      // The modules of this QR Code (false = light, true = dark).\n      // Immutable after constructor finishes. Accessed through getModule().\n      this.modules = [];\n      // Indicates function modules that are not subjected to masking. Discarded when constructor finishes.\n      this.isFunction = [];\n      if (version < _QrCode.MIN_VERSION || version > _QrCode.MAX_VERSION)\n        throw new RangeError(\"Version value out of range\");\n      if (msk < -1 || msk > 7)\n        throw new RangeError(\"Mask value out of range\");\n      this.size = version * 4 + 17;\n      let row = [];\n      for (let i = 0; i < this.size; i++)\n        row.push(false);\n      for (let i = 0; i < this.size; i++) {\n        this.modules.push(row.slice());\n        this.isFunction.push(row.slice());\n      }\n      this.drawFunctionPatterns();\n      const allCodewords = this.addEccAndInterleave(dataCodewords);\n      this.drawCodewords(allCodewords);\n      if (msk == -1) {\n        let minPenalty = 1e9;\n        for (let i = 0; i < 8; i++) {\n          this.applyMask(i);\n          this.drawFormatBits(i);\n          const penalty = this.getPenaltyScore();\n          if (penalty < minPenalty) {\n            msk = i;\n            minPenalty = penalty;\n          }\n          this.applyMask(i);\n        }\n      }\n      assert(0 <= msk && msk <= 7);\n      this.mask = msk;\n      this.applyMask(msk);\n      this.drawFormatBits(msk);\n      this.isFunction = [];\n    }\n    /*-- Static factory functions (high level) --*/\n    // Returns a QR Code representing the given Unicode text string at the given error correction level.\n    // As a conservative upper bound, this function is guaranteed to succeed for strings that have 738 or fewer\n    // Unicode code points (not UTF-16 code units) if the low error correction level is used. The smallest possible\n    // QR Code version is automatically chosen for the output. The ECC level of the result may be higher than the\n    // ecl argument if it can be done without increasing the version.\n    static encodeText(text, ecl) {\n      const segs = qrcodegen2.QrSegment.makeSegments(text);\n      return _QrCode.encodeSegments(segs, ecl);\n    }\n    // Returns a QR Code representing the given binary data at the given error correction level.\n    // This function always encodes using the binary segment mode, not any text mode. The maximum number of\n    // bytes allowed is 2953. The smallest possible QR Code version is automatically chosen for the output.\n    // The ECC level of the result may be higher than the ecl argument if it can be done without increasing the version.\n    static encodeBinary(data, ecl) {\n      const seg = qrcodegen2.QrSegment.makeBytes(data);\n      return _QrCode.encodeSegments([seg], ecl);\n    }\n    /*-- Static factory functions (mid level) --*/\n    // Returns a QR Code representing the given segments with the given encoding parameters.\n    // The smallest possible QR Code version within the given range is automatically\n    // chosen for the output. Iff boostEcl is true, then the ECC level of the result\n    // may be higher than the ecl argument if it can be done without increasing the\n    // version. The mask number is either between 0 to 7 (inclusive) to force that\n    // mask, or -1 to automatically choose an appropriate mask (which may be slow).\n    // This function allows the user to create a custom sequence of segments that switches\n    // between modes (such as alphanumeric and byte) to encode text in less space.\n    // This is a mid-level API; the high-level API is encodeText() and encodeBinary().\n    static encodeSegments(segs, ecl, minVersion = 1, maxVersion = 40, mask = -1, boostEcl = true) {\n      if (!(_QrCode.MIN_VERSION <= minVersion && minVersion <= maxVersion && maxVersion <= _QrCode.MAX_VERSION) || mask < -1 || mask > 7)\n        throw new RangeError(\"Invalid value\");\n      let version;\n      let dataUsedBits;\n      for (version = minVersion; ; version++) {\n        const dataCapacityBits2 = _QrCode.getNumDataCodewords(version, ecl) * 8;\n        const usedBits = QrSegment.getTotalBits(segs, version);\n        if (usedBits <= dataCapacityBits2) {\n          dataUsedBits = usedBits;\n          break;\n        }\n        if (version >= maxVersion)\n          throw new RangeError(\"Data too long\");\n      }\n      for (const newEcl of [_QrCode.Ecc.MEDIUM, _QrCode.Ecc.QUARTILE, _QrCode.Ecc.HIGH]) {\n        if (boostEcl && dataUsedBits <= _QrCode.getNumDataCodewords(version, newEcl) * 8)\n          ecl = newEcl;\n      }\n      let bb = [];\n      for (const seg of segs) {\n        appendBits(seg.mode.modeBits, 4, bb);\n        appendBits(seg.numChars, seg.mode.numCharCountBits(version), bb);\n        for (const b of seg.getData())\n          bb.push(b);\n      }\n      assert(bb.length == dataUsedBits);\n      const dataCapacityBits = _QrCode.getNumDataCodewords(version, ecl) * 8;\n      assert(bb.length <= dataCapacityBits);\n      appendBits(0, Math.min(4, dataCapacityBits - bb.length), bb);\n      appendBits(0, (8 - bb.length % 8) % 8, bb);\n      assert(bb.length % 8 == 0);\n      for (let padByte = 236; bb.length < dataCapacityBits; padByte ^= 236 ^ 17)\n        appendBits(padByte, 8, bb);\n      let dataCodewords = [];\n      while (dataCodewords.length * 8 < bb.length)\n        dataCodewords.push(0);\n      bb.forEach((b, i) => dataCodewords[i >>> 3] |= b << 7 - (i & 7));\n      return new _QrCode(version, ecl, dataCodewords, mask);\n    }\n    /*-- Accessor methods --*/\n    // Returns the color of the module (pixel) at the given coordinates, which is false\n    // for light or true for dark. The top left corner has the coordinates (x=0, y=0).\n    // If the given coordinates are out of bounds, then false (light) is returned.\n    getModule(x, y) {\n      return 0 <= x && x < this.size && 0 <= y && y < this.size && this.modules[y][x];\n    }\n    // Modified to expose modules for easy access\n    getModules() {\n      return this.modules;\n    }\n    /*-- Private helper methods for constructor: Drawing function modules --*/\n    // Reads this object's version field, and draws and marks all function modules.\n    drawFunctionPatterns() {\n      for (let i = 0; i < this.size; i++) {\n        this.setFunctionModule(6, i, i % 2 == 0);\n        this.setFunctionModule(i, 6, i % 2 == 0);\n      }\n      this.drawFinderPattern(3, 3);\n      this.drawFinderPattern(this.size - 4, 3);\n      this.drawFinderPattern(3, this.size - 4);\n      const alignPatPos = this.getAlignmentPatternPositions();\n      const numAlign = alignPatPos.length;\n      for (let i = 0; i < numAlign; i++) {\n        for (let j = 0; j < numAlign; j++) {\n          if (!(i == 0 && j == 0 || i == 0 && j == numAlign - 1 || i == numAlign - 1 && j == 0))\n            this.drawAlignmentPattern(alignPatPos[i], alignPatPos[j]);\n        }\n      }\n      this.drawFormatBits(0);\n      this.drawVersion();\n    }\n    // Draws two copies of the format bits (with its own error correction code)\n    // based on the given mask and this object's error correction level field.\n    drawFormatBits(mask) {\n      const data = this.errorCorrectionLevel.formatBits << 3 | mask;\n      let rem = data;\n      for (let i = 0; i < 10; i++)\n        rem = rem << 1 ^ (rem >>> 9) * 1335;\n      const bits = (data << 10 | rem) ^ 21522;\n      assert(bits >>> 15 == 0);\n      for (let i = 0; i <= 5; i++)\n        this.setFunctionModule(8, i, getBit(bits, i));\n      this.setFunctionModule(8, 7, getBit(bits, 6));\n      this.setFunctionModule(8, 8, getBit(bits, 7));\n      this.setFunctionModule(7, 8, getBit(bits, 8));\n      for (let i = 9; i < 15; i++)\n        this.setFunctionModule(14 - i, 8, getBit(bits, i));\n      for (let i = 0; i < 8; i++)\n        this.setFunctionModule(this.size - 1 - i, 8, getBit(bits, i));\n      for (let i = 8; i < 15; i++)\n        this.setFunctionModule(8, this.size - 15 + i, getBit(bits, i));\n      this.setFunctionModule(8, this.size - 8, true);\n    }\n    // Draws two copies of the version bits (with its own error correction code),\n    // based on this object's version field, iff 7 <= version <= 40.\n    drawVersion() {\n      if (this.version < 7)\n        return;\n      let rem = this.version;\n      for (let i = 0; i < 12; i++)\n        rem = rem << 1 ^ (rem >>> 11) * 7973;\n      const bits = this.version << 12 | rem;\n      assert(bits >>> 18 == 0);\n      for (let i = 0; i < 18; i++) {\n        const color = getBit(bits, i);\n        const a = this.size - 11 + i % 3;\n        const b = Math.floor(i / 3);\n        this.setFunctionModule(a, b, color);\n        this.setFunctionModule(b, a, color);\n      }\n    }\n    // Draws a 9*9 finder pattern including the border separator,\n    // with the center module at (x, y). Modules can be out of bounds.\n    drawFinderPattern(x, y) {\n      for (let dy = -4; dy <= 4; dy++) {\n        for (let dx = -4; dx <= 4; dx++) {\n          const dist = Math.max(Math.abs(dx), Math.abs(dy));\n          const xx = x + dx;\n          const yy = y + dy;\n          if (0 <= xx && xx < this.size && 0 <= yy && yy < this.size)\n            this.setFunctionModule(xx, yy, dist != 2 && dist != 4);\n        }\n      }\n    }\n    // Draws a 5*5 alignment pattern, with the center module\n    // at (x, y). All modules must be in bounds.\n    drawAlignmentPattern(x, y) {\n      for (let dy = -2; dy <= 2; dy++) {\n        for (let dx = -2; dx <= 2; dx++)\n          this.setFunctionModule(x + dx, y + dy, Math.max(Math.abs(dx), Math.abs(dy)) != 1);\n      }\n    }\n    // Sets the color of a module and marks it as a function module.\n    // Only used by the constructor. Coordinates must be in bounds.\n    setFunctionModule(x, y, isDark) {\n      this.modules[y][x] = isDark;\n      this.isFunction[y][x] = true;\n    }\n    /*-- Private helper methods for constructor: Codewords and masking --*/\n    // Returns a new byte string representing the given data with the appropriate error correction\n    // codewords appended to it, based on this object's version and error correction level.\n    addEccAndInterleave(data) {\n      const ver = this.version;\n      const ecl = this.errorCorrectionLevel;\n      if (data.length != _QrCode.getNumDataCodewords(ver, ecl))\n        throw new RangeError(\"Invalid argument\");\n      const numBlocks = _QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];\n      const blockEccLen = _QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver];\n      const rawCodewords = Math.floor(_QrCode.getNumRawDataModules(ver) / 8);\n      const numShortBlocks = numBlocks - rawCodewords % numBlocks;\n      const shortBlockLen = Math.floor(rawCodewords / numBlocks);\n      let blocks = [];\n      const rsDiv = _QrCode.reedSolomonComputeDivisor(blockEccLen);\n      for (let i = 0, k = 0; i < numBlocks; i++) {\n        let dat = data.slice(k, k + shortBlockLen - blockEccLen + (i < numShortBlocks ? 0 : 1));\n        k += dat.length;\n        const ecc = _QrCode.reedSolomonComputeRemainder(dat, rsDiv);\n        if (i < numShortBlocks)\n          dat.push(0);\n        blocks.push(dat.concat(ecc));\n      }\n      let result = [];\n      for (let i = 0; i < blocks[0].length; i++) {\n        blocks.forEach((block, j) => {\n          if (i != shortBlockLen - blockEccLen || j >= numShortBlocks)\n            result.push(block[i]);\n        });\n      }\n      assert(result.length == rawCodewords);\n      return result;\n    }\n    // Draws the given sequence of 8-bit codewords (data and error correction) onto the entire\n    // data area of this QR Code. Function modules need to be marked off before this is called.\n    drawCodewords(data) {\n      if (data.length != Math.floor(_QrCode.getNumRawDataModules(this.version) / 8))\n        throw new RangeError(\"Invalid argument\");\n      let i = 0;\n      for (let right = this.size - 1; right >= 1; right -= 2) {\n        if (right == 6)\n          right = 5;\n        for (let vert = 0; vert < this.size; vert++) {\n          for (let j = 0; j < 2; j++) {\n            const x = right - j;\n            const upward = (right + 1 & 2) == 0;\n            const y = upward ? this.size - 1 - vert : vert;\n            if (!this.isFunction[y][x] && i < data.length * 8) {\n              this.modules[y][x] = getBit(data[i >>> 3], 7 - (i & 7));\n              i++;\n            }\n          }\n        }\n      }\n      assert(i == data.length * 8);\n    }\n    // XORs the codeword modules in this QR Code with the given mask pattern.\n    // The function modules must be marked and the codeword bits must be drawn\n    // before masking. Due to the arithmetic of XOR, calling applyMask() with\n    // the same mask value a second time will undo the mask. A final well-formed\n    // QR Code needs exactly one (not zero, two, etc.) mask applied.\n    applyMask(mask) {\n      if (mask < 0 || mask > 7)\n        throw new RangeError(\"Mask value out of range\");\n      for (let y = 0; y < this.size; y++) {\n        for (let x = 0; x < this.size; x++) {\n          let invert;\n          switch (mask) {\n            case 0:\n              invert = (x + y) % 2 == 0;\n              break;\n            case 1:\n              invert = y % 2 == 0;\n              break;\n            case 2:\n              invert = x % 3 == 0;\n              break;\n            case 3:\n              invert = (x + y) % 3 == 0;\n              break;\n            case 4:\n              invert = (Math.floor(x / 3) + Math.floor(y / 2)) % 2 == 0;\n              break;\n            case 5:\n              invert = x * y % 2 + x * y % 3 == 0;\n              break;\n            case 6:\n              invert = (x * y % 2 + x * y % 3) % 2 == 0;\n              break;\n            case 7:\n              invert = ((x + y) % 2 + x * y % 3) % 2 == 0;\n              break;\n            default:\n              throw new Error(\"Unreachable\");\n          }\n          if (!this.isFunction[y][x] && invert)\n            this.modules[y][x] = !this.modules[y][x];\n        }\n      }\n    }\n    // Calculates and returns the penalty score based on state of this QR Code's current modules.\n    // This is used by the automatic mask choice algorithm to find the mask pattern that yields the lowest score.\n    getPenaltyScore() {\n      let result = 0;\n      for (let y = 0; y < this.size; y++) {\n        let runColor = false;\n        let runX = 0;\n        let runHistory = [0, 0, 0, 0, 0, 0, 0];\n        for (let x = 0; x < this.size; x++) {\n          if (this.modules[y][x] == runColor) {\n            runX++;\n            if (runX == 5)\n              result += _QrCode.PENALTY_N1;\n            else if (runX > 5)\n              result++;\n          } else {\n            this.finderPenaltyAddHistory(runX, runHistory);\n            if (!runColor)\n              result += this.finderPenaltyCountPatterns(runHistory) * _QrCode.PENALTY_N3;\n            runColor = this.modules[y][x];\n            runX = 1;\n          }\n        }\n        result += this.finderPenaltyTerminateAndCount(runColor, runX, runHistory) * _QrCode.PENALTY_N3;\n      }\n      for (let x = 0; x < this.size; x++) {\n        let runColor = false;\n        let runY = 0;\n        let runHistory = [0, 0, 0, 0, 0, 0, 0];\n        for (let y = 0; y < this.size; y++) {\n          if (this.modules[y][x] == runColor) {\n            runY++;\n            if (runY == 5)\n              result += _QrCode.PENALTY_N1;\n            else if (runY > 5)\n              result++;\n          } else {\n            this.finderPenaltyAddHistory(runY, runHistory);\n            if (!runColor)\n              result += this.finderPenaltyCountPatterns(runHistory) * _QrCode.PENALTY_N3;\n            runColor = this.modules[y][x];\n            runY = 1;\n          }\n        }\n        result += this.finderPenaltyTerminateAndCount(runColor, runY, runHistory) * _QrCode.PENALTY_N3;\n      }\n      for (let y = 0; y < this.size - 1; y++) {\n        for (let x = 0; x < this.size - 1; x++) {\n          const color = this.modules[y][x];\n          if (color == this.modules[y][x + 1] && color == this.modules[y + 1][x] && color == this.modules[y + 1][x + 1])\n            result += _QrCode.PENALTY_N2;\n        }\n      }\n      let dark = 0;\n      for (const row of this.modules)\n        dark = row.reduce((sum, color) => sum + (color ? 1 : 0), dark);\n      const total = this.size * this.size;\n      const k = Math.ceil(Math.abs(dark * 20 - total * 10) / total) - 1;\n      assert(0 <= k && k <= 9);\n      result += k * _QrCode.PENALTY_N4;\n      assert(0 <= result && result <= 2568888);\n      return result;\n    }\n    /*-- Private helper functions --*/\n    // Returns an ascending list of positions of alignment patterns for this version number.\n    // Each position is in the range [0,177), and are used on both the x and y axes.\n    // This could be implemented as lookup table of 40 variable-length lists of integers.\n    getAlignmentPatternPositions() {\n      if (this.version == 1)\n        return [];\n      else {\n        const numAlign = Math.floor(this.version / 7) + 2;\n        const step = this.version == 32 ? 26 : Math.ceil((this.version * 4 + 4) / (numAlign * 2 - 2)) * 2;\n        let result = [6];\n        for (let pos = this.size - 7; result.length < numAlign; pos -= step)\n          result.splice(1, 0, pos);\n        return result;\n      }\n    }\n    // Returns the number of data bits that can be stored in a QR Code of the given version number, after\n    // all function modules are excluded. This includes remainder bits, so it might not be a multiple of 8.\n    // The result is in the range [208, 29648]. This could be implemented as a 40-entry lookup table.\n    static getNumRawDataModules(ver) {\n      if (ver < _QrCode.MIN_VERSION || ver > _QrCode.MAX_VERSION)\n        throw new RangeError(\"Version number out of range\");\n      let result = (16 * ver + 128) * ver + 64;\n      if (ver >= 2) {\n        const numAlign = Math.floor(ver / 7) + 2;\n        result -= (25 * numAlign - 10) * numAlign - 55;\n        if (ver >= 7)\n          result -= 36;\n      }\n      assert(208 <= result && result <= 29648);\n      return result;\n    }\n    // Returns the number of 8-bit data (i.e. not error correction) codewords contained in any\n    // QR Code of the given version number and error correction level, with remainder bits discarded.\n    // This stateless pure function could be implemented as a (40*4)-cell lookup table.\n    static getNumDataCodewords(ver, ecl) {\n      return Math.floor(_QrCode.getNumRawDataModules(ver) / 8) - _QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver] * _QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];\n    }\n    // Returns a Reed-Solomon ECC generator polynomial for the given degree. This could be\n    // implemented as a lookup table over all possible parameter values, instead of as an algorithm.\n    static reedSolomonComputeDivisor(degree) {\n      if (degree < 1 || degree > 255)\n        throw new RangeError(\"Degree out of range\");\n      let result = [];\n      for (let i = 0; i < degree - 1; i++)\n        result.push(0);\n      result.push(1);\n      let root = 1;\n      for (let i = 0; i < degree; i++) {\n        for (let j = 0; j < result.length; j++) {\n          result[j] = _QrCode.reedSolomonMultiply(result[j], root);\n          if (j + 1 < result.length)\n            result[j] ^= result[j + 1];\n        }\n        root = _QrCode.reedSolomonMultiply(root, 2);\n      }\n      return result;\n    }\n    // Returns the Reed-Solomon error correction codeword for the given data and divisor polynomials.\n    static reedSolomonComputeRemainder(data, divisor) {\n      let result = divisor.map((_) => 0);\n      for (const b of data) {\n        const factor = b ^ result.shift();\n        result.push(0);\n        divisor.forEach((coef, i) => result[i] ^= _QrCode.reedSolomonMultiply(coef, factor));\n      }\n      return result;\n    }\n    // Returns the product of the two given field elements modulo GF(2^8/0x11D). The arguments and result\n    // are unsigned 8-bit integers. This could be implemented as a lookup table of 256*256 entries of uint8.\n    static reedSolomonMultiply(x, y) {\n      if (x >>> 8 != 0 || y >>> 8 != 0)\n        throw new RangeError(\"Byte out of range\");\n      let z = 0;\n      for (let i = 7; i >= 0; i--) {\n        z = z << 1 ^ (z >>> 7) * 285;\n        z ^= (y >>> i & 1) * x;\n      }\n      assert(z >>> 8 == 0);\n      return z;\n    }\n    // Can only be called immediately after a light run is added, and\n    // returns either 0, 1, or 2. A helper function for getPenaltyScore().\n    finderPenaltyCountPatterns(runHistory) {\n      const n = runHistory[1];\n      assert(n <= this.size * 3);\n      const core = n > 0 && runHistory[2] == n && runHistory[3] == n * 3 && runHistory[4] == n && runHistory[5] == n;\n      return (core && runHistory[0] >= n * 4 && runHistory[6] >= n ? 1 : 0) + (core && runHistory[6] >= n * 4 && runHistory[0] >= n ? 1 : 0);\n    }\n    // Must be called at the end of a line (row or column) of modules. A helper function for getPenaltyScore().\n    finderPenaltyTerminateAndCount(currentRunColor, currentRunLength, runHistory) {\n      if (currentRunColor) {\n        this.finderPenaltyAddHistory(currentRunLength, runHistory);\n        currentRunLength = 0;\n      }\n      currentRunLength += this.size;\n      this.finderPenaltyAddHistory(currentRunLength, runHistory);\n      return this.finderPenaltyCountPatterns(runHistory);\n    }\n    // Pushes the given value to the front and drops the last value. A helper function for getPenaltyScore().\n    finderPenaltyAddHistory(currentRunLength, runHistory) {\n      if (runHistory[0] == 0)\n        currentRunLength += this.size;\n      runHistory.pop();\n      runHistory.unshift(currentRunLength);\n    }\n  };\n  /*-- Constants and tables --*/\n  // The minimum version number supported in the QR Code Model 2 standard.\n  _QrCode.MIN_VERSION = 1;\n  // The maximum version number supported in the QR Code Model 2 standard.\n  _QrCode.MAX_VERSION = 40;\n  // For use in getPenaltyScore(), when evaluating which mask is best.\n  _QrCode.PENALTY_N1 = 3;\n  _QrCode.PENALTY_N2 = 3;\n  _QrCode.PENALTY_N3 = 40;\n  _QrCode.PENALTY_N4 = 10;\n  _QrCode.ECC_CODEWORDS_PER_BLOCK = [\n    // Version: (note that index 0 is for padding, and is set to an illegal value)\n    //0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40    Error correction level\n    [-1, 7, 10, 15, 20, 26, 18, 20, 24, 30, 18, 20, 24, 26, 30, 22, 24, 28, 30, 28, 28, 28, 28, 30, 30, 26, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],\n    // Low\n    [-1, 10, 16, 26, 18, 24, 16, 18, 22, 22, 26, 30, 22, 22, 24, 24, 28, 28, 26, 26, 26, 26, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28],\n    // Medium\n    [-1, 13, 22, 18, 26, 18, 24, 18, 22, 20, 24, 28, 26, 24, 20, 30, 24, 28, 28, 26, 30, 28, 30, 30, 30, 30, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],\n    // Quartile\n    [-1, 17, 28, 22, 16, 22, 28, 26, 26, 24, 28, 24, 28, 22, 24, 24, 30, 28, 28, 26, 28, 30, 24, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30]\n    // High\n  ];\n  _QrCode.NUM_ERROR_CORRECTION_BLOCKS = [\n    // Version: (note that index 0 is for padding, and is set to an illegal value)\n    //0, 1, 2, 3, 4, 5, 6, 7, 8, 9,10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40    Error correction level\n    [-1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 4, 4, 4, 4, 4, 6, 6, 6, 6, 7, 8, 8, 9, 9, 10, 12, 12, 12, 13, 14, 15, 16, 17, 18, 19, 19, 20, 21, 22, 24, 25],\n    // Low\n    [-1, 1, 1, 1, 2, 2, 4, 4, 4, 5, 5, 5, 8, 9, 9, 10, 10, 11, 13, 14, 16, 17, 17, 18, 20, 21, 23, 25, 26, 28, 29, 31, 33, 35, 37, 38, 40, 43, 45, 47, 49],\n    // Medium\n    [-1, 1, 1, 2, 2, 4, 4, 6, 6, 8, 8, 8, 10, 12, 16, 12, 17, 16, 18, 21, 20, 23, 23, 25, 27, 29, 34, 34, 35, 38, 40, 43, 45, 48, 51, 53, 56, 59, 62, 65, 68],\n    // Quartile\n    [-1, 1, 1, 2, 4, 4, 4, 5, 6, 8, 8, 11, 11, 16, 16, 18, 16, 19, 21, 25, 25, 25, 34, 30, 32, 35, 37, 40, 42, 45, 48, 51, 54, 57, 60, 63, 66, 70, 74, 77, 81]\n    // High\n  ];\n  let QrCode = _QrCode;\n  qrcodegen2.QrCode = _QrCode;\n  function appendBits(val, len, bb) {\n    if (len < 0 || len > 31 || val >>> len != 0)\n      throw new RangeError(\"Value out of range\");\n    for (let i = len - 1; i >= 0; i--)\n      bb.push(val >>> i & 1);\n  }\n  function getBit(x, i) {\n    return (x >>> i & 1) != 0;\n  }\n  function assert(cond) {\n    if (!cond)\n      throw new Error(\"Assertion error\");\n  }\n  const _QrSegment = class _QrSegment {\n    /*-- Constructor (low level) and fields --*/\n    // Creates a new QR Code segment with the given attributes and data.\n    // The character count (numChars) must agree with the mode and the bit buffer length,\n    // but the constraint isn't checked. The given bit buffer is cloned and stored.\n    constructor(mode, numChars, bitData) {\n      this.mode = mode;\n      this.numChars = numChars;\n      this.bitData = bitData;\n      if (numChars < 0)\n        throw new RangeError(\"Invalid argument\");\n      this.bitData = bitData.slice();\n    }\n    /*-- Static factory functions (mid level) --*/\n    // Returns a segment representing the given binary data encoded in\n    // byte mode. All input byte arrays are acceptable. Any text string\n    // can be converted to UTF-8 bytes and encoded as a byte mode segment.\n    static makeBytes(data) {\n      let bb = [];\n      for (const b of data)\n        appendBits(b, 8, bb);\n      return new _QrSegment(_QrSegment.Mode.BYTE, data.length, bb);\n    }\n    // Returns a segment representing the given string of decimal digits encoded in numeric mode.\n    static makeNumeric(digits) {\n      if (!_QrSegment.isNumeric(digits))\n        throw new RangeError(\"String contains non-numeric characters\");\n      let bb = [];\n      for (let i = 0; i < digits.length; ) {\n        const n = Math.min(digits.length - i, 3);\n        appendBits(parseInt(digits.substring(i, i + n), 10), n * 3 + 1, bb);\n        i += n;\n      }\n      return new _QrSegment(_QrSegment.Mode.NUMERIC, digits.length, bb);\n    }\n    // Returns a segment representing the given text string encoded in alphanumeric mode.\n    // The characters allowed are: 0 to 9, A to Z (uppercase only), space,\n    // dollar, percent, asterisk, plus, hyphen, period, slash, colon.\n    static makeAlphanumeric(text) {\n      if (!_QrSegment.isAlphanumeric(text))\n        throw new RangeError(\"String contains unencodable characters in alphanumeric mode\");\n      let bb = [];\n      let i;\n      for (i = 0; i + 2 <= text.length; i += 2) {\n        let temp = _QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)) * 45;\n        temp += _QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i + 1));\n        appendBits(temp, 11, bb);\n      }\n      if (i < text.length)\n        appendBits(_QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)), 6, bb);\n      return new _QrSegment(_QrSegment.Mode.ALPHANUMERIC, text.length, bb);\n    }\n    // Returns a new mutable list of zero or more segments to represent the given Unicode text string.\n    // The result may use various segment modes and switch modes to optimize the length of the bit stream.\n    static makeSegments(text) {\n      if (text == \"\")\n        return [];\n      else if (_QrSegment.isNumeric(text))\n        return [_QrSegment.makeNumeric(text)];\n      else if (_QrSegment.isAlphanumeric(text))\n        return [_QrSegment.makeAlphanumeric(text)];\n      else\n        return [_QrSegment.makeBytes(_QrSegment.toUtf8ByteArray(text))];\n    }\n    // Returns a segment representing an Extended Channel Interpretation\n    // (ECI) designator with the given assignment value.\n    static makeEci(assignVal) {\n      let bb = [];\n      if (assignVal < 0)\n        throw new RangeError(\"ECI assignment value out of range\");\n      else if (assignVal < 1 << 7)\n        appendBits(assignVal, 8, bb);\n      else if (assignVal < 1 << 14) {\n        appendBits(2, 2, bb);\n        appendBits(assignVal, 14, bb);\n      } else if (assignVal < 1e6) {\n        appendBits(6, 3, bb);\n        appendBits(assignVal, 21, bb);\n      } else\n        throw new RangeError(\"ECI assignment value out of range\");\n      return new _QrSegment(_QrSegment.Mode.ECI, 0, bb);\n    }\n    // Tests whether the given string can be encoded as a segment in numeric mode.\n    // A string is encodable iff each character is in the range 0 to 9.\n    static isNumeric(text) {\n      return _QrSegment.NUMERIC_REGEX.test(text);\n    }\n    // Tests whether the given string can be encoded as a segment in alphanumeric mode.\n    // A string is encodable iff each character is in the following set: 0 to 9, A to Z\n    // (uppercase only), space, dollar, percent, asterisk, plus, hyphen, period, slash, colon.\n    static isAlphanumeric(text) {\n      return _QrSegment.ALPHANUMERIC_REGEX.test(text);\n    }\n    /*-- Methods --*/\n    // Returns a new copy of the data bits of this segment.\n    getData() {\n      return this.bitData.slice();\n    }\n    // (Package-private) Calculates and returns the number of bits needed to encode the given segments at\n    // the given version. The result is infinity if a segment has too many characters to fit its length field.\n    static getTotalBits(segs, version) {\n      let result = 0;\n      for (const seg of segs) {\n        const ccbits = seg.mode.numCharCountBits(version);\n        if (seg.numChars >= 1 << ccbits)\n          return Infinity;\n        result += 4 + ccbits + seg.bitData.length;\n      }\n      return result;\n    }\n    // Returns a new array of bytes representing the given string encoded in UTF-8.\n    static toUtf8ByteArray(str) {\n      str = encodeURI(str);\n      let result = [];\n      for (let i = 0; i < str.length; i++) {\n        if (str.charAt(i) != \"%\")\n          result.push(str.charCodeAt(i));\n        else {\n          result.push(parseInt(str.substring(i + 1, i + 3), 16));\n          i += 2;\n        }\n      }\n      return result;\n    }\n  };\n  /*-- Constants --*/\n  // Describes precisely all strings that are encodable in numeric mode.\n  _QrSegment.NUMERIC_REGEX = /^[0-9]*$/;\n  // Describes precisely all strings that are encodable in alphanumeric mode.\n  _QrSegment.ALPHANUMERIC_REGEX = /^[A-Z0-9 $%*+.\\/:-]*$/;\n  // The set of all legal characters in alphanumeric mode,\n  // where each character value maps to the index in the string.\n  _QrSegment.ALPHANUMERIC_CHARSET = \"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:\";\n  let QrSegment = _QrSegment;\n  qrcodegen2.QrSegment = _QrSegment;\n})(qrcodegen || (qrcodegen = {}));\n((qrcodegen2) => {\n  let QrCode;\n  ((QrCode2) => {\n    const _Ecc = class _Ecc {\n      // The QR Code can tolerate about 30% erroneous codewords\n      /*-- Constructor and fields --*/\n      constructor(ordinal, formatBits) {\n        this.ordinal = ordinal;\n        this.formatBits = formatBits;\n      }\n    };\n    /*-- Constants --*/\n    _Ecc.LOW = new _Ecc(0, 1);\n    // The QR Code can tolerate about  7% erroneous codewords\n    _Ecc.MEDIUM = new _Ecc(1, 0);\n    // The QR Code can tolerate about 15% erroneous codewords\n    _Ecc.QUARTILE = new _Ecc(2, 3);\n    // The QR Code can tolerate about 25% erroneous codewords\n    _Ecc.HIGH = new _Ecc(3, 2);\n    let Ecc = _Ecc;\n    QrCode2.Ecc = _Ecc;\n  })(QrCode = qrcodegen2.QrCode || (qrcodegen2.QrCode = {}));\n})(qrcodegen || (qrcodegen = {}));\n((qrcodegen2) => {\n  let QrSegment;\n  ((QrSegment2) => {\n    const _Mode = class _Mode {\n      /*-- Constructor and fields --*/\n      constructor(modeBits, numBitsCharCount) {\n        this.modeBits = modeBits;\n        this.numBitsCharCount = numBitsCharCount;\n      }\n      /*-- Method --*/\n      // (Package-private) Returns the bit width of the character count field for a segment in\n      // this mode in a QR Code at the given version number. The result is in the range [0, 16].\n      numCharCountBits(ver) {\n        return this.numBitsCharCount[Math.floor((ver + 7) / 17)];\n      }\n    };\n    /*-- Constants --*/\n    _Mode.NUMERIC = new _Mode(1, [10, 12, 14]);\n    _Mode.ALPHANUMERIC = new _Mode(2, [9, 11, 13]);\n    _Mode.BYTE = new _Mode(4, [8, 16, 16]);\n    _Mode.KANJI = new _Mode(8, [8, 10, 12]);\n    _Mode.ECI = new _Mode(7, [0, 0, 0]);\n    let Mode = _Mode;\n    QrSegment2.Mode = _Mode;\n  })(QrSegment = qrcodegen2.QrSegment || (qrcodegen2.QrSegment = {}));\n})(qrcodegen || (qrcodegen = {}));\nvar qrcodegen_default = qrcodegen;\n\n// src/index.tsx\n/**\n * @license qrcode.react\n * Copyright (c) Paul O'Shannessy\n * SPDX-License-Identifier: ISC\n */\nvar ERROR_LEVEL_MAP = {\n  L: qrcodegen_default.QrCode.Ecc.LOW,\n  M: qrcodegen_default.QrCode.Ecc.MEDIUM,\n  Q: qrcodegen_default.QrCode.Ecc.QUARTILE,\n  H: qrcodegen_default.QrCode.Ecc.HIGH\n};\nvar DEFAULT_SIZE = 128;\nvar DEFAULT_LEVEL = \"L\";\nvar DEFAULT_BGCOLOR = \"#FFFFFF\";\nvar DEFAULT_FGCOLOR = \"#000000\";\nvar DEFAULT_INCLUDEMARGIN = false;\nvar DEFAULT_MINVERSION = 1;\nvar SPEC_MARGIN_SIZE = 4;\nvar DEFAULT_MARGIN_SIZE = 0;\nvar DEFAULT_IMG_SCALE = 0.1;\nfunction generatePath(modules, margin = 0) {\n  const ops = [];\n  modules.forEach(function(row, y) {\n    let start = null;\n    row.forEach(function(cell, x) {\n      if (!cell && start !== null) {\n        ops.push(\n          `M${start + margin} ${y + margin}h${x - start}v1H${start + margin}z`\n        );\n        start = null;\n        return;\n      }\n      if (x === row.length - 1) {\n        if (!cell) {\n          return;\n        }\n        if (start === null) {\n          ops.push(`M${x + margin},${y + margin} h1v1H${x + margin}z`);\n        } else {\n          ops.push(\n            `M${start + margin},${y + margin} h${x + 1 - start}v1H${start + margin}z`\n          );\n        }\n        return;\n      }\n      if (cell && start === null) {\n        start = x;\n      }\n    });\n  });\n  return ops.join(\"\");\n}\nfunction excavateModules(modules, excavation) {\n  return modules.slice().map((row, y) => {\n    if (y < excavation.y || y >= excavation.y + excavation.h) {\n      return row;\n    }\n    return row.map((cell, x) => {\n      if (x < excavation.x || x >= excavation.x + excavation.w) {\n        return cell;\n      }\n      return false;\n    });\n  });\n}\nfunction getImageSettings(cells, size, margin, imageSettings) {\n  if (imageSettings == null) {\n    return null;\n  }\n  const numCells = cells.length + margin * 2;\n  const defaultSize = Math.floor(size * DEFAULT_IMG_SCALE);\n  const scale = numCells / size;\n  const w = (imageSettings.width || defaultSize) * scale;\n  const h = (imageSettings.height || defaultSize) * scale;\n  const x = imageSettings.x == null ? cells.length / 2 - w / 2 : imageSettings.x * scale;\n  const y = imageSettings.y == null ? cells.length / 2 - h / 2 : imageSettings.y * scale;\n  const opacity = imageSettings.opacity == null ? 1 : imageSettings.opacity;\n  let excavation = null;\n  if (imageSettings.excavate) {\n    let floorX = Math.floor(x);\n    let floorY = Math.floor(y);\n    let ceilW = Math.ceil(w + x - floorX);\n    let ceilH = Math.ceil(h + y - floorY);\n    excavation = { x: floorX, y: floorY, w: ceilW, h: ceilH };\n  }\n  const crossOrigin = imageSettings.crossOrigin;\n  return { x, y, h, w, excavation, opacity, crossOrigin };\n}\nfunction getMarginSize(includeMargin, marginSize) {\n  if (marginSize != null) {\n    return Math.max(Math.floor(marginSize), 0);\n  }\n  return includeMargin ? SPEC_MARGIN_SIZE : DEFAULT_MARGIN_SIZE;\n}\nfunction useQRCode({\n  value,\n  level,\n  minVersion,\n  includeMargin,\n  marginSize,\n  imageSettings,\n  size,\n  boostLevel\n}) {\n  let qrcode = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    const values = Array.isArray(value) ? value : [value];\n    const segments = values.reduce((accum, v) => {\n      accum.push(...qrcodegen_default.QrSegment.makeSegments(v));\n      return accum;\n    }, []);\n    return qrcodegen_default.QrCode.encodeSegments(\n      segments,\n      ERROR_LEVEL_MAP[level],\n      minVersion,\n      void 0,\n      void 0,\n      boostLevel\n    );\n  }, [value, level, minVersion, boostLevel]);\n  const { cells, margin, numCells, calculatedImageSettings } = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    let cells2 = qrcode.getModules();\n    const margin2 = getMarginSize(includeMargin, marginSize);\n    const numCells2 = cells2.length + margin2 * 2;\n    const calculatedImageSettings2 = getImageSettings(\n      cells2,\n      size,\n      margin2,\n      imageSettings\n    );\n    return {\n      cells: cells2,\n      margin: margin2,\n      numCells: numCells2,\n      calculatedImageSettings: calculatedImageSettings2\n    };\n  }, [qrcode, size, imageSettings, includeMargin, marginSize]);\n  return {\n    qrcode,\n    margin,\n    cells,\n    numCells,\n    calculatedImageSettings\n  };\n}\nvar SUPPORTS_PATH2D = function() {\n  try {\n    new Path2D().addPath(new Path2D());\n  } catch (e) {\n    return false;\n  }\n  return true;\n}();\nvar QRCodeCanvas = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  function QRCodeCanvas2(props, forwardedRef) {\n    const _a = props, {\n      value,\n      size = DEFAULT_SIZE,\n      level = DEFAULT_LEVEL,\n      bgColor = DEFAULT_BGCOLOR,\n      fgColor = DEFAULT_FGCOLOR,\n      includeMargin = DEFAULT_INCLUDEMARGIN,\n      minVersion = DEFAULT_MINVERSION,\n      boostLevel,\n      marginSize,\n      imageSettings\n    } = _a, extraProps = __objRest(_a, [\n      \"value\",\n      \"size\",\n      \"level\",\n      \"bgColor\",\n      \"fgColor\",\n      \"includeMargin\",\n      \"minVersion\",\n      \"boostLevel\",\n      \"marginSize\",\n      \"imageSettings\"\n    ]);\n    const _b = extraProps, { style } = _b, otherProps = __objRest(_b, [\"style\"]);\n    const imgSrc = imageSettings == null ? void 0 : imageSettings.src;\n    const _canvas = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const _image = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const setCanvasRef = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n      (node) => {\n        _canvas.current = node;\n        if (typeof forwardedRef === \"function\") {\n          forwardedRef(node);\n        } else if (forwardedRef) {\n          forwardedRef.current = node;\n        }\n      },\n      [forwardedRef]\n    );\n    const [isImgLoaded, setIsImageLoaded] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const { margin, cells, numCells, calculatedImageSettings } = useQRCode({\n      value,\n      level,\n      minVersion,\n      boostLevel,\n      includeMargin,\n      marginSize,\n      imageSettings,\n      size\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      if (_canvas.current != null) {\n        const canvas = _canvas.current;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) {\n          return;\n        }\n        let cellsToDraw = cells;\n        const image = _image.current;\n        const haveImageToRender = calculatedImageSettings != null && image !== null && image.complete && image.naturalHeight !== 0 && image.naturalWidth !== 0;\n        if (haveImageToRender) {\n          if (calculatedImageSettings.excavation != null) {\n            cellsToDraw = excavateModules(\n              cells,\n              calculatedImageSettings.excavation\n            );\n          }\n        }\n        const pixelRatio = window.devicePixelRatio || 1;\n        canvas.height = canvas.width = size * pixelRatio;\n        const scale = size / numCells * pixelRatio;\n        ctx.scale(scale, scale);\n        ctx.fillStyle = bgColor;\n        ctx.fillRect(0, 0, numCells, numCells);\n        ctx.fillStyle = fgColor;\n        if (SUPPORTS_PATH2D) {\n          ctx.fill(new Path2D(generatePath(cellsToDraw, margin)));\n        } else {\n          cells.forEach(function(row, rdx) {\n            row.forEach(function(cell, cdx) {\n              if (cell) {\n                ctx.fillRect(cdx + margin, rdx + margin, 1, 1);\n              }\n            });\n          });\n        }\n        if (calculatedImageSettings) {\n          ctx.globalAlpha = calculatedImageSettings.opacity;\n        }\n        if (haveImageToRender) {\n          ctx.drawImage(\n            image,\n            calculatedImageSettings.x + margin,\n            calculatedImageSettings.y + margin,\n            calculatedImageSettings.w,\n            calculatedImageSettings.h\n          );\n        }\n      }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      setIsImageLoaded(false);\n    }, [imgSrc]);\n    const canvasStyle = __spreadValues({ height: size, width: size }, style);\n    let img = null;\n    if (imgSrc != null) {\n      img = /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n        \"img\",\n        {\n          src: imgSrc,\n          key: imgSrc,\n          style: { display: \"none\" },\n          onLoad: () => {\n            setIsImageLoaded(true);\n          },\n          ref: _image,\n          crossOrigin: calculatedImageSettings == null ? void 0 : calculatedImageSettings.crossOrigin\n        }\n      );\n    }\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n      \"canvas\",\n      __spreadValues({\n        style: canvasStyle,\n        height: size,\n        width: size,\n        ref: setCanvasRef,\n        role: \"img\"\n      }, otherProps)\n    ), img);\n  }\n);\nQRCodeCanvas.displayName = \"QRCodeCanvas\";\nvar QRCodeSVG = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  function QRCodeSVG2(props, forwardedRef) {\n    const _a = props, {\n      value,\n      size = DEFAULT_SIZE,\n      level = DEFAULT_LEVEL,\n      bgColor = DEFAULT_BGCOLOR,\n      fgColor = DEFAULT_FGCOLOR,\n      includeMargin = DEFAULT_INCLUDEMARGIN,\n      minVersion = DEFAULT_MINVERSION,\n      boostLevel,\n      title,\n      marginSize,\n      imageSettings\n    } = _a, otherProps = __objRest(_a, [\n      \"value\",\n      \"size\",\n      \"level\",\n      \"bgColor\",\n      \"fgColor\",\n      \"includeMargin\",\n      \"minVersion\",\n      \"boostLevel\",\n      \"title\",\n      \"marginSize\",\n      \"imageSettings\"\n    ]);\n    const { margin, cells, numCells, calculatedImageSettings } = useQRCode({\n      value,\n      level,\n      minVersion,\n      boostLevel,\n      includeMargin,\n      marginSize,\n      imageSettings,\n      size\n    });\n    let cellsToDraw = cells;\n    let image = null;\n    if (imageSettings != null && calculatedImageSettings != null) {\n      if (calculatedImageSettings.excavation != null) {\n        cellsToDraw = excavateModules(\n          cells,\n          calculatedImageSettings.excavation\n        );\n      }\n      image = /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n        \"image\",\n        {\n          href: imageSettings.src,\n          height: calculatedImageSettings.h,\n          width: calculatedImageSettings.w,\n          x: calculatedImageSettings.x + margin,\n          y: calculatedImageSettings.y + margin,\n          preserveAspectRatio: \"none\",\n          opacity: calculatedImageSettings.opacity,\n          crossOrigin: calculatedImageSettings.crossOrigin\n        }\n      );\n    }\n    const fgPath = generatePath(cellsToDraw, margin);\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n      \"svg\",\n      __spreadValues({\n        height: size,\n        width: size,\n        viewBox: `0 0 ${numCells} ${numCells}`,\n        ref: forwardedRef,\n        role: \"img\"\n      }, otherProps),\n      !!title && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", null, title),\n      /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n        \"path\",\n        {\n          fill: bgColor,\n          d: `M0,0 h${numCells}v${numCells}H0z`,\n          shapeRendering: \"crispEdges\"\n        }\n      ),\n      /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", { fill: fgColor, d: fgPath, shapeRendering: \"crispEdges\" }),\n      image\n    );\n  }\n);\nQRCodeSVG.displayName = \"QRCodeSVG\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/qrcode.react@4.2.0_react@19.0.0/node_modules/qrcode.react/lib/esm/index.js\n");

/***/ })

};
;