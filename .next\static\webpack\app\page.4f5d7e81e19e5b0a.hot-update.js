"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/LaundryCard.tsx":
/*!****************************************!*\
  !*** ./src/components/LaundryCard.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LaundryCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/src/hooks/useSupabaseData */ \"(app-pages-browser)/./src/hooks/useSupabaseData.tsx\");\n/* harmony import */ var _src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/hooks/useCountdown */ \"(app-pages-browser)/./src/hooks/useCountdown.tsx\");\n/* harmony import */ var _IncidentBadge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./IncidentBadge */ \"(app-pages-browser)/./src/components/IncidentBadge.tsx\");\n/* harmony import */ var _ui_CardWrapper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/CardWrapper */ \"(app-pages-browser)/./src/components/ui/CardWrapper.tsx\");\n/* harmony import */ var _ui_WasherSVG__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/WasherSVG */ \"(app-pages-browser)/./src/components/ui/WasherSVG.tsx\");\n/* harmony import */ var _ui_DryerOutlineSVG__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ui/DryerOutlineSVG */ \"(app-pages-browser)/./src/components/ui/DryerOutlineSVG.tsx\");\n/* harmony import */ var _src_utils_timeUtils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/src/utils/timeUtils */ \"(app-pages-browser)/./src/utils/timeUtils.ts\");\n/* harmony import */ var _src_utils_machineOwnership__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/src/utils/machineOwnership */ \"(app-pages-browser)/./src/utils/machineOwnership.ts\");\n/* harmony import */ var _TimeAdjustmentModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./TimeAdjustmentModal */ \"(app-pages-browser)/./src/components/TimeAdjustmentModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Component for \"Just Updated\" badge\nfunction JustUpdatedBadge(param) {\n    let { machine } = param;\n    _s();\n    const [showBadge, setShowBadge] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"JustUpdatedBadge.useEffect\": ()=>{\n            if ((0,_src_utils_timeUtils__WEBPACK_IMPORTED_MODULE_8__.isRecentlyUpdated)(machine.updatedAt)) {\n                setShowBadge(true);\n                // Hide badge after 10 seconds\n                const timer = setTimeout({\n                    \"JustUpdatedBadge.useEffect.timer\": ()=>setShowBadge(false)\n                }[\"JustUpdatedBadge.useEffect.timer\"], 10000);\n                return ({\n                    \"JustUpdatedBadge.useEffect\": ()=>clearTimeout(timer)\n                })[\"JustUpdatedBadge.useEffect\"];\n            }\n        }\n    }[\"JustUpdatedBadge.useEffect\"], [\n        machine.updatedAt\n    ]);\n    if (!showBadge) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"absolute -top-2 -left-2 z-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-blue-500 text-white text-xs px-2 py-1 rounded-full shadow-lg animate-pulse-slow\",\n            children: \"Just Updated\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n_s(JustUpdatedBadge, \"mDNabOB/uIKS4gwTk58LpTtHb0g=\");\n_c = JustUpdatedBadge;\n// Component for time ago display\nfunction TimeAgoDisplay(param) {\n    let { machine } = param;\n    _s1();\n    const [timeAgo, setTimeAgo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TimeAgoDisplay.useEffect\": ()=>{\n            const updateTimeAgo = {\n                \"TimeAgoDisplay.useEffect.updateTimeAgo\": ()=>{\n                    setTimeAgo((0,_src_utils_timeUtils__WEBPACK_IMPORTED_MODULE_8__.formatTimeAgo)(machine.updatedAt));\n                }\n            }[\"TimeAgoDisplay.useEffect.updateTimeAgo\"];\n            updateTimeAgo();\n            // Update every 30 seconds\n            const interval = setInterval(updateTimeAgo, 30000);\n            return ({\n                \"TimeAgoDisplay.useEffect\": ()=>clearInterval(interval)\n            })[\"TimeAgoDisplay.useEffect\"];\n        }\n    }[\"TimeAgoDisplay.useEffect\"], [\n        machine.updatedAt\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-xs text-gray-500 mt-1 text-center\",\n        children: timeAgo\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n_s1(TimeAgoDisplay, \"IBf0cw+KUIsg95VJGn4acX1ZO9o=\");\n_c1 = TimeAgoDisplay;\n// Component for ownership badge\nfunction OwnershipBadge(param) {\n    let { machine } = param;\n    if (!(0,_src_utils_machineOwnership__WEBPACK_IMPORTED_MODULE_9__.hasOwner)(machine)) return null;\n    const ownershipText = (0,_src_utils_machineOwnership__WEBPACK_IMPORTED_MODULE_9__.getOwnershipDisplay)(machine);\n    const badgeClasses = (0,_src_utils_machineOwnership__WEBPACK_IMPORTED_MODULE_9__.getOwnershipBadgeClasses)(machine);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border \".concat(badgeClasses),\n        children: ownershipText\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n_c2 = OwnershipBadge;\n// Component for time adjustment button\nfunction TimeAdjustButton(param) {\n    let { machine, onAdjust } = param;\n    if (!(0,_src_utils_machineOwnership__WEBPACK_IMPORTED_MODULE_9__.canAdjustTime)(machine)) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: onAdjust,\n        className: \"inline-flex items-center gap-1 px-2 py-1 text-xs font-medium text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-colors\",\n        children: \"⏱️ Adjust\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, this);\n}\n_c3 = TimeAdjustButton;\nfunction MachineStatus(param) {\n    let { machine } = param;\n    _s2();\n    const countdown = (0,_src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(machine.endAt, machine.graceEndAt);\n    if (machine.status === \"free\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-green-100 text-green-800 border-2 border-green-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"w-2 h-2 bg-green-500 rounded-full mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 11\n                    }, this),\n                    \"Available\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                lineNumber: 94,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n            lineNumber: 93,\n            columnNumber: 7\n        }, this);\n    }\n    if (machine.status === \"running\" && machine.endAt) {\n        const hours = Math.floor(countdown.secondsLeft / 3600);\n        const minutes = Math.floor(countdown.secondsLeft % 3600 / 60);\n        const seconds = countdown.secondsLeft % 60;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-blue-100 text-blue-800 border-2 border-blue-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"w-2 h-2 bg-blue-500 rounded-full mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, this),\n                    hours,\n                    \":\",\n                    minutes.toString().padStart(2, \"0\"),\n                    \":\",\n                    seconds.toString().padStart(2, \"0\"),\n                    \" left\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                lineNumber: 109,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n            lineNumber: 108,\n            columnNumber: 7\n        }, this);\n    }\n    if (machine.status === \"finishedGrace\") {\n        let graceTimeDisplay = \"5:00\";\n        if (machine.graceEndAt) {\n            const minutes = Math.floor(countdown.graceSecondsLeft / 60);\n            const seconds = countdown.graceSecondsLeft % 60;\n            graceTimeDisplay = \"\".concat(minutes, \":\").concat(seconds.toString().padStart(2, \"0\"));\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-orange-100 text-orange-800 border-2 border-orange-200 animate-pulse-slow\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"w-2 h-2 bg-orange-500 rounded-full mr-2 animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, this),\n                    \"Collect items - \",\n                    graceTimeDisplay\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                lineNumber: 128,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n            lineNumber: 127,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-gray-100 text-gray-800 border-2 border-gray-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"w-2 h-2 bg-gray-500 rounded-full mr-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this),\n                \"Unknown\"\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n            lineNumber: 138,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n        lineNumber: 137,\n        columnNumber: 5\n    }, this);\n}\n_s2(MachineStatus, \"u8Q9UI2BbjjlXAW3yY+wFIi4lfA=\", false, function() {\n    return [\n        _src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    ];\n});\n_c4 = MachineStatus;\nfunction LaundryCard() {\n    _s3();\n    const { laundry, incidents, deleteIncident, adjustMachineTime } = (0,_src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const [adjustModalOpen, setAdjustModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedMachine, setSelectedMachine] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Count total incidents for the badge\n    const totalIncidents = incidents.length;\n    // Handle time adjustment\n    const handleAdjustTime = (machine)=>{\n        setSelectedMachine(machine);\n        setAdjustModalOpen(true);\n    };\n    const handleCloseModal = ()=>{\n        setAdjustModalOpen(false);\n        setSelectedMachine(null);\n    };\n    // Custom display names for machines\n    const getDisplayName = (machine)=>{\n        const isWasher = machine.name.toLowerCase().includes(\"washer\");\n        if (isWasher) {\n            // Extract number from washer name (e.g., \"Washer 1\" -> \"1\")\n            const numberMatch = machine.name.match(/\\d+/);\n            const number = numberMatch ? numberMatch[0] : \"\";\n            return \"Washer \".concat(number);\n        } else {\n            // For dryers, use \"Dryer 5\" through \"Dryer 8\" based on original number\n            const numberMatch = machine.name.match(/\\d+/);\n            const originalNumber = numberMatch ? Number.parseInt(numberMatch[0]) : 0;\n            const newNumber = originalNumber + 4 // Map 1->5, 2->6, 3->7, 4->8\n            ;\n            return \"Dryer \".concat(newNumber);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_CardWrapper__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        id: \"laundry-machines\",\n        color: \"white\",\n        className: \"border-l-4 border-accent shadow-lg\",\n        count: totalIncidents,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-4 h-4 bg-accent rounded-full mr-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-primary\",\n                                children: \"\\uD83D\\uDC55 Laundry Machines\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 px-4 py-2 rounded-xl shadow-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 bg-green-500 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-bold text-green-800\",\n                                            children: laundry.filter((m)=>m.status === \"free\").length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"of\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-semibold text-gray-800\",\n                                            children: laundry.length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-green-700\",\n                                            children: \"available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-xs\",\n                                children: [\n                                    laundry.filter((m)=>m.status === \"running\").length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1 bg-blue-50 px-2 py-1 rounded-lg border border-blue-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-blue-700\",\n                                                children: [\n                                                    laundry.filter((m)=>m.status === \"running\").length,\n                                                    \" in use\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 15\n                                    }, this),\n                                    laundry.filter((m)=>m.status === \"finishedGrace\").length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1 bg-orange-50 px-2 py-1 rounded-lg border border-orange-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-orange-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-orange-700\",\n                                                children: [\n                                                    laundry.filter((m)=>m.status === \"finishedGrace\").length,\n                                                    \" ready\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\",\n                children: laundry.map((machine)=>{\n                    var _machine_graceEndAt, _machine_endAt;\n                    const machineIncidents = incidents.filter((inc)=>inc.machineId === machine.id);\n                    const isWasher = machine.name.toLowerCase().includes(\"washer\");\n                    const displayName = getDisplayName(machine);\n                    const recentUpdateClasses = (0,_src_utils_timeUtils__WEBPACK_IMPORTED_MODULE_8__.getRecentUpdateClasses)(machine.updatedAt);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border-2 border-gray-100 rounded-xl p-6 shadow-sm hover:shadow-lg transition-all duration-300 hover:border-accent/30 relative group \".concat(recentUpdateClasses),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(JustUpdatedBadge, {\n                                machine: machine\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 15\n                            }, this),\n                            machineIncidents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-3 -right-3 z-10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_IncidentBadge__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    incidents: machineIncidents,\n                                    onDelete: deleteIncident\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 bg-bgLight rounded-xl group-hover:bg-accent/10 transition-colors duration-300\",\n                                    children: isWasher ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_WasherSVG__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-20 h-20\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 31\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_DryerOutlineSVG__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-20 h-20\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 69\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-bold text-primary text-xl mb-1\",\n                                        children: displayName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TimeAgoDisplay, {\n                                        machine: machine\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OwnershipBadge, {\n                                            machine: machine\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MachineStatus, {\n                                        machine: machine\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 17\n                                    }, this),\n                                    machine.status === \"finishedGrace\" && machine.graceEndAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-orange-600 text-center mt-2 bg-orange-50 py-2 px-3 rounded-lg\",\n                                        children: [\n                                            \"Grace ends: \",\n                                            (_machine_graceEndAt = machine.graceEndAt) === null || _machine_graceEndAt === void 0 ? void 0 : _machine_graceEndAt.toLocaleTimeString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 19\n                                    }, this),\n                                    machine.status === \"running\" && machine.endAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-blue-600 text-center mt-2 bg-blue-50 py-2 px-3 rounded-lg\",\n                                        children: [\n                                            \"Cycle ends: \",\n                                            (_machine_endAt = machine.endAt) === null || _machine_endAt === void 0 ? void 0 : _machine_endAt.toLocaleTimeString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mt-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TimeAdjustButton, {\n                                            machine: machine,\n                                            onAdjust: ()=>handleAdjustTime(machine)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, machine.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                lineNumber: 232,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 pt-6 border-t border-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap justify-center gap-4 text-sm text-gray-600\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Available\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"In Use\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"w-2 h-2 bg-orange-500 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Please Collect\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                    lineNumber: 305,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                lineNumber: 304,\n                columnNumber: 7\n            }, this),\n            selectedMachine && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TimeAdjustmentModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                machine: selectedMachine,\n                isOpen: adjustModalOpen,\n                onClose: handleCloseModal,\n                onAdjust: adjustMachineTime\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                lineNumber: 323,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n        lineNumber: 186,\n        columnNumber: 5\n    }, this);\n}\n_s3(LaundryCard, \"UUtJ1yuarJ02cO9HpKvLUv8baYU=\", false, function() {\n    return [\n        _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n    ];\n});\n_c5 = LaundryCard;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"JustUpdatedBadge\");\n$RefreshReg$(_c1, \"TimeAgoDisplay\");\n$RefreshReg$(_c2, \"OwnershipBadge\");\n$RefreshReg$(_c3, \"TimeAdjustButton\");\n$RefreshReg$(_c4, \"MachineStatus\");\n$RefreshReg$(_c5, \"LaundryCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/LaundryCard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/TimeAdjustmentModal.tsx":
/*!************************************************!*\
  !*** ./src/components/TimeAdjustmentModal.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TimeAdjustmentModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction TimeAdjustmentModal(param) {\n    let { machine, isOpen, onClose, onAdjust } = param;\n    _s();\n    const [minutes, setMinutes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    if (!isOpen) return null;\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        const minutesNum = parseInt(minutes);\n        if (isNaN(minutesNum) || minutesNum < 1 || minutesNum > 120) {\n            setMessage(\"Please enter a number between 1-120 minutes\");\n            return;\n        }\n        setIsProcessing(true);\n        setMessage(\"\");\n        const result = await onAdjust(machine.id, minutesNum);\n        if (result.success) {\n            setMessage(\"Timer updated successfully\");\n            setTimeout(()=>{\n                onClose();\n                setMessage(\"\");\n                setMinutes(\"\");\n            }, 1500);\n        } else {\n            setMessage(result.error || \"Failed to update timer\");\n        }\n        setIsProcessing(false);\n    };\n    const handleClose = ()=>{\n        if (!isProcessing) {\n            onClose();\n            setMessage(\"\");\n            setMinutes(\"\");\n        }\n    };\n    // Calculate current remaining time\n    const currentMinutesLeft = machine.endAt ? Math.max(0, Math.ceil((machine.endAt.getTime() - Date.now()) / (1000 * 60))) : 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl max-w-md w-full p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: \"Adjust Timer\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\TimeAdjustmentModal.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleClose,\n                            disabled: isProcessing,\n                            className: \"text-gray-400 hover:text-gray-600 text-xl font-bold\",\n                            children: \"\\xd7\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\TimeAdjustmentModal.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\TimeAdjustmentModal.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600 mb-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: machine.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\TimeAdjustmentModal.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\TimeAdjustmentModal.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: [\n                                \"Current: \",\n                                currentMinutesLeft,\n                                \" minutes remaining\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\TimeAdjustmentModal.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\TimeAdjustmentModal.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"minutes\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Minutes remaining:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\TimeAdjustmentModal.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"number\",\n                                    id: \"minutes\",\n                                    min: \"1\",\n                                    max: \"120\",\n                                    value: minutes,\n                                    onChange: (e)=>setMinutes(e.target.value),\n                                    placeholder: \"Enter minutes (1-120)\",\n                                    disabled: isProcessing,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                    autoFocus: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\TimeAdjustmentModal.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-1\",\n                                    children: \"Enter a number between 1 and 120 minutes\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\TimeAdjustmentModal.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\TimeAdjustmentModal.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this),\n                        message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4 p-3 rounded-md text-sm \".concat(message.includes(\"successfully\") ? \"bg-green-100 text-green-800 border border-green-200\" : \"bg-red-100 text-red-800 border border-red-200\"),\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\TimeAdjustmentModal.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isProcessing || !minutes,\n                                    className: \"flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2\",\n                                    children: [\n                                        isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\TimeAdjustmentModal.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 17\n                                        }, this),\n                                        isProcessing ? \"Updating...\" : \"Update Timer\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\TimeAdjustmentModal.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleClose,\n                                    disabled: isProcessing,\n                                    className: \"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\TimeAdjustmentModal.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\TimeAdjustmentModal.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\TimeAdjustmentModal.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\TimeAdjustmentModal.tsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\TimeAdjustmentModal.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n_s(TimeAdjustmentModal, \"2W+Uu997SJsR6REtg9dpW6XHox0=\");\n_c = TimeAdjustmentModal;\nvar _c;\n$RefreshReg$(_c, \"TimeAdjustmentModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TimeAdjustmentModal.tsx\n"));

/***/ })

});