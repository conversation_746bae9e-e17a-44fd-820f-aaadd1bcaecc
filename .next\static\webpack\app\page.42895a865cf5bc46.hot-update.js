"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/LaundryCard.tsx":
/*!****************************************!*\
  !*** ./src/components/LaundryCard.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LaundryCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/src/hooks/useSupabaseData */ \"(app-pages-browser)/./src/hooks/useSupabaseData.tsx\");\n/* harmony import */ var _src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/hooks/useCountdown */ \"(app-pages-browser)/./src/hooks/useCountdown.tsx\");\n/* harmony import */ var _IncidentBadge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./IncidentBadge */ \"(app-pages-browser)/./src/components/IncidentBadge.tsx\");\n/* harmony import */ var _ui_CardWrapper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/CardWrapper */ \"(app-pages-browser)/./src/components/ui/CardWrapper.tsx\");\n/* harmony import */ var _ui_WasherSVG__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/WasherSVG */ \"(app-pages-browser)/./src/components/ui/WasherSVG.tsx\");\n/* harmony import */ var _ui_DryerOutlineSVG__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ui/DryerOutlineSVG */ \"(app-pages-browser)/./src/components/ui/DryerOutlineSVG.tsx\");\n/* harmony import */ var _src_utils_timeUtils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/src/utils/timeUtils */ \"(app-pages-browser)/./src/utils/timeUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Component for \"Just Updated\" badge\nfunction JustUpdatedBadge(param) {\n    let { machine } = param;\n    _s();\n    const [showBadge, setShowBadge] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"JustUpdatedBadge.useEffect\": ()=>{\n            if ((0,_src_utils_timeUtils__WEBPACK_IMPORTED_MODULE_8__.isRecentlyUpdated)(machine.updatedAt)) {\n                setShowBadge(true);\n                // Hide badge after 10 seconds\n                const timer = setTimeout({\n                    \"JustUpdatedBadge.useEffect.timer\": ()=>setShowBadge(false)\n                }[\"JustUpdatedBadge.useEffect.timer\"], 10000);\n                return ({\n                    \"JustUpdatedBadge.useEffect\": ()=>clearTimeout(timer)\n                })[\"JustUpdatedBadge.useEffect\"];\n            }\n        }\n    }[\"JustUpdatedBadge.useEffect\"], [\n        machine.updatedAt\n    ]);\n    if (!showBadge) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"absolute -top-2 -left-2 z-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-blue-500 text-white text-xs px-2 py-1 rounded-full shadow-lg animate-pulse-slow\",\n            children: \"Just Updated\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n_s(JustUpdatedBadge, \"mDNabOB/uIKS4gwTk58LpTtHb0g=\");\n_c = JustUpdatedBadge;\n// Component for time ago display\nfunction TimeAgoDisplay(param) {\n    let { machine } = param;\n    _s1();\n    const [timeAgo, setTimeAgo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TimeAgoDisplay.useEffect\": ()=>{\n            const updateTimeAgo = {\n                \"TimeAgoDisplay.useEffect.updateTimeAgo\": ()=>{\n                    setTimeAgo((0,_src_utils_timeUtils__WEBPACK_IMPORTED_MODULE_8__.formatTimeAgo)(machine.updatedAt));\n                }\n            }[\"TimeAgoDisplay.useEffect.updateTimeAgo\"];\n            updateTimeAgo();\n            // Update every 30 seconds\n            const interval = setInterval(updateTimeAgo, 30000);\n            return ({\n                \"TimeAgoDisplay.useEffect\": ()=>clearInterval(interval)\n            })[\"TimeAgoDisplay.useEffect\"];\n        }\n    }[\"TimeAgoDisplay.useEffect\"], [\n        machine.updatedAt\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-xs text-gray-500 mt-1 text-center\",\n        children: timeAgo\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n_s1(TimeAgoDisplay, \"IBf0cw+KUIsg95VJGn4acX1ZO9o=\");\n_c1 = TimeAgoDisplay;\nfunction MachineStatus(param) {\n    let { machine } = param;\n    _s2();\n    const countdown = (0,_src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(machine.endAt, machine.graceEndAt);\n    if (machine.status === \"free\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-green-100 text-green-800 border-2 border-green-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"w-2 h-2 bg-green-500 rounded-full mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, this),\n                    \"Available\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, this);\n    }\n    if (machine.status === \"running\" && machine.endAt) {\n        const hours = Math.floor(countdown.secondsLeft / 3600);\n        const minutes = Math.floor(countdown.secondsLeft % 3600 / 60);\n        const seconds = countdown.secondsLeft % 60;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-blue-100 text-blue-800 border-2 border-blue-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"w-2 h-2 bg-blue-500 rounded-full mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, this),\n                    hours,\n                    \":\",\n                    minutes.toString().padStart(2, \"0\"),\n                    \":\",\n                    seconds.toString().padStart(2, \"0\"),\n                    \" left\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                lineNumber: 81,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n            lineNumber: 80,\n            columnNumber: 7\n        }, this);\n    }\n    if (machine.status === \"finishedGrace\") {\n        let graceTimeDisplay = \"5:00\";\n        if (machine.graceEndAt) {\n            const minutes = Math.floor(countdown.graceSecondsLeft / 60);\n            const seconds = countdown.graceSecondsLeft % 60;\n            graceTimeDisplay = \"\".concat(minutes, \":\").concat(seconds.toString().padStart(2, \"0\"));\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-orange-100 text-orange-800 border-2 border-orange-200 animate-pulse-slow\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"w-2 h-2 bg-orange-500 rounded-full mr-2 animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, this),\n                    \"Collect items - \",\n                    graceTimeDisplay\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                lineNumber: 100,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-gray-100 text-gray-800 border-2 border-gray-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"w-2 h-2 bg-gray-500 rounded-full mr-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this),\n                \"Unknown\"\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n            lineNumber: 110,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n        lineNumber: 109,\n        columnNumber: 5\n    }, this);\n}\n_s2(MachineStatus, \"u8Q9UI2BbjjlXAW3yY+wFIi4lfA=\", false, function() {\n    return [\n        _src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    ];\n});\n_c2 = MachineStatus;\nfunction LaundryCard() {\n    _s3();\n    const { laundry, incidents, deleteIncident } = (0,_src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    // Count total incidents for the badge\n    const totalIncidents = incidents.length;\n    // Custom display names for machines\n    const getDisplayName = (machine)=>{\n        const isWasher = machine.name.toLowerCase().includes(\"washer\");\n        if (isWasher) {\n            // Extract number from washer name (e.g., \"Washer 1\" -> \"1\")\n            const numberMatch = machine.name.match(/\\d+/);\n            const number = numberMatch ? numberMatch[0] : \"\";\n            return \"Washer \".concat(number);\n        } else {\n            // For dryers, use \"Dryer 5\" through \"Dryer 8\" based on original number\n            const numberMatch = machine.name.match(/\\d+/);\n            const originalNumber = numberMatch ? Number.parseInt(numberMatch[0]) : 0;\n            const newNumber = originalNumber + 4 // Map 1->5, 2->6, 3->7, 4->8\n            ;\n            return \"Dryer \".concat(newNumber);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_CardWrapper__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        id: \"laundry-machines\",\n        color: \"white\",\n        className: \"border-l-4 border-accent shadow-lg\",\n        count: totalIncidents,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-4 h-4 bg-accent rounded-full mr-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-primary\",\n                                children: \"\\uD83D\\uDC55 Laundry Machines\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 px-4 py-2 rounded-xl shadow-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 bg-green-500 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-bold text-green-800\",\n                                            children: laundry.filter((m)=>m.status === \"free\").length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"of\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-semibold text-gray-800\",\n                                            children: laundry.length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-green-700\",\n                                            children: \"available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-xs\",\n                                children: [\n                                    laundry.filter((m)=>m.status === \"running\").length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1 bg-blue-50 px-2 py-1 rounded-lg border border-blue-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-blue-700\",\n                                                children: [\n                                                    laundry.filter((m)=>m.status === \"running\").length,\n                                                    \" in use\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this),\n                                    laundry.filter((m)=>m.status === \"finishedGrace\").length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1 bg-orange-50 px-2 py-1 rounded-lg border border-orange-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-orange-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-orange-700\",\n                                                children: [\n                                                    laundry.filter((m)=>m.status === \"finishedGrace\").length,\n                                                    \" ready\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\",\n                children: laundry.map((machine)=>{\n                    var _machine_graceEndAt, _machine_endAt;\n                    const machineIncidents = incidents.filter((inc)=>inc.machineId === machine.id);\n                    const isWasher = machine.name.toLowerCase().includes(\"washer\");\n                    const displayName = getDisplayName(machine);\n                    const recentUpdateClasses = (0,_src_utils_timeUtils__WEBPACK_IMPORTED_MODULE_8__.getRecentUpdateClasses)(machine.updatedAt);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border-2 border-gray-100 rounded-xl p-6 shadow-sm hover:shadow-lg transition-all duration-300 hover:border-accent/30 relative group \".concat(recentUpdateClasses),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(JustUpdatedBadge, {\n                                machine: machine\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 15\n                            }, this),\n                            machineIncidents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-3 -right-3 z-10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_IncidentBadge__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    incidents: machineIncidents,\n                                    onDelete: deleteIncident\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 bg-bgLight rounded-xl group-hover:bg-accent/10 transition-colors duration-300\",\n                                    children: isWasher ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_WasherSVG__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-20 h-20\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 31\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_DryerOutlineSVG__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-20 h-20\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 69\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-bold text-primary text-xl mb-1\",\n                                        children: displayName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TimeAgoDisplay, {\n                                        machine: machine\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MachineStatus, {\n                                        machine: machine\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 17\n                                    }, this),\n                                    machine.status === \"finishedGrace\" && machine.graceEndAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-orange-600 text-center mt-2 bg-orange-50 py-2 px-3 rounded-lg\",\n                                        children: [\n                                            \"Grace ends: \",\n                                            (_machine_graceEndAt = machine.graceEndAt) === null || _machine_graceEndAt === void 0 ? void 0 : _machine_graceEndAt.toLocaleTimeString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 19\n                                    }, this),\n                                    machine.status === \"running\" && machine.endAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-blue-600 text-center mt-2 bg-blue-50 py-2 px-3 rounded-lg\",\n                                        children: [\n                                            \"Cycle ends: \",\n                                            (_machine_endAt = machine.endAt) === null || _machine_endAt === void 0 ? void 0 : _machine_endAt.toLocaleTimeString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, machine.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 pt-6 border-t border-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap justify-center gap-4 text-sm text-gray-600\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Available\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"In Use\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"w-2 h-2 bg-orange-500 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Please Collect\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                lineNumber: 250,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n        lineNumber: 145,\n        columnNumber: 5\n    }, this);\n}\n_s3(LaundryCard, \"LxkqdWGvpeonC73MNgMFUYmDkJo=\", false, function() {\n    return [\n        _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n    ];\n});\n_c3 = LaundryCard;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"JustUpdatedBadge\");\n$RefreshReg$(_c1, \"TimeAgoDisplay\");\n$RefreshReg$(_c2, \"MachineStatus\");\n$RefreshReg$(_c3, \"LaundryCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/LaundryCard.tsx\n"));

/***/ })

});