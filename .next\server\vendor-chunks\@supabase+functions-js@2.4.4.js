"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@supabase+functions-js@2.4.4";
exports.ids = ["vendor-chunks/@supabase+functions-js@2.4.4"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@supabase+functions-js@2.4.4/node_modules/@supabase/functions-js/dist/module/FunctionsClient.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+functions-js@2.4.4/node_modules/@supabase/functions-js/dist/module/FunctionsClient.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FunctionsClient: () => (/* binding */ FunctionsClient)\n/* harmony export */ });\n/* harmony import */ var _helper__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./helper */ \"(ssr)/./node_modules/.pnpm/@supabase+functions-js@2.4.4/node_modules/@supabase/functions-js/dist/module/helper.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/.pnpm/@supabase+functions-js@2.4.4/node_modules/@supabase/functions-js/dist/module/types.js\");\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\n\nclass FunctionsClient {\n    constructor(url, { headers = {}, customFetch, region = _types__WEBPACK_IMPORTED_MODULE_0__.FunctionRegion.Any, } = {}) {\n        this.url = url;\n        this.headers = headers;\n        this.region = region;\n        this.fetch = (0,_helper__WEBPACK_IMPORTED_MODULE_1__.resolveFetch)(customFetch);\n    }\n    /**\n     * Updates the authorization header\n     * @param token - the new jwt token sent in the authorisation header\n     */\n    setAuth(token) {\n        this.headers.Authorization = `Bearer ${token}`;\n    }\n    /**\n     * Invokes a function\n     * @param functionName - The name of the Function to invoke.\n     * @param options - Options for invoking the Function.\n     */\n    invoke(functionName, options = {}) {\n        var _a;\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const { headers, method, body: functionArgs } = options;\n                let _headers = {};\n                let { region } = options;\n                if (!region) {\n                    region = this.region;\n                }\n                if (region && region !== 'any') {\n                    _headers['x-region'] = region;\n                }\n                let body;\n                if (functionArgs &&\n                    ((headers && !Object.prototype.hasOwnProperty.call(headers, 'Content-Type')) || !headers)) {\n                    if ((typeof Blob !== 'undefined' && functionArgs instanceof Blob) ||\n                        functionArgs instanceof ArrayBuffer) {\n                        // will work for File as File inherits Blob\n                        // also works for ArrayBuffer as it is the same underlying structure as a Blob\n                        _headers['Content-Type'] = 'application/octet-stream';\n                        body = functionArgs;\n                    }\n                    else if (typeof functionArgs === 'string') {\n                        // plain string\n                        _headers['Content-Type'] = 'text/plain';\n                        body = functionArgs;\n                    }\n                    else if (typeof FormData !== 'undefined' && functionArgs instanceof FormData) {\n                        // don't set content-type headers\n                        // Request will automatically add the right boundary value\n                        body = functionArgs;\n                    }\n                    else {\n                        // default, assume this is JSON\n                        _headers['Content-Type'] = 'application/json';\n                        body = JSON.stringify(functionArgs);\n                    }\n                }\n                const response = yield this.fetch(`${this.url}/${functionName}`, {\n                    method: method || 'POST',\n                    // headers priority is (high to low):\n                    // 1. invoke-level headers\n                    // 2. client-level headers\n                    // 3. default Content-Type header\n                    headers: Object.assign(Object.assign(Object.assign({}, _headers), this.headers), headers),\n                    body,\n                }).catch((fetchError) => {\n                    throw new _types__WEBPACK_IMPORTED_MODULE_0__.FunctionsFetchError(fetchError);\n                });\n                const isRelayError = response.headers.get('x-relay-error');\n                if (isRelayError && isRelayError === 'true') {\n                    throw new _types__WEBPACK_IMPORTED_MODULE_0__.FunctionsRelayError(response);\n                }\n                if (!response.ok) {\n                    throw new _types__WEBPACK_IMPORTED_MODULE_0__.FunctionsHttpError(response);\n                }\n                let responseType = ((_a = response.headers.get('Content-Type')) !== null && _a !== void 0 ? _a : 'text/plain').split(';')[0].trim();\n                let data;\n                if (responseType === 'application/json') {\n                    data = yield response.json();\n                }\n                else if (responseType === 'application/octet-stream') {\n                    data = yield response.blob();\n                }\n                else if (responseType === 'text/event-stream') {\n                    data = response;\n                }\n                else if (responseType === 'multipart/form-data') {\n                    data = yield response.formData();\n                }\n                else {\n                    // default to text\n                    data = yield response.text();\n                }\n                return { data, error: null };\n            }\n            catch (error) {\n                return { data: null, error };\n            }\n        });\n    }\n}\n//# sourceMappingURL=FunctionsClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+functions-js@2.4.4/node_modules/@supabase/functions-js/dist/module/FunctionsClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+functions-js@2.4.4/node_modules/@supabase/functions-js/dist/module/helper.js":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+functions-js@2.4.4/node_modules/@supabase/functions-js/dist/module/helper.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveFetch: () => (/* binding */ resolveFetch)\n/* harmony export */ });\nconst resolveFetch = (customFetch) => {\n    let _fetch;\n    if (customFetch) {\n        _fetch = customFetch;\n    }\n    else if (typeof fetch === 'undefined') {\n        _fetch = (...args) => Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! @supabase/node-fetch */ \"(ssr)/./node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/lib/index.js\", 23)).then(({ default: fetch }) => fetch(...args));\n    }\n    else {\n        _fetch = fetch;\n    }\n    return (...args) => _fetch(...args);\n};\n//# sourceMappingURL=helper.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK2Z1bmN0aW9ucy1qc0AyLjQuNC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL2Z1bmN0aW9ucy1qcy9kaXN0L21vZHVsZS9oZWxwZXIuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QiwrTkFBOEIsU0FBUyxnQkFBZ0I7QUFDckY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbWVtZXRcXERlc2t0b3BcXGRvcm1fMjFcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZStmdW5jdGlvbnMtanNAMi40LjRcXG5vZGVfbW9kdWxlc1xcQHN1cGFiYXNlXFxmdW5jdGlvbnMtanNcXGRpc3RcXG1vZHVsZVxcaGVscGVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCByZXNvbHZlRmV0Y2ggPSAoY3VzdG9tRmV0Y2gpID0+IHtcbiAgICBsZXQgX2ZldGNoO1xuICAgIGlmIChjdXN0b21GZXRjaCkge1xuICAgICAgICBfZmV0Y2ggPSBjdXN0b21GZXRjaDtcbiAgICB9XG4gICAgZWxzZSBpZiAodHlwZW9mIGZldGNoID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICBfZmV0Y2ggPSAoLi4uYXJncykgPT4gaW1wb3J0KCdAc3VwYWJhc2Uvbm9kZS1mZXRjaCcpLnRoZW4oKHsgZGVmYXVsdDogZmV0Y2ggfSkgPT4gZmV0Y2goLi4uYXJncykpO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgX2ZldGNoID0gZmV0Y2g7XG4gICAgfVxuICAgIHJldHVybiAoLi4uYXJncykgPT4gX2ZldGNoKC4uLmFyZ3MpO1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWhlbHBlci5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+functions-js@2.4.4/node_modules/@supabase/functions-js/dist/module/helper.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+functions-js@2.4.4/node_modules/@supabase/functions-js/dist/module/types.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+functions-js@2.4.4/node_modules/@supabase/functions-js/dist/module/types.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FunctionRegion: () => (/* binding */ FunctionRegion),\n/* harmony export */   FunctionsError: () => (/* binding */ FunctionsError),\n/* harmony export */   FunctionsFetchError: () => (/* binding */ FunctionsFetchError),\n/* harmony export */   FunctionsHttpError: () => (/* binding */ FunctionsHttpError),\n/* harmony export */   FunctionsRelayError: () => (/* binding */ FunctionsRelayError)\n/* harmony export */ });\nclass FunctionsError extends Error {\n    constructor(message, name = 'FunctionsError', context) {\n        super(message);\n        this.name = name;\n        this.context = context;\n    }\n}\nclass FunctionsFetchError extends FunctionsError {\n    constructor(context) {\n        super('Failed to send a request to the Edge Function', 'FunctionsFetchError', context);\n    }\n}\nclass FunctionsRelayError extends FunctionsError {\n    constructor(context) {\n        super('Relay Error invoking the Edge Function', 'FunctionsRelayError', context);\n    }\n}\nclass FunctionsHttpError extends FunctionsError {\n    constructor(context) {\n        super('Edge Function returned a non-2xx status code', 'FunctionsHttpError', context);\n    }\n}\n// Define the enum for the 'region' property\nvar FunctionRegion;\n(function (FunctionRegion) {\n    FunctionRegion[\"Any\"] = \"any\";\n    FunctionRegion[\"ApNortheast1\"] = \"ap-northeast-1\";\n    FunctionRegion[\"ApNortheast2\"] = \"ap-northeast-2\";\n    FunctionRegion[\"ApSouth1\"] = \"ap-south-1\";\n    FunctionRegion[\"ApSoutheast1\"] = \"ap-southeast-1\";\n    FunctionRegion[\"ApSoutheast2\"] = \"ap-southeast-2\";\n    FunctionRegion[\"CaCentral1\"] = \"ca-central-1\";\n    FunctionRegion[\"EuCentral1\"] = \"eu-central-1\";\n    FunctionRegion[\"EuWest1\"] = \"eu-west-1\";\n    FunctionRegion[\"EuWest2\"] = \"eu-west-2\";\n    FunctionRegion[\"EuWest3\"] = \"eu-west-3\";\n    FunctionRegion[\"SaEast1\"] = \"sa-east-1\";\n    FunctionRegion[\"UsEast1\"] = \"us-east-1\";\n    FunctionRegion[\"UsWest1\"] = \"us-west-1\";\n    FunctionRegion[\"UsWest2\"] = \"us-west-2\";\n})(FunctionRegion || (FunctionRegion = {}));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+functions-js@2.4.4/node_modules/@supabase/functions-js/dist/module/types.js\n");

/***/ })

};
;