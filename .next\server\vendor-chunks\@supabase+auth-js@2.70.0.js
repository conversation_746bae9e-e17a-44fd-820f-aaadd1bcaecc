"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@supabase+auth-js@2.70.0";
exports.ids = ["vendor-chunks/@supabase+auth-js@2.70.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/AuthAdminApi.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/AuthAdminApi.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _GoTrueAdminApi__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./GoTrueAdminApi */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js\");\n\nconst AuthAdminApi = _GoTrueAdminApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"];\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuthAdminApi);\n//# sourceMappingURL=AuthAdminApi.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK2F1dGgtanNAMi43MC4wL25vZGVfbW9kdWxlcy9Ac3VwYWJhc2UvYXV0aC1qcy9kaXN0L21vZHVsZS9BdXRoQWRtaW5BcGkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBOEM7QUFDOUMscUJBQXFCLHVEQUFjO0FBQ25DLGlFQUFlLFlBQVksRUFBQztBQUM1QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxtZW1ldFxcRGVza3RvcFxcZG9ybV8yMVxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHN1cGFiYXNlK2F1dGgtanNAMi43MC4wXFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxcYXV0aC1qc1xcZGlzdFxcbW9kdWxlXFxBdXRoQWRtaW5BcGkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IEdvVHJ1ZUFkbWluQXBpIGZyb20gJy4vR29UcnVlQWRtaW5BcGknO1xuY29uc3QgQXV0aEFkbWluQXBpID0gR29UcnVlQWRtaW5BcGk7XG5leHBvcnQgZGVmYXVsdCBBdXRoQWRtaW5BcGk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1BdXRoQWRtaW5BcGkuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/AuthAdminApi.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/AuthClient.js":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/AuthClient.js ***!
  \**************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _GoTrueClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./GoTrueClient */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/GoTrueClient.js\");\n\nconst AuthClient = _GoTrueClient__WEBPACK_IMPORTED_MODULE_0__[\"default\"];\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuthClient);\n//# sourceMappingURL=AuthClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK2F1dGgtanNAMi43MC4wL25vZGVfbW9kdWxlcy9Ac3VwYWJhc2UvYXV0aC1qcy9kaXN0L21vZHVsZS9BdXRoQ2xpZW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTBDO0FBQzFDLG1CQUFtQixxREFBWTtBQUMvQixpRUFBZSxVQUFVLEVBQUM7QUFDMUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbWVtZXRcXERlc2t0b3BcXGRvcm1fMjFcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZSthdXRoLWpzQDIuNzAuMFxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXGF1dGgtanNcXGRpc3RcXG1vZHVsZVxcQXV0aENsaWVudC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgR29UcnVlQ2xpZW50IGZyb20gJy4vR29UcnVlQ2xpZW50JztcbmNvbnN0IEF1dGhDbGllbnQgPSBHb1RydWVDbGllbnQ7XG5leHBvcnQgZGVmYXVsdCBBdXRoQ2xpZW50O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9QXV0aENsaWVudC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/AuthClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GoTrueAdminApi)\n/* harmony export */ });\n/* harmony import */ var _lib_fetch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/fetch */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/fetch.js\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/helpers */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/helpers.js\");\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/types */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/types.js\");\n/* harmony import */ var _lib_errors__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/errors */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/errors.js\");\nvar __rest = (undefined && undefined.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\n\n\n\n\nclass GoTrueAdminApi {\n    constructor({ url = '', headers = {}, fetch, }) {\n        this.url = url;\n        this.headers = headers;\n        this.fetch = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_1__.resolveFetch)(fetch);\n        this.mfa = {\n            listFactors: this._listFactors.bind(this),\n            deleteFactor: this._deleteFactor.bind(this),\n        };\n    }\n    /**\n     * Removes a logged-in session.\n     * @param jwt A valid, logged-in JWT.\n     * @param scope The logout sope.\n     */\n    async signOut(jwt, scope = _lib_types__WEBPACK_IMPORTED_MODULE_2__.SIGN_OUT_SCOPES[0]) {\n        if (_lib_types__WEBPACK_IMPORTED_MODULE_2__.SIGN_OUT_SCOPES.indexOf(scope) < 0) {\n            throw new Error(`@supabase/auth-js: Parameter scope must be one of ${_lib_types__WEBPACK_IMPORTED_MODULE_2__.SIGN_OUT_SCOPES.join(', ')}`);\n        }\n        try {\n            await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'POST', `${this.url}/logout?scope=${scope}`, {\n                headers: this.headers,\n                jwt,\n                noResolveJson: true,\n            });\n            return { data: null, error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Sends an invite link to an email address.\n     * @param email The email address of the user.\n     * @param options Additional options to be included when inviting.\n     */\n    async inviteUserByEmail(email, options = {}) {\n        try {\n            return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'POST', `${this.url}/invite`, {\n                body: { email, data: options.data },\n                headers: this.headers,\n                redirectTo: options.redirectTo,\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_0__._userResponse,\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isAuthError)(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Generates email links and OTPs to be sent via a custom email provider.\n     * @param email The user's email.\n     * @param options.password User password. For signup only.\n     * @param options.data Optional user metadata. For signup only.\n     * @param options.redirectTo The redirect url which should be appended to the generated link\n     */\n    async generateLink(params) {\n        try {\n            const { options } = params, rest = __rest(params, [\"options\"]);\n            const body = Object.assign(Object.assign({}, rest), options);\n            if ('newEmail' in rest) {\n                // replace newEmail with new_email in request body\n                body.new_email = rest === null || rest === void 0 ? void 0 : rest.newEmail;\n                delete body['newEmail'];\n            }\n            return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'POST', `${this.url}/admin/generate_link`, {\n                body: body,\n                headers: this.headers,\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_0__._generateLinkResponse,\n                redirectTo: options === null || options === void 0 ? void 0 : options.redirectTo,\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isAuthError)(error)) {\n                return {\n                    data: {\n                        properties: null,\n                        user: null,\n                    },\n                    error,\n                };\n            }\n            throw error;\n        }\n    }\n    // User Admin API\n    /**\n     * Creates a new user.\n     * This function should only be called on a server. Never expose your `service_role` key in the browser.\n     */\n    async createUser(attributes) {\n        try {\n            return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'POST', `${this.url}/admin/users`, {\n                body: attributes,\n                headers: this.headers,\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_0__._userResponse,\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isAuthError)(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Get a list of users.\n     *\n     * This function should only be called on a server. Never expose your `service_role` key in the browser.\n     * @param params An object which supports `page` and `perPage` as numbers, to alter the paginated results.\n     */\n    async listUsers(params) {\n        var _a, _b, _c, _d, _e, _f, _g;\n        try {\n            const pagination = { nextPage: null, lastPage: 0, total: 0 };\n            const response = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'GET', `${this.url}/admin/users`, {\n                headers: this.headers,\n                noResolveJson: true,\n                query: {\n                    page: (_b = (_a = params === null || params === void 0 ? void 0 : params.page) === null || _a === void 0 ? void 0 : _a.toString()) !== null && _b !== void 0 ? _b : '',\n                    per_page: (_d = (_c = params === null || params === void 0 ? void 0 : params.perPage) === null || _c === void 0 ? void 0 : _c.toString()) !== null && _d !== void 0 ? _d : '',\n                },\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_0__._noResolveJsonResponse,\n            });\n            if (response.error)\n                throw response.error;\n            const users = await response.json();\n            const total = (_e = response.headers.get('x-total-count')) !== null && _e !== void 0 ? _e : 0;\n            const links = (_g = (_f = response.headers.get('link')) === null || _f === void 0 ? void 0 : _f.split(',')) !== null && _g !== void 0 ? _g : [];\n            if (links.length > 0) {\n                links.forEach((link) => {\n                    const page = parseInt(link.split(';')[0].split('=')[1].substring(0, 1));\n                    const rel = JSON.parse(link.split(';')[1].split('=')[1]);\n                    pagination[`${rel}Page`] = page;\n                });\n                pagination.total = parseInt(total);\n            }\n            return { data: Object.assign(Object.assign({}, users), pagination), error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isAuthError)(error)) {\n                return { data: { users: [] }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Get user by id.\n     *\n     * @param uid The user's unique identifier\n     *\n     * This function should only be called on a server. Never expose your `service_role` key in the browser.\n     */\n    async getUserById(uid) {\n        (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_1__.validateUUID)(uid);\n        try {\n            return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'GET', `${this.url}/admin/users/${uid}`, {\n                headers: this.headers,\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_0__._userResponse,\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isAuthError)(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Updates the user data.\n     *\n     * @param attributes The data you want to update.\n     *\n     * This function should only be called on a server. Never expose your `service_role` key in the browser.\n     */\n    async updateUserById(uid, attributes) {\n        (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_1__.validateUUID)(uid);\n        try {\n            return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'PUT', `${this.url}/admin/users/${uid}`, {\n                body: attributes,\n                headers: this.headers,\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_0__._userResponse,\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isAuthError)(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Delete a user. Requires a `service_role` key.\n     *\n     * @param id The user id you want to remove.\n     * @param shouldSoftDelete If true, then the user will be soft-deleted from the auth schema. Soft deletion allows user identification from the hashed user ID but is not reversible.\n     * Defaults to false for backward compatibility.\n     *\n     * This function should only be called on a server. Never expose your `service_role` key in the browser.\n     */\n    async deleteUser(id, shouldSoftDelete = false) {\n        (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_1__.validateUUID)(id);\n        try {\n            return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'DELETE', `${this.url}/admin/users/${id}`, {\n                headers: this.headers,\n                body: {\n                    should_soft_delete: shouldSoftDelete,\n                },\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_0__._userResponse,\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isAuthError)(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    async _listFactors(params) {\n        (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_1__.validateUUID)(params.userId);\n        try {\n            const { data, error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'GET', `${this.url}/admin/users/${params.userId}/factors`, {\n                headers: this.headers,\n                xform: (factors) => {\n                    return { data: { factors }, error: null };\n                },\n            });\n            return { data, error };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    async _deleteFactor(params) {\n        (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_1__.validateUUID)(params.userId);\n        (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_1__.validateUUID)(params.id);\n        try {\n            const data = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'DELETE', `${this.url}/admin/users/${params.userId}/factors/${params.id}`, {\n                headers: this.headers,\n            });\n            return { data, error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n}\n//# sourceMappingURL=GoTrueAdminApi.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/GoTrueClient.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/GoTrueClient.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GoTrueClient)\n/* harmony export */ });\n/* harmony import */ var _GoTrueAdminApi__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./GoTrueAdminApi */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/constants */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/constants.js\");\n/* harmony import */ var _lib_errors__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/errors */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/errors.js\");\n/* harmony import */ var _lib_fetch__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/fetch */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/fetch.js\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/helpers */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/helpers.js\");\n/* harmony import */ var _lib_local_storage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./lib/local-storage */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/local-storage.js\");\n/* harmony import */ var _lib_polyfills__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./lib/polyfills */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/polyfills.js\");\n/* harmony import */ var _lib_version__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./lib/version */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/version.js\");\n/* harmony import */ var _lib_locks__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./lib/locks */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/locks.js\");\n/* harmony import */ var _lib_base64url__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./lib/base64url */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/base64url.js\");\n\n\n\n\n\n\n\n\n\n\n(0,_lib_polyfills__WEBPACK_IMPORTED_MODULE_6__.polyfillGlobalThis)(); // Make \"globalThis\" available\nconst DEFAULT_OPTIONS = {\n    url: _lib_constants__WEBPACK_IMPORTED_MODULE_1__.GOTRUE_URL,\n    storageKey: _lib_constants__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEY,\n    autoRefreshToken: true,\n    persistSession: true,\n    detectSessionInUrl: true,\n    headers: _lib_constants__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_HEADERS,\n    flowType: 'implicit',\n    debug: false,\n    hasCustomAuthorizationHeader: false,\n};\nasync function lockNoOp(name, acquireTimeout, fn) {\n    return await fn();\n}\nclass GoTrueClient {\n    /**\n     * Create a new client for use in the browser.\n     */\n    constructor(options) {\n        var _a, _b;\n        this.memoryStorage = null;\n        this.stateChangeEmitters = new Map();\n        this.autoRefreshTicker = null;\n        this.visibilityChangedCallback = null;\n        this.refreshingDeferred = null;\n        /**\n         * Keeps track of the async client initialization.\n         * When null or not yet resolved the auth state is `unknown`\n         * Once resolved the the auth state is known and it's save to call any further client methods.\n         * Keep extra care to never reject or throw uncaught errors\n         */\n        this.initializePromise = null;\n        this.detectSessionInUrl = true;\n        this.hasCustomAuthorizationHeader = false;\n        this.suppressGetSessionWarning = false;\n        this.lockAcquired = false;\n        this.pendingInLock = [];\n        /**\n         * Used to broadcast state change events to other tabs listening.\n         */\n        this.broadcastChannel = null;\n        this.logger = console.log;\n        this.instanceID = GoTrueClient.nextInstanceID;\n        GoTrueClient.nextInstanceID += 1;\n        if (this.instanceID > 0 && (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)()) {\n            console.warn('Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.');\n        }\n        const settings = Object.assign(Object.assign({}, DEFAULT_OPTIONS), options);\n        this.logDebugMessages = !!settings.debug;\n        if (typeof settings.debug === 'function') {\n            this.logger = settings.debug;\n        }\n        this.persistSession = settings.persistSession;\n        this.storageKey = settings.storageKey;\n        this.autoRefreshToken = settings.autoRefreshToken;\n        this.admin = new _GoTrueAdminApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n            url: settings.url,\n            headers: settings.headers,\n            fetch: settings.fetch,\n        });\n        this.url = settings.url;\n        this.headers = settings.headers;\n        this.fetch = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.resolveFetch)(settings.fetch);\n        this.lock = settings.lock || lockNoOp;\n        this.detectSessionInUrl = settings.detectSessionInUrl;\n        this.flowType = settings.flowType;\n        this.hasCustomAuthorizationHeader = settings.hasCustomAuthorizationHeader;\n        if (settings.lock) {\n            this.lock = settings.lock;\n        }\n        else if ((0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)() && ((_a = globalThis === null || globalThis === void 0 ? void 0 : globalThis.navigator) === null || _a === void 0 ? void 0 : _a.locks)) {\n            this.lock = _lib_locks__WEBPACK_IMPORTED_MODULE_8__.navigatorLock;\n        }\n        else {\n            this.lock = lockNoOp;\n        }\n        this.jwks = { keys: [] };\n        this.jwks_cached_at = Number.MIN_SAFE_INTEGER;\n        this.mfa = {\n            verify: this._verify.bind(this),\n            enroll: this._enroll.bind(this),\n            unenroll: this._unenroll.bind(this),\n            challenge: this._challenge.bind(this),\n            listFactors: this._listFactors.bind(this),\n            challengeAndVerify: this._challengeAndVerify.bind(this),\n            getAuthenticatorAssuranceLevel: this._getAuthenticatorAssuranceLevel.bind(this),\n        };\n        if (this.persistSession) {\n            if (settings.storage) {\n                this.storage = settings.storage;\n            }\n            else {\n                if ((0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.supportsLocalStorage)()) {\n                    this.storage = _lib_local_storage__WEBPACK_IMPORTED_MODULE_5__.localStorageAdapter;\n                }\n                else {\n                    this.memoryStorage = {};\n                    this.storage = (0,_lib_local_storage__WEBPACK_IMPORTED_MODULE_5__.memoryLocalStorageAdapter)(this.memoryStorage);\n                }\n            }\n        }\n        else {\n            this.memoryStorage = {};\n            this.storage = (0,_lib_local_storage__WEBPACK_IMPORTED_MODULE_5__.memoryLocalStorageAdapter)(this.memoryStorage);\n        }\n        if ((0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)() && globalThis.BroadcastChannel && this.persistSession && this.storageKey) {\n            try {\n                this.broadcastChannel = new globalThis.BroadcastChannel(this.storageKey);\n            }\n            catch (e) {\n                console.error('Failed to create a new BroadcastChannel, multi-tab state changes will not be available', e);\n            }\n            (_b = this.broadcastChannel) === null || _b === void 0 ? void 0 : _b.addEventListener('message', async (event) => {\n                this._debug('received broadcast notification from other tab or client', event);\n                await this._notifyAllSubscribers(event.data.event, event.data.session, false); // broadcast = false so we don't get an endless loop of messages\n            });\n        }\n        this.initialize();\n    }\n    _debug(...args) {\n        if (this.logDebugMessages) {\n            this.logger(`GoTrueClient@${this.instanceID} (${_lib_version__WEBPACK_IMPORTED_MODULE_7__.version}) ${new Date().toISOString()}`, ...args);\n        }\n        return this;\n    }\n    /**\n     * Initializes the client session either from the url or from storage.\n     * This method is automatically called when instantiating the client, but should also be called\n     * manually when checking for an error from an auth redirect (oauth, magiclink, password recovery, etc).\n     */\n    async initialize() {\n        if (this.initializePromise) {\n            return await this.initializePromise;\n        }\n        this.initializePromise = (async () => {\n            return await this._acquireLock(-1, async () => {\n                return await this._initialize();\n            });\n        })();\n        return await this.initializePromise;\n    }\n    /**\n     * IMPORTANT:\n     * 1. Never throw in this method, as it is called from the constructor\n     * 2. Never return a session from this method as it would be cached over\n     *    the whole lifetime of the client\n     */\n    async _initialize() {\n        var _a;\n        try {\n            const params = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.parseParametersFromURL)(window.location.href);\n            let callbackUrlType = 'none';\n            if (this._isImplicitGrantCallback(params)) {\n                callbackUrlType = 'implicit';\n            }\n            else if (await this._isPKCECallback(params)) {\n                callbackUrlType = 'pkce';\n            }\n            /**\n             * Attempt to get the session from the URL only if these conditions are fulfilled\n             *\n             * Note: If the URL isn't one of the callback url types (implicit or pkce),\n             * then there could be an existing session so we don't want to prematurely remove it\n             */\n            if ((0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)() && this.detectSessionInUrl && callbackUrlType !== 'none') {\n                const { data, error } = await this._getSessionFromURL(params, callbackUrlType);\n                if (error) {\n                    this._debug('#_initialize()', 'error detecting session from URL', error);\n                    if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthImplicitGrantRedirectError)(error)) {\n                        const errorCode = (_a = error.details) === null || _a === void 0 ? void 0 : _a.code;\n                        if (errorCode === 'identity_already_exists' ||\n                            errorCode === 'identity_not_found' ||\n                            errorCode === 'single_identity_not_deletable') {\n                            return { error };\n                        }\n                    }\n                    // failed login attempt via url,\n                    // remove old session as in verifyOtp, signUp and signInWith*\n                    await this._removeSession();\n                    return { error };\n                }\n                const { session, redirectType } = data;\n                this._debug('#_initialize()', 'detected session in URL', session, 'redirect type', redirectType);\n                await this._saveSession(session);\n                setTimeout(async () => {\n                    if (redirectType === 'recovery') {\n                        await this._notifyAllSubscribers('PASSWORD_RECOVERY', session);\n                    }\n                    else {\n                        await this._notifyAllSubscribers('SIGNED_IN', session);\n                    }\n                }, 0);\n                return { error: null };\n            }\n            // no login attempt via callback url try to recover session from storage\n            await this._recoverAndRefresh();\n            return { error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { error };\n            }\n            return {\n                error: new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthUnknownError('Unexpected error during initialization', error),\n            };\n        }\n        finally {\n            await this._handleVisibilityChange();\n            this._debug('#_initialize()', 'end');\n        }\n    }\n    /**\n     * Creates a new anonymous user.\n     *\n     * @returns A session where the is_anonymous claim in the access token JWT set to true\n     */\n    async signInAnonymously(credentials) {\n        var _a, _b, _c;\n        try {\n            const res = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/signup`, {\n                headers: this.headers,\n                body: {\n                    data: (_b = (_a = credentials === null || credentials === void 0 ? void 0 : credentials.options) === null || _a === void 0 ? void 0 : _a.data) !== null && _b !== void 0 ? _b : {},\n                    gotrue_meta_security: { captcha_token: (_c = credentials === null || credentials === void 0 ? void 0 : credentials.options) === null || _c === void 0 ? void 0 : _c.captchaToken },\n                },\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponse,\n            });\n            const { data, error } = res;\n            if (error || !data) {\n                return { data: { user: null, session: null }, error: error };\n            }\n            const session = data.session;\n            const user = data.user;\n            if (data.session) {\n                await this._saveSession(data.session);\n                await this._notifyAllSubscribers('SIGNED_IN', session);\n            }\n            return { data: { user, session }, error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Creates a new user.\n     *\n     * Be aware that if a user account exists in the system you may get back an\n     * error message that attempts to hide this information from the user.\n     * This method has support for PKCE via email signups. The PKCE flow cannot be used when autoconfirm is enabled.\n     *\n     * @returns A logged-in session if the server has \"autoconfirm\" ON\n     * @returns A user if the server has \"autoconfirm\" OFF\n     */\n    async signUp(credentials) {\n        var _a, _b, _c;\n        try {\n            let res;\n            if ('email' in credentials) {\n                const { email, password, options } = credentials;\n                let codeChallenge = null;\n                let codeChallengeMethod = null;\n                if (this.flowType === 'pkce') {\n                    ;\n                    [codeChallenge, codeChallengeMethod] = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getCodeChallengeAndMethod)(this.storage, this.storageKey);\n                }\n                res = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/signup`, {\n                    headers: this.headers,\n                    redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo,\n                    body: {\n                        email,\n                        password,\n                        data: (_a = options === null || options === void 0 ? void 0 : options.data) !== null && _a !== void 0 ? _a : {},\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                        code_challenge: codeChallenge,\n                        code_challenge_method: codeChallengeMethod,\n                    },\n                    xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponse,\n                });\n            }\n            else if ('phone' in credentials) {\n                const { phone, password, options } = credentials;\n                res = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/signup`, {\n                    headers: this.headers,\n                    body: {\n                        phone,\n                        password,\n                        data: (_b = options === null || options === void 0 ? void 0 : options.data) !== null && _b !== void 0 ? _b : {},\n                        channel: (_c = options === null || options === void 0 ? void 0 : options.channel) !== null && _c !== void 0 ? _c : 'sms',\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                    },\n                    xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponse,\n                });\n            }\n            else {\n                throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidCredentialsError('You must provide either an email or phone number and a password');\n            }\n            const { data, error } = res;\n            if (error || !data) {\n                return { data: { user: null, session: null }, error: error };\n            }\n            const session = data.session;\n            const user = data.user;\n            if (data.session) {\n                await this._saveSession(data.session);\n                await this._notifyAllSubscribers('SIGNED_IN', session);\n            }\n            return { data: { user, session }, error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Log in an existing user with an email and password or phone and password.\n     *\n     * Be aware that you may get back an error message that will not distinguish\n     * between the cases where the account does not exist or that the\n     * email/phone and password combination is wrong or that the account can only\n     * be accessed via social login.\n     */\n    async signInWithPassword(credentials) {\n        try {\n            let res;\n            if ('email' in credentials) {\n                const { email, password, options } = credentials;\n                res = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/token?grant_type=password`, {\n                    headers: this.headers,\n                    body: {\n                        email,\n                        password,\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                    },\n                    xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponsePassword,\n                });\n            }\n            else if ('phone' in credentials) {\n                const { phone, password, options } = credentials;\n                res = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/token?grant_type=password`, {\n                    headers: this.headers,\n                    body: {\n                        phone,\n                        password,\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                    },\n                    xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponsePassword,\n                });\n            }\n            else {\n                throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidCredentialsError('You must provide either an email or phone number and a password');\n            }\n            const { data, error } = res;\n            if (error) {\n                return { data: { user: null, session: null }, error };\n            }\n            else if (!data || !data.session || !data.user) {\n                return { data: { user: null, session: null }, error: new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidTokenResponseError() };\n            }\n            if (data.session) {\n                await this._saveSession(data.session);\n                await this._notifyAllSubscribers('SIGNED_IN', data.session);\n            }\n            return {\n                data: Object.assign({ user: data.user, session: data.session }, (data.weak_password ? { weakPassword: data.weak_password } : null)),\n                error,\n            };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Log in an existing user via a third-party provider.\n     * This method supports the PKCE flow.\n     */\n    async signInWithOAuth(credentials) {\n        var _a, _b, _c, _d;\n        return await this._handleProviderSignIn(credentials.provider, {\n            redirectTo: (_a = credentials.options) === null || _a === void 0 ? void 0 : _a.redirectTo,\n            scopes: (_b = credentials.options) === null || _b === void 0 ? void 0 : _b.scopes,\n            queryParams: (_c = credentials.options) === null || _c === void 0 ? void 0 : _c.queryParams,\n            skipBrowserRedirect: (_d = credentials.options) === null || _d === void 0 ? void 0 : _d.skipBrowserRedirect,\n        });\n    }\n    /**\n     * Log in an existing user by exchanging an Auth Code issued during the PKCE flow.\n     */\n    async exchangeCodeForSession(authCode) {\n        await this.initializePromise;\n        return this._acquireLock(-1, async () => {\n            return this._exchangeCodeForSession(authCode);\n        });\n    }\n    /**\n     * Signs in a user by verifying a message signed by the user's private key.\n     * Only Solana supported at this time, using the Sign in with Solana standard.\n     */\n    async signInWithWeb3(credentials) {\n        const { chain } = credentials;\n        if (chain === 'solana') {\n            return await this.signInWithSolana(credentials);\n        }\n        throw new Error(`@supabase/auth-js: Unsupported chain \"${chain}\"`);\n    }\n    async signInWithSolana(credentials) {\n        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;\n        let message;\n        let signature;\n        if ('message' in credentials) {\n            message = credentials.message;\n            signature = credentials.signature;\n        }\n        else {\n            const { chain, wallet, statement, options } = credentials;\n            let resolvedWallet;\n            if (!(0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)()) {\n                if (typeof wallet !== 'object' || !(options === null || options === void 0 ? void 0 : options.url)) {\n                    throw new Error('@supabase/auth-js: Both wallet and url must be specified in non-browser environments.');\n                }\n                resolvedWallet = wallet;\n            }\n            else if (typeof wallet === 'object') {\n                resolvedWallet = wallet;\n            }\n            else {\n                const windowAny = window;\n                if ('solana' in windowAny &&\n                    typeof windowAny.solana === 'object' &&\n                    (('signIn' in windowAny.solana && typeof windowAny.solana.signIn === 'function') ||\n                        ('signMessage' in windowAny.solana &&\n                            typeof windowAny.solana.signMessage === 'function'))) {\n                    resolvedWallet = windowAny.solana;\n                }\n                else {\n                    throw new Error(`@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.`);\n                }\n            }\n            const url = new URL((_a = options === null || options === void 0 ? void 0 : options.url) !== null && _a !== void 0 ? _a : window.location.href);\n            if ('signIn' in resolvedWallet && resolvedWallet.signIn) {\n                const output = await resolvedWallet.signIn(Object.assign(Object.assign(Object.assign({ issuedAt: new Date().toISOString() }, options === null || options === void 0 ? void 0 : options.signInWithSolana), { \n                    // non-overridable properties\n                    version: '1', domain: url.host, uri: url.href }), (statement ? { statement } : null)));\n                let outputToProcess;\n                if (Array.isArray(output) && output[0] && typeof output[0] === 'object') {\n                    outputToProcess = output[0];\n                }\n                else if (output &&\n                    typeof output === 'object' &&\n                    'signedMessage' in output &&\n                    'signature' in output) {\n                    outputToProcess = output;\n                }\n                else {\n                    throw new Error('@supabase/auth-js: Wallet method signIn() returned unrecognized value');\n                }\n                if ('signedMessage' in outputToProcess &&\n                    'signature' in outputToProcess &&\n                    (typeof outputToProcess.signedMessage === 'string' ||\n                        outputToProcess.signedMessage instanceof Uint8Array) &&\n                    outputToProcess.signature instanceof Uint8Array) {\n                    message =\n                        typeof outputToProcess.signedMessage === 'string'\n                            ? outputToProcess.signedMessage\n                            : new TextDecoder().decode(outputToProcess.signedMessage);\n                    signature = outputToProcess.signature;\n                }\n                else {\n                    throw new Error('@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields');\n                }\n            }\n            else {\n                if (!('signMessage' in resolvedWallet) ||\n                    typeof resolvedWallet.signMessage !== 'function' ||\n                    !('publicKey' in resolvedWallet) ||\n                    typeof resolvedWallet !== 'object' ||\n                    !resolvedWallet.publicKey ||\n                    !('toBase58' in resolvedWallet.publicKey) ||\n                    typeof resolvedWallet.publicKey.toBase58 !== 'function') {\n                    throw new Error('@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API');\n                }\n                message = [\n                    `${url.host} wants you to sign in with your Solana account:`,\n                    resolvedWallet.publicKey.toBase58(),\n                    ...(statement ? ['', statement, ''] : ['']),\n                    'Version: 1',\n                    `URI: ${url.href}`,\n                    `Issued At: ${(_c = (_b = options === null || options === void 0 ? void 0 : options.signInWithSolana) === null || _b === void 0 ? void 0 : _b.issuedAt) !== null && _c !== void 0 ? _c : new Date().toISOString()}`,\n                    ...(((_d = options === null || options === void 0 ? void 0 : options.signInWithSolana) === null || _d === void 0 ? void 0 : _d.notBefore)\n                        ? [`Not Before: ${options.signInWithSolana.notBefore}`]\n                        : []),\n                    ...(((_e = options === null || options === void 0 ? void 0 : options.signInWithSolana) === null || _e === void 0 ? void 0 : _e.expirationTime)\n                        ? [`Expiration Time: ${options.signInWithSolana.expirationTime}`]\n                        : []),\n                    ...(((_f = options === null || options === void 0 ? void 0 : options.signInWithSolana) === null || _f === void 0 ? void 0 : _f.chainId)\n                        ? [`Chain ID: ${options.signInWithSolana.chainId}`]\n                        : []),\n                    ...(((_g = options === null || options === void 0 ? void 0 : options.signInWithSolana) === null || _g === void 0 ? void 0 : _g.nonce) ? [`Nonce: ${options.signInWithSolana.nonce}`] : []),\n                    ...(((_h = options === null || options === void 0 ? void 0 : options.signInWithSolana) === null || _h === void 0 ? void 0 : _h.requestId)\n                        ? [`Request ID: ${options.signInWithSolana.requestId}`]\n                        : []),\n                    ...(((_k = (_j = options === null || options === void 0 ? void 0 : options.signInWithSolana) === null || _j === void 0 ? void 0 : _j.resources) === null || _k === void 0 ? void 0 : _k.length)\n                        ? [\n                            'Resources',\n                            ...options.signInWithSolana.resources.map((resource) => `- ${resource}`),\n                        ]\n                        : []),\n                ].join('\\n');\n                const maybeSignature = await resolvedWallet.signMessage(new TextEncoder().encode(message), 'utf8');\n                if (!maybeSignature || !(maybeSignature instanceof Uint8Array)) {\n                    throw new Error('@supabase/auth-js: Wallet signMessage() API returned an recognized value');\n                }\n                signature = maybeSignature;\n            }\n        }\n        try {\n            const { data, error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/token?grant_type=web3`, {\n                headers: this.headers,\n                body: Object.assign({ chain: 'solana', message, signature: (0,_lib_base64url__WEBPACK_IMPORTED_MODULE_9__.bytesToBase64URL)(signature) }, (((_l = credentials.options) === null || _l === void 0 ? void 0 : _l.captchaToken)\n                    ? { gotrue_meta_security: { captcha_token: (_m = credentials.options) === null || _m === void 0 ? void 0 : _m.captchaToken } }\n                    : null)),\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponse,\n            });\n            if (error) {\n                throw error;\n            }\n            if (!data || !data.session || !data.user) {\n                return {\n                    data: { user: null, session: null },\n                    error: new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidTokenResponseError(),\n                };\n            }\n            if (data.session) {\n                await this._saveSession(data.session);\n                await this._notifyAllSubscribers('SIGNED_IN', data.session);\n            }\n            return { data: Object.assign({}, data), error };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    async _exchangeCodeForSession(authCode) {\n        const storageItem = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getItemAsync)(this.storage, `${this.storageKey}-code-verifier`);\n        const [codeVerifier, redirectType] = (storageItem !== null && storageItem !== void 0 ? storageItem : '').split('/');\n        try {\n            const { data, error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/token?grant_type=pkce`, {\n                headers: this.headers,\n                body: {\n                    auth_code: authCode,\n                    code_verifier: codeVerifier,\n                },\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponse,\n            });\n            await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.removeItemAsync)(this.storage, `${this.storageKey}-code-verifier`);\n            if (error) {\n                throw error;\n            }\n            if (!data || !data.session || !data.user) {\n                return {\n                    data: { user: null, session: null, redirectType: null },\n                    error: new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidTokenResponseError(),\n                };\n            }\n            if (data.session) {\n                await this._saveSession(data.session);\n                await this._notifyAllSubscribers('SIGNED_IN', data.session);\n            }\n            return { data: Object.assign(Object.assign({}, data), { redirectType: redirectType !== null && redirectType !== void 0 ? redirectType : null }), error };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null, redirectType: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Allows signing in with an OIDC ID token. The authentication provider used\n     * should be enabled and configured.\n     */\n    async signInWithIdToken(credentials) {\n        try {\n            const { options, provider, token, access_token, nonce } = credentials;\n            const res = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/token?grant_type=id_token`, {\n                headers: this.headers,\n                body: {\n                    provider,\n                    id_token: token,\n                    access_token,\n                    nonce,\n                    gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                },\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponse,\n            });\n            const { data, error } = res;\n            if (error) {\n                return { data: { user: null, session: null }, error };\n            }\n            else if (!data || !data.session || !data.user) {\n                return {\n                    data: { user: null, session: null },\n                    error: new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidTokenResponseError(),\n                };\n            }\n            if (data.session) {\n                await this._saveSession(data.session);\n                await this._notifyAllSubscribers('SIGNED_IN', data.session);\n            }\n            return { data, error };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Log in a user using magiclink or a one-time password (OTP).\n     *\n     * If the `{{ .ConfirmationURL }}` variable is specified in the email template, a magiclink will be sent.\n     * If the `{{ .Token }}` variable is specified in the email template, an OTP will be sent.\n     * If you're using phone sign-ins, only an OTP will be sent. You won't be able to send a magiclink for phone sign-ins.\n     *\n     * Be aware that you may get back an error message that will not distinguish\n     * between the cases where the account does not exist or, that the account\n     * can only be accessed via social login.\n     *\n     * Do note that you will need to configure a Whatsapp sender on Twilio\n     * if you are using phone sign in with the 'whatsapp' channel. The whatsapp\n     * channel is not supported on other providers\n     * at this time.\n     * This method supports PKCE when an email is passed.\n     */\n    async signInWithOtp(credentials) {\n        var _a, _b, _c, _d, _e;\n        try {\n            if ('email' in credentials) {\n                const { email, options } = credentials;\n                let codeChallenge = null;\n                let codeChallengeMethod = null;\n                if (this.flowType === 'pkce') {\n                    ;\n                    [codeChallenge, codeChallengeMethod] = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getCodeChallengeAndMethod)(this.storage, this.storageKey);\n                }\n                const { error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/otp`, {\n                    headers: this.headers,\n                    body: {\n                        email,\n                        data: (_a = options === null || options === void 0 ? void 0 : options.data) !== null && _a !== void 0 ? _a : {},\n                        create_user: (_b = options === null || options === void 0 ? void 0 : options.shouldCreateUser) !== null && _b !== void 0 ? _b : true,\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                        code_challenge: codeChallenge,\n                        code_challenge_method: codeChallengeMethod,\n                    },\n                    redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo,\n                });\n                return { data: { user: null, session: null }, error };\n            }\n            if ('phone' in credentials) {\n                const { phone, options } = credentials;\n                const { data, error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/otp`, {\n                    headers: this.headers,\n                    body: {\n                        phone,\n                        data: (_c = options === null || options === void 0 ? void 0 : options.data) !== null && _c !== void 0 ? _c : {},\n                        create_user: (_d = options === null || options === void 0 ? void 0 : options.shouldCreateUser) !== null && _d !== void 0 ? _d : true,\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                        channel: (_e = options === null || options === void 0 ? void 0 : options.channel) !== null && _e !== void 0 ? _e : 'sms',\n                    },\n                });\n                return { data: { user: null, session: null, messageId: data === null || data === void 0 ? void 0 : data.message_id }, error };\n            }\n            throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidCredentialsError('You must provide either an email or phone number.');\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Log in a user given a User supplied OTP or TokenHash received through mobile or email.\n     */\n    async verifyOtp(params) {\n        var _a, _b;\n        try {\n            let redirectTo = undefined;\n            let captchaToken = undefined;\n            if ('options' in params) {\n                redirectTo = (_a = params.options) === null || _a === void 0 ? void 0 : _a.redirectTo;\n                captchaToken = (_b = params.options) === null || _b === void 0 ? void 0 : _b.captchaToken;\n            }\n            const { data, error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/verify`, {\n                headers: this.headers,\n                body: Object.assign(Object.assign({}, params), { gotrue_meta_security: { captcha_token: captchaToken } }),\n                redirectTo,\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponse,\n            });\n            if (error) {\n                throw error;\n            }\n            if (!data) {\n                throw new Error('An error occurred on token verification.');\n            }\n            const session = data.session;\n            const user = data.user;\n            if (session === null || session === void 0 ? void 0 : session.access_token) {\n                await this._saveSession(session);\n                await this._notifyAllSubscribers(params.type == 'recovery' ? 'PASSWORD_RECOVERY' : 'SIGNED_IN', session);\n            }\n            return { data: { user, session }, error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Attempts a single-sign on using an enterprise Identity Provider. A\n     * successful SSO attempt will redirect the current page to the identity\n     * provider authorization page. The redirect URL is implementation and SSO\n     * protocol specific.\n     *\n     * You can use it by providing a SSO domain. Typically you can extract this\n     * domain by asking users for their email address. If this domain is\n     * registered on the Auth instance the redirect will use that organization's\n     * currently active SSO Identity Provider for the login.\n     *\n     * If you have built an organization-specific login page, you can use the\n     * organization's SSO Identity Provider UUID directly instead.\n     */\n    async signInWithSSO(params) {\n        var _a, _b, _c;\n        try {\n            let codeChallenge = null;\n            let codeChallengeMethod = null;\n            if (this.flowType === 'pkce') {\n                ;\n                [codeChallenge, codeChallengeMethod] = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getCodeChallengeAndMethod)(this.storage, this.storageKey);\n            }\n            return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/sso`, {\n                body: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, ('providerId' in params ? { provider_id: params.providerId } : null)), ('domain' in params ? { domain: params.domain } : null)), { redirect_to: (_b = (_a = params.options) === null || _a === void 0 ? void 0 : _a.redirectTo) !== null && _b !== void 0 ? _b : undefined }), (((_c = params === null || params === void 0 ? void 0 : params.options) === null || _c === void 0 ? void 0 : _c.captchaToken)\n                    ? { gotrue_meta_security: { captcha_token: params.options.captchaToken } }\n                    : null)), { skip_http_redirect: true, code_challenge: codeChallenge, code_challenge_method: codeChallengeMethod }),\n                headers: this.headers,\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._ssoResponse,\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Sends a reauthentication OTP to the user's email or phone number.\n     * Requires the user to be signed-in.\n     */\n    async reauthenticate() {\n        await this.initializePromise;\n        return await this._acquireLock(-1, async () => {\n            return await this._reauthenticate();\n        });\n    }\n    async _reauthenticate() {\n        try {\n            return await this._useSession(async (result) => {\n                const { data: { session }, error: sessionError, } = result;\n                if (sessionError)\n                    throw sessionError;\n                if (!session)\n                    throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthSessionMissingError();\n                const { error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'GET', `${this.url}/reauthenticate`, {\n                    headers: this.headers,\n                    jwt: session.access_token,\n                });\n                return { data: { user: null, session: null }, error };\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Resends an existing signup confirmation email, email change email, SMS OTP or phone change OTP.\n     */\n    async resend(credentials) {\n        try {\n            const endpoint = `${this.url}/resend`;\n            if ('email' in credentials) {\n                const { email, type, options } = credentials;\n                const { error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', endpoint, {\n                    headers: this.headers,\n                    body: {\n                        email,\n                        type,\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                    },\n                    redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo,\n                });\n                return { data: { user: null, session: null }, error };\n            }\n            else if ('phone' in credentials) {\n                const { phone, type, options } = credentials;\n                const { data, error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', endpoint, {\n                    headers: this.headers,\n                    body: {\n                        phone,\n                        type,\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                    },\n                });\n                return { data: { user: null, session: null, messageId: data === null || data === void 0 ? void 0 : data.message_id }, error };\n            }\n            throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidCredentialsError('You must provide either an email or phone number and a type');\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Returns the session, refreshing it if necessary.\n     *\n     * The session returned can be null if the session is not detected which can happen in the event a user is not signed-in or has logged out.\n     *\n     * **IMPORTANT:** This method loads values directly from the storage attached\n     * to the client. If that storage is based on request cookies for example,\n     * the values in it may not be authentic and therefore it's strongly advised\n     * against using this method and its results in such circumstances. A warning\n     * will be emitted if this is detected. Use {@link #getUser()} instead.\n     */\n    async getSession() {\n        await this.initializePromise;\n        const result = await this._acquireLock(-1, async () => {\n            return this._useSession(async (result) => {\n                return result;\n            });\n        });\n        return result;\n    }\n    /**\n     * Acquires a global lock based on the storage key.\n     */\n    async _acquireLock(acquireTimeout, fn) {\n        this._debug('#_acquireLock', 'begin', acquireTimeout);\n        try {\n            if (this.lockAcquired) {\n                const last = this.pendingInLock.length\n                    ? this.pendingInLock[this.pendingInLock.length - 1]\n                    : Promise.resolve();\n                const result = (async () => {\n                    await last;\n                    return await fn();\n                })();\n                this.pendingInLock.push((async () => {\n                    try {\n                        await result;\n                    }\n                    catch (e) {\n                        // we just care if it finished\n                    }\n                })());\n                return result;\n            }\n            return await this.lock(`lock:${this.storageKey}`, acquireTimeout, async () => {\n                this._debug('#_acquireLock', 'lock acquired for storage key', this.storageKey);\n                try {\n                    this.lockAcquired = true;\n                    const result = fn();\n                    this.pendingInLock.push((async () => {\n                        try {\n                            await result;\n                        }\n                        catch (e) {\n                            // we just care if it finished\n                        }\n                    })());\n                    await result;\n                    // keep draining the queue until there's nothing to wait on\n                    while (this.pendingInLock.length) {\n                        const waitOn = [...this.pendingInLock];\n                        await Promise.all(waitOn);\n                        this.pendingInLock.splice(0, waitOn.length);\n                    }\n                    return await result;\n                }\n                finally {\n                    this._debug('#_acquireLock', 'lock released for storage key', this.storageKey);\n                    this.lockAcquired = false;\n                }\n            });\n        }\n        finally {\n            this._debug('#_acquireLock', 'end');\n        }\n    }\n    /**\n     * Use instead of {@link #getSession} inside the library. It is\n     * semantically usually what you want, as getting a session involves some\n     * processing afterwards that requires only one client operating on the\n     * session at once across multiple tabs or processes.\n     */\n    async _useSession(fn) {\n        this._debug('#_useSession', 'begin');\n        try {\n            // the use of __loadSession here is the only correct use of the function!\n            const result = await this.__loadSession();\n            return await fn(result);\n        }\n        finally {\n            this._debug('#_useSession', 'end');\n        }\n    }\n    /**\n     * NEVER USE DIRECTLY!\n     *\n     * Always use {@link #_useSession}.\n     */\n    async __loadSession() {\n        this._debug('#__loadSession()', 'begin');\n        if (!this.lockAcquired) {\n            this._debug('#__loadSession()', 'used outside of an acquired lock!', new Error().stack);\n        }\n        try {\n            let currentSession = null;\n            const maybeSession = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getItemAsync)(this.storage, this.storageKey);\n            this._debug('#getSession()', 'session from storage', maybeSession);\n            if (maybeSession !== null) {\n                if (this._isValidSession(maybeSession)) {\n                    currentSession = maybeSession;\n                }\n                else {\n                    this._debug('#getSession()', 'session from storage is not valid');\n                    await this._removeSession();\n                }\n            }\n            if (!currentSession) {\n                return { data: { session: null }, error: null };\n            }\n            // A session is considered expired before the access token _actually_\n            // expires. When the autoRefreshToken option is off (or when the tab is\n            // in the background), very eager users of getSession() -- like\n            // realtime-js -- might send a valid JWT which will expire by the time it\n            // reaches the server.\n            const hasExpired = currentSession.expires_at\n                ? currentSession.expires_at * 1000 - Date.now() < _lib_constants__WEBPACK_IMPORTED_MODULE_1__.EXPIRY_MARGIN_MS\n                : false;\n            this._debug('#__loadSession()', `session has${hasExpired ? '' : ' not'} expired`, 'expires_at', currentSession.expires_at);\n            if (!hasExpired) {\n                if (this.storage.isServer) {\n                    let suppressWarning = this.suppressGetSessionWarning;\n                    const proxySession = new Proxy(currentSession, {\n                        get: (target, prop, receiver) => {\n                            if (!suppressWarning && prop === 'user') {\n                                // only show warning when the user object is being accessed from the server\n                                console.warn('Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.');\n                                suppressWarning = true; // keeps this proxy instance from logging additional warnings\n                                this.suppressGetSessionWarning = true; // keeps this client's future proxy instances from warning\n                            }\n                            return Reflect.get(target, prop, receiver);\n                        },\n                    });\n                    currentSession = proxySession;\n                }\n                return { data: { session: currentSession }, error: null };\n            }\n            const { session, error } = await this._callRefreshToken(currentSession.refresh_token);\n            if (error) {\n                return { data: { session: null }, error };\n            }\n            return { data: { session }, error: null };\n        }\n        finally {\n            this._debug('#__loadSession()', 'end');\n        }\n    }\n    /**\n     * Gets the current user details if there is an existing session. This method\n     * performs a network request to the Supabase Auth server, so the returned\n     * value is authentic and can be used to base authorization rules on.\n     *\n     * @param jwt Takes in an optional access token JWT. If no JWT is provided, the JWT from the current session is used.\n     */\n    async getUser(jwt) {\n        if (jwt) {\n            return await this._getUser(jwt);\n        }\n        await this.initializePromise;\n        const result = await this._acquireLock(-1, async () => {\n            return await this._getUser();\n        });\n        return result;\n    }\n    async _getUser(jwt) {\n        try {\n            if (jwt) {\n                return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'GET', `${this.url}/user`, {\n                    headers: this.headers,\n                    jwt: jwt,\n                    xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._userResponse,\n                });\n            }\n            return await this._useSession(async (result) => {\n                var _a, _b, _c;\n                const { data, error } = result;\n                if (error) {\n                    throw error;\n                }\n                // returns an error if there is no access_token or custom authorization header\n                if (!((_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token) && !this.hasCustomAuthorizationHeader) {\n                    return { data: { user: null }, error: new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthSessionMissingError() };\n                }\n                return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'GET', `${this.url}/user`, {\n                    headers: this.headers,\n                    jwt: (_c = (_b = data.session) === null || _b === void 0 ? void 0 : _b.access_token) !== null && _c !== void 0 ? _c : undefined,\n                    xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._userResponse,\n                });\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthSessionMissingError)(error)) {\n                    // JWT contains a `session_id` which does not correspond to an active\n                    // session in the database, indicating the user is signed out.\n                    await this._removeSession();\n                    await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.removeItemAsync)(this.storage, `${this.storageKey}-code-verifier`);\n                }\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Updates user data for a logged in user.\n     */\n    async updateUser(attributes, options = {}) {\n        await this.initializePromise;\n        return await this._acquireLock(-1, async () => {\n            return await this._updateUser(attributes, options);\n        });\n    }\n    async _updateUser(attributes, options = {}) {\n        try {\n            return await this._useSession(async (result) => {\n                const { data: sessionData, error: sessionError } = result;\n                if (sessionError) {\n                    throw sessionError;\n                }\n                if (!sessionData.session) {\n                    throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthSessionMissingError();\n                }\n                const session = sessionData.session;\n                let codeChallenge = null;\n                let codeChallengeMethod = null;\n                if (this.flowType === 'pkce' && attributes.email != null) {\n                    ;\n                    [codeChallenge, codeChallengeMethod] = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getCodeChallengeAndMethod)(this.storage, this.storageKey);\n                }\n                const { data, error: userError } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'PUT', `${this.url}/user`, {\n                    headers: this.headers,\n                    redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo,\n                    body: Object.assign(Object.assign({}, attributes), { code_challenge: codeChallenge, code_challenge_method: codeChallengeMethod }),\n                    jwt: session.access_token,\n                    xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._userResponse,\n                });\n                if (userError)\n                    throw userError;\n                session.user = data.user;\n                await this._saveSession(session);\n                await this._notifyAllSubscribers('USER_UPDATED', session);\n                return { data: { user: session.user }, error: null };\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Sets the session data from the current session. If the current session is expired, setSession will take care of refreshing it to obtain a new session.\n     * If the refresh token or access token in the current session is invalid, an error will be thrown.\n     * @param currentSession The current session that minimally contains an access token and refresh token.\n     */\n    async setSession(currentSession) {\n        await this.initializePromise;\n        return await this._acquireLock(-1, async () => {\n            return await this._setSession(currentSession);\n        });\n    }\n    async _setSession(currentSession) {\n        try {\n            if (!currentSession.access_token || !currentSession.refresh_token) {\n                throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthSessionMissingError();\n            }\n            const timeNow = Date.now() / 1000;\n            let expiresAt = timeNow;\n            let hasExpired = true;\n            let session = null;\n            const { payload } = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.decodeJWT)(currentSession.access_token);\n            if (payload.exp) {\n                expiresAt = payload.exp;\n                hasExpired = expiresAt <= timeNow;\n            }\n            if (hasExpired) {\n                const { session: refreshedSession, error } = await this._callRefreshToken(currentSession.refresh_token);\n                if (error) {\n                    return { data: { user: null, session: null }, error: error };\n                }\n                if (!refreshedSession) {\n                    return { data: { user: null, session: null }, error: null };\n                }\n                session = refreshedSession;\n            }\n            else {\n                const { data, error } = await this._getUser(currentSession.access_token);\n                if (error) {\n                    throw error;\n                }\n                session = {\n                    access_token: currentSession.access_token,\n                    refresh_token: currentSession.refresh_token,\n                    user: data.user,\n                    token_type: 'bearer',\n                    expires_in: expiresAt - timeNow,\n                    expires_at: expiresAt,\n                };\n                await this._saveSession(session);\n                await this._notifyAllSubscribers('SIGNED_IN', session);\n            }\n            return { data: { user: session.user, session }, error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { session: null, user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Returns a new session, regardless of expiry status.\n     * Takes in an optional current session. If not passed in, then refreshSession() will attempt to retrieve it from getSession().\n     * If the current session's refresh token is invalid, an error will be thrown.\n     * @param currentSession The current session. If passed in, it must contain a refresh token.\n     */\n    async refreshSession(currentSession) {\n        await this.initializePromise;\n        return await this._acquireLock(-1, async () => {\n            return await this._refreshSession(currentSession);\n        });\n    }\n    async _refreshSession(currentSession) {\n        try {\n            return await this._useSession(async (result) => {\n                var _a;\n                if (!currentSession) {\n                    const { data, error } = result;\n                    if (error) {\n                        throw error;\n                    }\n                    currentSession = (_a = data.session) !== null && _a !== void 0 ? _a : undefined;\n                }\n                if (!(currentSession === null || currentSession === void 0 ? void 0 : currentSession.refresh_token)) {\n                    throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthSessionMissingError();\n                }\n                const { session, error } = await this._callRefreshToken(currentSession.refresh_token);\n                if (error) {\n                    return { data: { user: null, session: null }, error: error };\n                }\n                if (!session) {\n                    return { data: { user: null, session: null }, error: null };\n                }\n                return { data: { user: session.user, session }, error: null };\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Gets the session data from a URL string\n     */\n    async _getSessionFromURL(params, callbackUrlType) {\n        try {\n            if (!(0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)())\n                throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthImplicitGrantRedirectError('No browser detected.');\n            // If there's an error in the URL, it doesn't matter what flow it is, we just return the error.\n            if (params.error || params.error_description || params.error_code) {\n                // The error class returned implies that the redirect is from an implicit grant flow\n                // but it could also be from a redirect error from a PKCE flow.\n                throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthImplicitGrantRedirectError(params.error_description || 'Error in URL with unspecified error_description', {\n                    error: params.error || 'unspecified_error',\n                    code: params.error_code || 'unspecified_code',\n                });\n            }\n            // Checks for mismatches between the flowType initialised in the client and the URL parameters\n            switch (callbackUrlType) {\n                case 'implicit':\n                    if (this.flowType === 'pkce') {\n                        throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthPKCEGrantCodeExchangeError('Not a valid PKCE flow url.');\n                    }\n                    break;\n                case 'pkce':\n                    if (this.flowType === 'implicit') {\n                        throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthImplicitGrantRedirectError('Not a valid implicit grant flow url.');\n                    }\n                    break;\n                default:\n                // there's no mismatch so we continue\n            }\n            // Since this is a redirect for PKCE, we attempt to retrieve the code from the URL for the code exchange\n            if (callbackUrlType === 'pkce') {\n                this._debug('#_initialize()', 'begin', 'is PKCE flow', true);\n                if (!params.code)\n                    throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthPKCEGrantCodeExchangeError('No code detected.');\n                const { data, error } = await this._exchangeCodeForSession(params.code);\n                if (error)\n                    throw error;\n                const url = new URL(window.location.href);\n                url.searchParams.delete('code');\n                window.history.replaceState(window.history.state, '', url.toString());\n                return { data: { session: data.session, redirectType: null }, error: null };\n            }\n            const { provider_token, provider_refresh_token, access_token, refresh_token, expires_in, expires_at, token_type, } = params;\n            if (!access_token || !expires_in || !refresh_token || !token_type) {\n                throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthImplicitGrantRedirectError('No session defined in URL');\n            }\n            const timeNow = Math.round(Date.now() / 1000);\n            const expiresIn = parseInt(expires_in);\n            let expiresAt = timeNow + expiresIn;\n            if (expires_at) {\n                expiresAt = parseInt(expires_at);\n            }\n            const actuallyExpiresIn = expiresAt - timeNow;\n            if (actuallyExpiresIn * 1000 <= _lib_constants__WEBPACK_IMPORTED_MODULE_1__.AUTO_REFRESH_TICK_DURATION_MS) {\n                console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${actuallyExpiresIn}s, should have been closer to ${expiresIn}s`);\n            }\n            const issuedAt = expiresAt - expiresIn;\n            if (timeNow - issuedAt >= 120) {\n                console.warn('@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale', issuedAt, expiresAt, timeNow);\n            }\n            else if (timeNow - issuedAt < 0) {\n                console.warn('@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew', issuedAt, expiresAt, timeNow);\n            }\n            const { data, error } = await this._getUser(access_token);\n            if (error)\n                throw error;\n            const session = {\n                provider_token,\n                provider_refresh_token,\n                access_token,\n                expires_in: expiresIn,\n                expires_at: expiresAt,\n                refresh_token,\n                token_type,\n                user: data.user,\n            };\n            // Remove tokens from URL\n            window.location.hash = '';\n            this._debug('#_getSessionFromURL()', 'clearing window.location.hash');\n            return { data: { session, redirectType: params.type }, error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { session: null, redirectType: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Checks if the current URL contains parameters given by an implicit oauth grant flow (https://www.rfc-editor.org/rfc/rfc6749.html#section-4.2)\n     */\n    _isImplicitGrantCallback(params) {\n        return Boolean(params.access_token || params.error_description);\n    }\n    /**\n     * Checks if the current URL and backing storage contain parameters given by a PKCE flow\n     */\n    async _isPKCECallback(params) {\n        const currentStorageContent = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getItemAsync)(this.storage, `${this.storageKey}-code-verifier`);\n        return !!(params.code && currentStorageContent);\n    }\n    /**\n     * Inside a browser context, `signOut()` will remove the logged in user from the browser session and log them out - removing all items from localstorage and then trigger a `\"SIGNED_OUT\"` event.\n     *\n     * For server-side management, you can revoke all refresh tokens for a user by passing a user's JWT through to `auth.api.signOut(JWT: string)`.\n     * There is no way to revoke a user's access token jwt until it expires. It is recommended to set a shorter expiry on the jwt for this reason.\n     *\n     * If using `others` scope, no `SIGNED_OUT` event is fired!\n     */\n    async signOut(options = { scope: 'global' }) {\n        await this.initializePromise;\n        return await this._acquireLock(-1, async () => {\n            return await this._signOut(options);\n        });\n    }\n    async _signOut({ scope } = { scope: 'global' }) {\n        return await this._useSession(async (result) => {\n            var _a;\n            const { data, error: sessionError } = result;\n            if (sessionError) {\n                return { error: sessionError };\n            }\n            const accessToken = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token;\n            if (accessToken) {\n                const { error } = await this.admin.signOut(accessToken, scope);\n                if (error) {\n                    // ignore 404s since user might not exist anymore\n                    // ignore 401s since an invalid or expired JWT should sign out the current session\n                    if (!((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthApiError)(error) &&\n                        (error.status === 404 || error.status === 401 || error.status === 403))) {\n                        return { error };\n                    }\n                }\n            }\n            if (scope !== 'others') {\n                await this._removeSession();\n                await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.removeItemAsync)(this.storage, `${this.storageKey}-code-verifier`);\n            }\n            return { error: null };\n        });\n    }\n    /**\n     * Receive a notification every time an auth event happens.\n     * @param callback A callback function to be invoked when an auth event happens.\n     */\n    onAuthStateChange(callback) {\n        const id = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.uuid)();\n        const subscription = {\n            id,\n            callback,\n            unsubscribe: () => {\n                this._debug('#unsubscribe()', 'state change callback with id removed', id);\n                this.stateChangeEmitters.delete(id);\n            },\n        };\n        this._debug('#onAuthStateChange()', 'registered callback with id', id);\n        this.stateChangeEmitters.set(id, subscription);\n        (async () => {\n            await this.initializePromise;\n            await this._acquireLock(-1, async () => {\n                this._emitInitialSession(id);\n            });\n        })();\n        return { data: { subscription } };\n    }\n    async _emitInitialSession(id) {\n        return await this._useSession(async (result) => {\n            var _a, _b;\n            try {\n                const { data: { session }, error, } = result;\n                if (error)\n                    throw error;\n                await ((_a = this.stateChangeEmitters.get(id)) === null || _a === void 0 ? void 0 : _a.callback('INITIAL_SESSION', session));\n                this._debug('INITIAL_SESSION', 'callback id', id, 'session', session);\n            }\n            catch (err) {\n                await ((_b = this.stateChangeEmitters.get(id)) === null || _b === void 0 ? void 0 : _b.callback('INITIAL_SESSION', null));\n                this._debug('INITIAL_SESSION', 'callback id', id, 'error', err);\n                console.error(err);\n            }\n        });\n    }\n    /**\n     * Sends a password reset request to an email address. This method supports the PKCE flow.\n     *\n     * @param email The email address of the user.\n     * @param options.redirectTo The URL to send the user to after they click the password reset link.\n     * @param options.captchaToken Verification token received when the user completes the captcha on the site.\n     */\n    async resetPasswordForEmail(email, options = {}) {\n        let codeChallenge = null;\n        let codeChallengeMethod = null;\n        if (this.flowType === 'pkce') {\n            ;\n            [codeChallenge, codeChallengeMethod] = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getCodeChallengeAndMethod)(this.storage, this.storageKey, true // isPasswordRecovery\n            );\n        }\n        try {\n            return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/recover`, {\n                body: {\n                    email,\n                    code_challenge: codeChallenge,\n                    code_challenge_method: codeChallengeMethod,\n                    gotrue_meta_security: { captcha_token: options.captchaToken },\n                },\n                headers: this.headers,\n                redirectTo: options.redirectTo,\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Gets all the identities linked to a user.\n     */\n    async getUserIdentities() {\n        var _a;\n        try {\n            const { data, error } = await this.getUser();\n            if (error)\n                throw error;\n            return { data: { identities: (_a = data.user.identities) !== null && _a !== void 0 ? _a : [] }, error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Links an oauth identity to an existing user.\n     * This method supports the PKCE flow.\n     */\n    async linkIdentity(credentials) {\n        var _a;\n        try {\n            const { data, error } = await this._useSession(async (result) => {\n                var _a, _b, _c, _d, _e;\n                const { data, error } = result;\n                if (error)\n                    throw error;\n                const url = await this._getUrlForProvider(`${this.url}/user/identities/authorize`, credentials.provider, {\n                    redirectTo: (_a = credentials.options) === null || _a === void 0 ? void 0 : _a.redirectTo,\n                    scopes: (_b = credentials.options) === null || _b === void 0 ? void 0 : _b.scopes,\n                    queryParams: (_c = credentials.options) === null || _c === void 0 ? void 0 : _c.queryParams,\n                    skipBrowserRedirect: true,\n                });\n                return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'GET', url, {\n                    headers: this.headers,\n                    jwt: (_e = (_d = data.session) === null || _d === void 0 ? void 0 : _d.access_token) !== null && _e !== void 0 ? _e : undefined,\n                });\n            });\n            if (error)\n                throw error;\n            if ((0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)() && !((_a = credentials.options) === null || _a === void 0 ? void 0 : _a.skipBrowserRedirect)) {\n                window.location.assign(data === null || data === void 0 ? void 0 : data.url);\n            }\n            return { data: { provider: credentials.provider, url: data === null || data === void 0 ? void 0 : data.url }, error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { provider: credentials.provider, url: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Unlinks an identity from a user by deleting it. The user will no longer be able to sign in with that identity once it's unlinked.\n     */\n    async unlinkIdentity(identity) {\n        try {\n            return await this._useSession(async (result) => {\n                var _a, _b;\n                const { data, error } = result;\n                if (error) {\n                    throw error;\n                }\n                return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'DELETE', `${this.url}/user/identities/${identity.identity_id}`, {\n                    headers: this.headers,\n                    jwt: (_b = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token) !== null && _b !== void 0 ? _b : undefined,\n                });\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Generates a new JWT.\n     * @param refreshToken A valid refresh token that was returned on login.\n     */\n    async _refreshAccessToken(refreshToken) {\n        const debugName = `#_refreshAccessToken(${refreshToken.substring(0, 5)}...)`;\n        this._debug(debugName, 'begin');\n        try {\n            const startedAt = Date.now();\n            // will attempt to refresh the token with exponential backoff\n            return await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.retryable)(async (attempt) => {\n                if (attempt > 0) {\n                    await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.sleep)(200 * Math.pow(2, attempt - 1)); // 200, 400, 800, ...\n                }\n                this._debug(debugName, 'refreshing attempt', attempt);\n                return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/token?grant_type=refresh_token`, {\n                    body: { refresh_token: refreshToken },\n                    headers: this.headers,\n                    xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponse,\n                });\n            }, (attempt, error) => {\n                const nextBackOffInterval = 200 * Math.pow(2, attempt);\n                return (error &&\n                    (0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthRetryableFetchError)(error) &&\n                    // retryable only if the request can be sent before the backoff overflows the tick duration\n                    Date.now() + nextBackOffInterval - startedAt < _lib_constants__WEBPACK_IMPORTED_MODULE_1__.AUTO_REFRESH_TICK_DURATION_MS);\n            });\n        }\n        catch (error) {\n            this._debug(debugName, 'error', error);\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { session: null, user: null }, error };\n            }\n            throw error;\n        }\n        finally {\n            this._debug(debugName, 'end');\n        }\n    }\n    _isValidSession(maybeSession) {\n        const isValidSession = typeof maybeSession === 'object' &&\n            maybeSession !== null &&\n            'access_token' in maybeSession &&\n            'refresh_token' in maybeSession &&\n            'expires_at' in maybeSession;\n        return isValidSession;\n    }\n    async _handleProviderSignIn(provider, options) {\n        const url = await this._getUrlForProvider(`${this.url}/authorize`, provider, {\n            redirectTo: options.redirectTo,\n            scopes: options.scopes,\n            queryParams: options.queryParams,\n        });\n        this._debug('#_handleProviderSignIn()', 'provider', provider, 'options', options, 'url', url);\n        // try to open on the browser\n        if ((0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)() && !options.skipBrowserRedirect) {\n            window.location.assign(url);\n        }\n        return { data: { provider, url }, error: null };\n    }\n    /**\n     * Recovers the session from LocalStorage and refreshes the token\n     * Note: this method is async to accommodate for AsyncStorage e.g. in React native.\n     */\n    async _recoverAndRefresh() {\n        var _a;\n        const debugName = '#_recoverAndRefresh()';\n        this._debug(debugName, 'begin');\n        try {\n            const currentSession = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getItemAsync)(this.storage, this.storageKey);\n            this._debug(debugName, 'session from storage', currentSession);\n            if (!this._isValidSession(currentSession)) {\n                this._debug(debugName, 'session is not valid');\n                if (currentSession !== null) {\n                    await this._removeSession();\n                }\n                return;\n            }\n            const expiresWithMargin = ((_a = currentSession.expires_at) !== null && _a !== void 0 ? _a : Infinity) * 1000 - Date.now() < _lib_constants__WEBPACK_IMPORTED_MODULE_1__.EXPIRY_MARGIN_MS;\n            this._debug(debugName, `session has${expiresWithMargin ? '' : ' not'} expired with margin of ${_lib_constants__WEBPACK_IMPORTED_MODULE_1__.EXPIRY_MARGIN_MS}s`);\n            if (expiresWithMargin) {\n                if (this.autoRefreshToken && currentSession.refresh_token) {\n                    const { error } = await this._callRefreshToken(currentSession.refresh_token);\n                    if (error) {\n                        console.error(error);\n                        if (!(0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthRetryableFetchError)(error)) {\n                            this._debug(debugName, 'refresh failed with a non-retryable error, removing the session', error);\n                            await this._removeSession();\n                        }\n                    }\n                }\n            }\n            else {\n                // no need to persist currentSession again, as we just loaded it from\n                // local storage; persisting it again may overwrite a value saved by\n                // another client with access to the same local storage\n                await this._notifyAllSubscribers('SIGNED_IN', currentSession);\n            }\n        }\n        catch (err) {\n            this._debug(debugName, 'error', err);\n            console.error(err);\n            return;\n        }\n        finally {\n            this._debug(debugName, 'end');\n        }\n    }\n    async _callRefreshToken(refreshToken) {\n        var _a, _b;\n        if (!refreshToken) {\n            throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthSessionMissingError();\n        }\n        // refreshing is already in progress\n        if (this.refreshingDeferred) {\n            return this.refreshingDeferred.promise;\n        }\n        const debugName = `#_callRefreshToken(${refreshToken.substring(0, 5)}...)`;\n        this._debug(debugName, 'begin');\n        try {\n            this.refreshingDeferred = new _lib_helpers__WEBPACK_IMPORTED_MODULE_4__.Deferred();\n            const { data, error } = await this._refreshAccessToken(refreshToken);\n            if (error)\n                throw error;\n            if (!data.session)\n                throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthSessionMissingError();\n            await this._saveSession(data.session);\n            await this._notifyAllSubscribers('TOKEN_REFRESHED', data.session);\n            const result = { session: data.session, error: null };\n            this.refreshingDeferred.resolve(result);\n            return result;\n        }\n        catch (error) {\n            this._debug(debugName, 'error', error);\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                const result = { session: null, error };\n                if (!(0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthRetryableFetchError)(error)) {\n                    await this._removeSession();\n                }\n                (_a = this.refreshingDeferred) === null || _a === void 0 ? void 0 : _a.resolve(result);\n                return result;\n            }\n            (_b = this.refreshingDeferred) === null || _b === void 0 ? void 0 : _b.reject(error);\n            throw error;\n        }\n        finally {\n            this.refreshingDeferred = null;\n            this._debug(debugName, 'end');\n        }\n    }\n    async _notifyAllSubscribers(event, session, broadcast = true) {\n        const debugName = `#_notifyAllSubscribers(${event})`;\n        this._debug(debugName, 'begin', session, `broadcast = ${broadcast}`);\n        try {\n            if (this.broadcastChannel && broadcast) {\n                this.broadcastChannel.postMessage({ event, session });\n            }\n            const errors = [];\n            const promises = Array.from(this.stateChangeEmitters.values()).map(async (x) => {\n                try {\n                    await x.callback(event, session);\n                }\n                catch (e) {\n                    errors.push(e);\n                }\n            });\n            await Promise.all(promises);\n            if (errors.length > 0) {\n                for (let i = 0; i < errors.length; i += 1) {\n                    console.error(errors[i]);\n                }\n                throw errors[0];\n            }\n        }\n        finally {\n            this._debug(debugName, 'end');\n        }\n    }\n    /**\n     * set currentSession and currentUser\n     * process to _startAutoRefreshToken if possible\n     */\n    async _saveSession(session) {\n        this._debug('#_saveSession()', session);\n        // _saveSession is always called whenever a new session has been acquired\n        // so we can safely suppress the warning returned by future getSession calls\n        this.suppressGetSessionWarning = true;\n        await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.setItemAsync)(this.storage, this.storageKey, session);\n    }\n    async _removeSession() {\n        this._debug('#_removeSession()');\n        await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.removeItemAsync)(this.storage, this.storageKey);\n        await this._notifyAllSubscribers('SIGNED_OUT', null);\n    }\n    /**\n     * Removes any registered visibilitychange callback.\n     *\n     * {@see #startAutoRefresh}\n     * {@see #stopAutoRefresh}\n     */\n    _removeVisibilityChangedCallback() {\n        this._debug('#_removeVisibilityChangedCallback()');\n        const callback = this.visibilityChangedCallback;\n        this.visibilityChangedCallback = null;\n        try {\n            if (callback && (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)() && (window === null || window === void 0 ? void 0 : window.removeEventListener)) {\n                window.removeEventListener('visibilitychange', callback);\n            }\n        }\n        catch (e) {\n            console.error('removing visibilitychange callback failed', e);\n        }\n    }\n    /**\n     * This is the private implementation of {@link #startAutoRefresh}. Use this\n     * within the library.\n     */\n    async _startAutoRefresh() {\n        await this._stopAutoRefresh();\n        this._debug('#_startAutoRefresh()');\n        const ticker = setInterval(() => this._autoRefreshTokenTick(), _lib_constants__WEBPACK_IMPORTED_MODULE_1__.AUTO_REFRESH_TICK_DURATION_MS);\n        this.autoRefreshTicker = ticker;\n        if (ticker && typeof ticker === 'object' && typeof ticker.unref === 'function') {\n            // ticker is a NodeJS Timeout object that has an `unref` method\n            // https://nodejs.org/api/timers.html#timeoutunref\n            // When auto refresh is used in NodeJS (like for testing) the\n            // `setInterval` is preventing the process from being marked as\n            // finished and tests run endlessly. This can be prevented by calling\n            // `unref()` on the returned object.\n            ticker.unref();\n            // @ts-expect-error TS has no context of Deno\n        }\n        else if (typeof Deno !== 'undefined' && typeof Deno.unrefTimer === 'function') {\n            // similar like for NodeJS, but with the Deno API\n            // https://deno.land/api@latest?unstable&s=Deno.unrefTimer\n            // @ts-expect-error TS has no context of Deno\n            Deno.unrefTimer(ticker);\n        }\n        // run the tick immediately, but in the next pass of the event loop so that\n        // #_initialize can be allowed to complete without recursively waiting on\n        // itself\n        setTimeout(async () => {\n            await this.initializePromise;\n            await this._autoRefreshTokenTick();\n        }, 0);\n    }\n    /**\n     * This is the private implementation of {@link #stopAutoRefresh}. Use this\n     * within the library.\n     */\n    async _stopAutoRefresh() {\n        this._debug('#_stopAutoRefresh()');\n        const ticker = this.autoRefreshTicker;\n        this.autoRefreshTicker = null;\n        if (ticker) {\n            clearInterval(ticker);\n        }\n    }\n    /**\n     * Starts an auto-refresh process in the background. The session is checked\n     * every few seconds. Close to the time of expiration a process is started to\n     * refresh the session. If refreshing fails it will be retried for as long as\n     * necessary.\n     *\n     * If you set the {@link GoTrueClientOptions#autoRefreshToken} you don't need\n     * to call this function, it will be called for you.\n     *\n     * On browsers the refresh process works only when the tab/window is in the\n     * foreground to conserve resources as well as prevent race conditions and\n     * flooding auth with requests. If you call this method any managed\n     * visibility change callback will be removed and you must manage visibility\n     * changes on your own.\n     *\n     * On non-browser platforms the refresh process works *continuously* in the\n     * background, which may not be desirable. You should hook into your\n     * platform's foreground indication mechanism and call these methods\n     * appropriately to conserve resources.\n     *\n     * {@see #stopAutoRefresh}\n     */\n    async startAutoRefresh() {\n        this._removeVisibilityChangedCallback();\n        await this._startAutoRefresh();\n    }\n    /**\n     * Stops an active auto refresh process running in the background (if any).\n     *\n     * If you call this method any managed visibility change callback will be\n     * removed and you must manage visibility changes on your own.\n     *\n     * See {@link #startAutoRefresh} for more details.\n     */\n    async stopAutoRefresh() {\n        this._removeVisibilityChangedCallback();\n        await this._stopAutoRefresh();\n    }\n    /**\n     * Runs the auto refresh token tick.\n     */\n    async _autoRefreshTokenTick() {\n        this._debug('#_autoRefreshTokenTick()', 'begin');\n        try {\n            await this._acquireLock(0, async () => {\n                try {\n                    const now = Date.now();\n                    try {\n                        return await this._useSession(async (result) => {\n                            const { data: { session }, } = result;\n                            if (!session || !session.refresh_token || !session.expires_at) {\n                                this._debug('#_autoRefreshTokenTick()', 'no session');\n                                return;\n                            }\n                            // session will expire in this many ticks (or has already expired if <= 0)\n                            const expiresInTicks = Math.floor((session.expires_at * 1000 - now) / _lib_constants__WEBPACK_IMPORTED_MODULE_1__.AUTO_REFRESH_TICK_DURATION_MS);\n                            this._debug('#_autoRefreshTokenTick()', `access token expires in ${expiresInTicks} ticks, a tick lasts ${_lib_constants__WEBPACK_IMPORTED_MODULE_1__.AUTO_REFRESH_TICK_DURATION_MS}ms, refresh threshold is ${_lib_constants__WEBPACK_IMPORTED_MODULE_1__.AUTO_REFRESH_TICK_THRESHOLD} ticks`);\n                            if (expiresInTicks <= _lib_constants__WEBPACK_IMPORTED_MODULE_1__.AUTO_REFRESH_TICK_THRESHOLD) {\n                                await this._callRefreshToken(session.refresh_token);\n                            }\n                        });\n                    }\n                    catch (e) {\n                        console.error('Auto refresh tick failed with error. This is likely a transient error.', e);\n                    }\n                }\n                finally {\n                    this._debug('#_autoRefreshTokenTick()', 'end');\n                }\n            });\n        }\n        catch (e) {\n            if (e.isAcquireTimeout || e instanceof _lib_locks__WEBPACK_IMPORTED_MODULE_8__.LockAcquireTimeoutError) {\n                this._debug('auto refresh token tick lock not available');\n            }\n            else {\n                throw e;\n            }\n        }\n    }\n    /**\n     * Registers callbacks on the browser / platform, which in-turn run\n     * algorithms when the browser window/tab are in foreground. On non-browser\n     * platforms it assumes always foreground.\n     */\n    async _handleVisibilityChange() {\n        this._debug('#_handleVisibilityChange()');\n        if (!(0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)() || !(window === null || window === void 0 ? void 0 : window.addEventListener)) {\n            if (this.autoRefreshToken) {\n                // in non-browser environments the refresh token ticker runs always\n                this.startAutoRefresh();\n            }\n            return false;\n        }\n        try {\n            this.visibilityChangedCallback = async () => await this._onVisibilityChanged(false);\n            window === null || window === void 0 ? void 0 : window.addEventListener('visibilitychange', this.visibilityChangedCallback);\n            // now immediately call the visbility changed callback to setup with the\n            // current visbility state\n            await this._onVisibilityChanged(true); // initial call\n        }\n        catch (error) {\n            console.error('_handleVisibilityChange', error);\n        }\n    }\n    /**\n     * Callback registered with `window.addEventListener('visibilitychange')`.\n     */\n    async _onVisibilityChanged(calledFromInitialize) {\n        const methodName = `#_onVisibilityChanged(${calledFromInitialize})`;\n        this._debug(methodName, 'visibilityState', document.visibilityState);\n        if (document.visibilityState === 'visible') {\n            if (this.autoRefreshToken) {\n                // in browser environments the refresh token ticker runs only on focused tabs\n                // which prevents race conditions\n                this._startAutoRefresh();\n            }\n            if (!calledFromInitialize) {\n                // called when the visibility has changed, i.e. the browser\n                // transitioned from hidden -> visible so we need to see if the session\n                // should be recovered immediately... but to do that we need to acquire\n                // the lock first asynchronously\n                await this.initializePromise;\n                await this._acquireLock(-1, async () => {\n                    if (document.visibilityState !== 'visible') {\n                        this._debug(methodName, 'acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting');\n                        // visibility has changed while waiting for the lock, abort\n                        return;\n                    }\n                    // recover the session\n                    await this._recoverAndRefresh();\n                });\n            }\n        }\n        else if (document.visibilityState === 'hidden') {\n            if (this.autoRefreshToken) {\n                this._stopAutoRefresh();\n            }\n        }\n    }\n    /**\n     * Generates the relevant login URL for a third-party provider.\n     * @param options.redirectTo A URL or mobile address to send the user to after they are confirmed.\n     * @param options.scopes A space-separated list of scopes granted to the OAuth application.\n     * @param options.queryParams An object of key-value pairs containing query parameters granted to the OAuth application.\n     */\n    async _getUrlForProvider(url, provider, options) {\n        const urlParams = [`provider=${encodeURIComponent(provider)}`];\n        if (options === null || options === void 0 ? void 0 : options.redirectTo) {\n            urlParams.push(`redirect_to=${encodeURIComponent(options.redirectTo)}`);\n        }\n        if (options === null || options === void 0 ? void 0 : options.scopes) {\n            urlParams.push(`scopes=${encodeURIComponent(options.scopes)}`);\n        }\n        if (this.flowType === 'pkce') {\n            const [codeChallenge, codeChallengeMethod] = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getCodeChallengeAndMethod)(this.storage, this.storageKey);\n            const flowParams = new URLSearchParams({\n                code_challenge: `${encodeURIComponent(codeChallenge)}`,\n                code_challenge_method: `${encodeURIComponent(codeChallengeMethod)}`,\n            });\n            urlParams.push(flowParams.toString());\n        }\n        if (options === null || options === void 0 ? void 0 : options.queryParams) {\n            const query = new URLSearchParams(options.queryParams);\n            urlParams.push(query.toString());\n        }\n        if (options === null || options === void 0 ? void 0 : options.skipBrowserRedirect) {\n            urlParams.push(`skip_http_redirect=${options.skipBrowserRedirect}`);\n        }\n        return `${url}?${urlParams.join('&')}`;\n    }\n    async _unenroll(params) {\n        try {\n            return await this._useSession(async (result) => {\n                var _a;\n                const { data: sessionData, error: sessionError } = result;\n                if (sessionError) {\n                    return { data: null, error: sessionError };\n                }\n                return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'DELETE', `${this.url}/factors/${params.factorId}`, {\n                    headers: this.headers,\n                    jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token,\n                });\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    async _enroll(params) {\n        try {\n            return await this._useSession(async (result) => {\n                var _a, _b;\n                const { data: sessionData, error: sessionError } = result;\n                if (sessionError) {\n                    return { data: null, error: sessionError };\n                }\n                const body = Object.assign({ friendly_name: params.friendlyName, factor_type: params.factorType }, (params.factorType === 'phone' ? { phone: params.phone } : { issuer: params.issuer }));\n                const { data, error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/factors`, {\n                    body,\n                    headers: this.headers,\n                    jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token,\n                });\n                if (error) {\n                    return { data: null, error };\n                }\n                if (params.factorType === 'totp' && ((_b = data === null || data === void 0 ? void 0 : data.totp) === null || _b === void 0 ? void 0 : _b.qr_code)) {\n                    data.totp.qr_code = `data:image/svg+xml;utf-8,${data.totp.qr_code}`;\n                }\n                return { data, error: null };\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * {@see GoTrueMFAApi#verify}\n     */\n    async _verify(params) {\n        return this._acquireLock(-1, async () => {\n            try {\n                return await this._useSession(async (result) => {\n                    var _a;\n                    const { data: sessionData, error: sessionError } = result;\n                    if (sessionError) {\n                        return { data: null, error: sessionError };\n                    }\n                    const { data, error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/factors/${params.factorId}/verify`, {\n                        body: { code: params.code, challenge_id: params.challengeId },\n                        headers: this.headers,\n                        jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token,\n                    });\n                    if (error) {\n                        return { data: null, error };\n                    }\n                    await this._saveSession(Object.assign({ expires_at: Math.round(Date.now() / 1000) + data.expires_in }, data));\n                    await this._notifyAllSubscribers('MFA_CHALLENGE_VERIFIED', data);\n                    return { data, error };\n                });\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * {@see GoTrueMFAApi#challenge}\n     */\n    async _challenge(params) {\n        return this._acquireLock(-1, async () => {\n            try {\n                return await this._useSession(async (result) => {\n                    var _a;\n                    const { data: sessionData, error: sessionError } = result;\n                    if (sessionError) {\n                        return { data: null, error: sessionError };\n                    }\n                    return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/factors/${params.factorId}/challenge`, {\n                        body: { channel: params.channel },\n                        headers: this.headers,\n                        jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token,\n                    });\n                });\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * {@see GoTrueMFAApi#challengeAndVerify}\n     */\n    async _challengeAndVerify(params) {\n        // both _challenge and _verify independently acquire the lock, so no need\n        // to acquire it here\n        const { data: challengeData, error: challengeError } = await this._challenge({\n            factorId: params.factorId,\n        });\n        if (challengeError) {\n            return { data: null, error: challengeError };\n        }\n        return await this._verify({\n            factorId: params.factorId,\n            challengeId: challengeData.id,\n            code: params.code,\n        });\n    }\n    /**\n     * {@see GoTrueMFAApi#listFactors}\n     */\n    async _listFactors() {\n        // use #getUser instead of #_getUser as the former acquires a lock\n        const { data: { user }, error: userError, } = await this.getUser();\n        if (userError) {\n            return { data: null, error: userError };\n        }\n        const factors = (user === null || user === void 0 ? void 0 : user.factors) || [];\n        const totp = factors.filter((factor) => factor.factor_type === 'totp' && factor.status === 'verified');\n        const phone = factors.filter((factor) => factor.factor_type === 'phone' && factor.status === 'verified');\n        return {\n            data: {\n                all: factors,\n                totp,\n                phone,\n            },\n            error: null,\n        };\n    }\n    /**\n     * {@see GoTrueMFAApi#getAuthenticatorAssuranceLevel}\n     */\n    async _getAuthenticatorAssuranceLevel() {\n        return this._acquireLock(-1, async () => {\n            return await this._useSession(async (result) => {\n                var _a, _b;\n                const { data: { session }, error: sessionError, } = result;\n                if (sessionError) {\n                    return { data: null, error: sessionError };\n                }\n                if (!session) {\n                    return {\n                        data: { currentLevel: null, nextLevel: null, currentAuthenticationMethods: [] },\n                        error: null,\n                    };\n                }\n                const { payload } = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.decodeJWT)(session.access_token);\n                let currentLevel = null;\n                if (payload.aal) {\n                    currentLevel = payload.aal;\n                }\n                let nextLevel = currentLevel;\n                const verifiedFactors = (_b = (_a = session.user.factors) === null || _a === void 0 ? void 0 : _a.filter((factor) => factor.status === 'verified')) !== null && _b !== void 0 ? _b : [];\n                if (verifiedFactors.length > 0) {\n                    nextLevel = 'aal2';\n                }\n                const currentAuthenticationMethods = payload.amr || [];\n                return { data: { currentLevel, nextLevel, currentAuthenticationMethods }, error: null };\n            });\n        });\n    }\n    async fetchJwk(kid, jwks = { keys: [] }) {\n        // try fetching from the supplied jwks\n        let jwk = jwks.keys.find((key) => key.kid === kid);\n        if (jwk) {\n            return jwk;\n        }\n        // try fetching from cache\n        jwk = this.jwks.keys.find((key) => key.kid === kid);\n        // jwk exists and jwks isn't stale\n        if (jwk && this.jwks_cached_at + _lib_constants__WEBPACK_IMPORTED_MODULE_1__.JWKS_TTL > Date.now()) {\n            return jwk;\n        }\n        // jwk isn't cached in memory so we need to fetch it from the well-known endpoint\n        const { data, error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'GET', `${this.url}/.well-known/jwks.json`, {\n            headers: this.headers,\n        });\n        if (error) {\n            throw error;\n        }\n        if (!data.keys || data.keys.length === 0) {\n            throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidJwtError('JWKS is empty');\n        }\n        this.jwks = data;\n        this.jwks_cached_at = Date.now();\n        // Find the signing key\n        jwk = data.keys.find((key) => key.kid === kid);\n        if (!jwk) {\n            throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidJwtError('No matching signing key found in JWKS');\n        }\n        return jwk;\n    }\n    /**\n     * @experimental This method may change in future versions.\n     * @description Gets the claims from a JWT. If the JWT is symmetric JWTs, it will call getUser() to verify against the server. If the JWT is asymmetric, it will be verified against the JWKS using the WebCrypto API.\n     */\n    async getClaims(jwt, jwks = { keys: [] }) {\n        try {\n            let token = jwt;\n            if (!token) {\n                const { data, error } = await this.getSession();\n                if (error || !data.session) {\n                    return { data: null, error };\n                }\n                token = data.session.access_token;\n            }\n            const { header, payload, signature, raw: { header: rawHeader, payload: rawPayload }, } = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.decodeJWT)(token);\n            // Reject expired JWTs\n            (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.validateExp)(payload.exp);\n            // If symmetric algorithm or WebCrypto API is unavailable, fallback to getUser()\n            if (!header.kid ||\n                header.alg === 'HS256' ||\n                !('crypto' in globalThis && 'subtle' in globalThis.crypto)) {\n                const { error } = await this.getUser(token);\n                if (error) {\n                    throw error;\n                }\n                // getUser succeeds so the claims in the JWT can be trusted\n                return {\n                    data: {\n                        claims: payload,\n                        header,\n                        signature,\n                    },\n                    error: null,\n                };\n            }\n            const algorithm = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getAlgorithm)(header.alg);\n            const signingKey = await this.fetchJwk(header.kid, jwks);\n            // Convert JWK to CryptoKey\n            const publicKey = await crypto.subtle.importKey('jwk', signingKey, algorithm, true, [\n                'verify',\n            ]);\n            // Verify the signature\n            const isValid = await crypto.subtle.verify(algorithm, publicKey, signature, (0,_lib_base64url__WEBPACK_IMPORTED_MODULE_9__.stringToUint8Array)(`${rawHeader}.${rawPayload}`));\n            if (!isValid) {\n                throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidJwtError('Invalid JWT signature');\n            }\n            // If verification succeeds, decode and return claims\n            return {\n                data: {\n                    claims: payload,\n                    header,\n                    signature,\n                },\n                error: null,\n            };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n}\nGoTrueClient.nextInstanceID = 0;\n//# sourceMappingURL=GoTrueClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/GoTrueClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/index.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/index.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthAdminApi: () => (/* reexport safe */ _AuthAdminApi__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   AuthApiError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthApiError),\n/* harmony export */   AuthClient: () => (/* reexport safe */ _AuthClient__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   AuthError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthError),\n/* harmony export */   AuthImplicitGrantRedirectError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthImplicitGrantRedirectError),\n/* harmony export */   AuthInvalidCredentialsError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthInvalidCredentialsError),\n/* harmony export */   AuthInvalidJwtError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthInvalidJwtError),\n/* harmony export */   AuthInvalidTokenResponseError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthInvalidTokenResponseError),\n/* harmony export */   AuthPKCEGrantCodeExchangeError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthPKCEGrantCodeExchangeError),\n/* harmony export */   AuthRetryableFetchError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthRetryableFetchError),\n/* harmony export */   AuthSessionMissingError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthSessionMissingError),\n/* harmony export */   AuthUnknownError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthUnknownError),\n/* harmony export */   AuthWeakPasswordError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthWeakPasswordError),\n/* harmony export */   CustomAuthError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.CustomAuthError),\n/* harmony export */   GoTrueAdminApi: () => (/* reexport safe */ _GoTrueAdminApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   GoTrueClient: () => (/* reexport safe */ _GoTrueClient__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   NavigatorLockAcquireTimeoutError: () => (/* reexport safe */ _lib_locks__WEBPACK_IMPORTED_MODULE_6__.NavigatorLockAcquireTimeoutError),\n/* harmony export */   SIGN_OUT_SCOPES: () => (/* reexport safe */ _lib_types__WEBPACK_IMPORTED_MODULE_4__.SIGN_OUT_SCOPES),\n/* harmony export */   isAuthApiError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.isAuthApiError),\n/* harmony export */   isAuthError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.isAuthError),\n/* harmony export */   isAuthImplicitGrantRedirectError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.isAuthImplicitGrantRedirectError),\n/* harmony export */   isAuthRetryableFetchError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.isAuthRetryableFetchError),\n/* harmony export */   isAuthSessionMissingError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.isAuthSessionMissingError),\n/* harmony export */   isAuthWeakPasswordError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.isAuthWeakPasswordError),\n/* harmony export */   lockInternals: () => (/* reexport safe */ _lib_locks__WEBPACK_IMPORTED_MODULE_6__.internals),\n/* harmony export */   navigatorLock: () => (/* reexport safe */ _lib_locks__WEBPACK_IMPORTED_MODULE_6__.navigatorLock),\n/* harmony export */   processLock: () => (/* reexport safe */ _lib_locks__WEBPACK_IMPORTED_MODULE_6__.processLock)\n/* harmony export */ });\n/* harmony import */ var _GoTrueAdminApi__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./GoTrueAdminApi */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js\");\n/* harmony import */ var _GoTrueClient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./GoTrueClient */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/GoTrueClient.js\");\n/* harmony import */ var _AuthAdminApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthAdminApi */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/AuthAdminApi.js\");\n/* harmony import */ var _AuthClient__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthClient */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/AuthClient.js\");\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/types */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/types.js\");\n/* harmony import */ var _lib_errors__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./lib/errors */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/errors.js\");\n/* harmony import */ var _lib_locks__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./lib/locks */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/locks.js\");\n\n\n\n\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK2F1dGgtanNAMi43MC4wL25vZGVfbW9kdWxlcy9Ac3VwYWJhc2UvYXV0aC1qcy9kaXN0L21vZHVsZS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQThDO0FBQ0o7QUFDQTtBQUNKO0FBQzRCO0FBQ3RDO0FBQ0M7QUFDMkY7QUFDeEgiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbWVtZXRcXERlc2t0b3BcXGRvcm1fMjFcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZSthdXRoLWpzQDIuNzAuMFxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXGF1dGgtanNcXGRpc3RcXG1vZHVsZVxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IEdvVHJ1ZUFkbWluQXBpIGZyb20gJy4vR29UcnVlQWRtaW5BcGknO1xuaW1wb3J0IEdvVHJ1ZUNsaWVudCBmcm9tICcuL0dvVHJ1ZUNsaWVudCc7XG5pbXBvcnQgQXV0aEFkbWluQXBpIGZyb20gJy4vQXV0aEFkbWluQXBpJztcbmltcG9ydCBBdXRoQ2xpZW50IGZyb20gJy4vQXV0aENsaWVudCc7XG5leHBvcnQgeyBHb1RydWVBZG1pbkFwaSwgR29UcnVlQ2xpZW50LCBBdXRoQWRtaW5BcGksIEF1dGhDbGllbnQgfTtcbmV4cG9ydCAqIGZyb20gJy4vbGliL3R5cGVzJztcbmV4cG9ydCAqIGZyb20gJy4vbGliL2Vycm9ycyc7XG5leHBvcnQgeyBuYXZpZ2F0b3JMb2NrLCBOYXZpZ2F0b3JMb2NrQWNxdWlyZVRpbWVvdXRFcnJvciwgaW50ZXJuYWxzIGFzIGxvY2tJbnRlcm5hbHMsIHByb2Nlc3NMb2NrLCB9IGZyb20gJy4vbGliL2xvY2tzJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/base64url.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/base64url.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   base64UrlToUint8Array: () => (/* binding */ base64UrlToUint8Array),\n/* harmony export */   byteFromBase64URL: () => (/* binding */ byteFromBase64URL),\n/* harmony export */   byteToBase64URL: () => (/* binding */ byteToBase64URL),\n/* harmony export */   bytesToBase64URL: () => (/* binding */ bytesToBase64URL),\n/* harmony export */   codepointToUTF8: () => (/* binding */ codepointToUTF8),\n/* harmony export */   stringFromBase64URL: () => (/* binding */ stringFromBase64URL),\n/* harmony export */   stringFromUTF8: () => (/* binding */ stringFromUTF8),\n/* harmony export */   stringToBase64URL: () => (/* binding */ stringToBase64URL),\n/* harmony export */   stringToUTF8: () => (/* binding */ stringToUTF8),\n/* harmony export */   stringToUint8Array: () => (/* binding */ stringToUint8Array)\n/* harmony export */ });\n/**\n * Avoid modifying this file. It's part of\n * https://github.com/supabase-community/base64url-js.  Submit all fixes on\n * that repo!\n */\n/**\n * An array of characters that encode 6 bits into a Base64-URL alphabet\n * character.\n */\nconst TO_BASE64URL = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_'.split('');\n/**\n * An array of characters that can appear in a Base64-URL encoded string but\n * should be ignored.\n */\nconst IGNORE_BASE64URL = ' \\t\\n\\r='.split('');\n/**\n * An array of 128 numbers that map a Base64-URL character to 6 bits, or if -2\n * used to skip the character, or if -1 used to error out.\n */\nconst FROM_BASE64URL = (() => {\n    const charMap = new Array(128);\n    for (let i = 0; i < charMap.length; i += 1) {\n        charMap[i] = -1;\n    }\n    for (let i = 0; i < IGNORE_BASE64URL.length; i += 1) {\n        charMap[IGNORE_BASE64URL[i].charCodeAt(0)] = -2;\n    }\n    for (let i = 0; i < TO_BASE64URL.length; i += 1) {\n        charMap[TO_BASE64URL[i].charCodeAt(0)] = i;\n    }\n    return charMap;\n})();\n/**\n * Converts a byte to a Base64-URL string.\n *\n * @param byte The byte to convert, or null to flush at the end of the byte sequence.\n * @param state The Base64 conversion state. Pass an initial value of `{ queue: 0, queuedBits: 0 }`.\n * @param emit A function called with the next Base64 character when ready.\n */\nfunction byteToBase64URL(byte, state, emit) {\n    if (byte !== null) {\n        state.queue = (state.queue << 8) | byte;\n        state.queuedBits += 8;\n        while (state.queuedBits >= 6) {\n            const pos = (state.queue >> (state.queuedBits - 6)) & 63;\n            emit(TO_BASE64URL[pos]);\n            state.queuedBits -= 6;\n        }\n    }\n    else if (state.queuedBits > 0) {\n        state.queue = state.queue << (6 - state.queuedBits);\n        state.queuedBits = 6;\n        while (state.queuedBits >= 6) {\n            const pos = (state.queue >> (state.queuedBits - 6)) & 63;\n            emit(TO_BASE64URL[pos]);\n            state.queuedBits -= 6;\n        }\n    }\n}\n/**\n * Converts a String char code (extracted using `string.charCodeAt(position)`) to a sequence of Base64-URL characters.\n *\n * @param charCode The char code of the JavaScript string.\n * @param state The Base64 state. Pass an initial value of `{ queue: 0, queuedBits: 0 }`.\n * @param emit A function called with the next byte.\n */\nfunction byteFromBase64URL(charCode, state, emit) {\n    const bits = FROM_BASE64URL[charCode];\n    if (bits > -1) {\n        // valid Base64-URL character\n        state.queue = (state.queue << 6) | bits;\n        state.queuedBits += 6;\n        while (state.queuedBits >= 8) {\n            emit((state.queue >> (state.queuedBits - 8)) & 0xff);\n            state.queuedBits -= 8;\n        }\n    }\n    else if (bits === -2) {\n        // ignore spaces, tabs, newlines, =\n        return;\n    }\n    else {\n        throw new Error(`Invalid Base64-URL character \"${String.fromCharCode(charCode)}\"`);\n    }\n}\n/**\n * Converts a JavaScript string (which may include any valid character) into a\n * Base64-URL encoded string. The string is first encoded in UTF-8 which is\n * then encoded as Base64-URL.\n *\n * @param str The string to convert.\n */\nfunction stringToBase64URL(str) {\n    const base64 = [];\n    const emitter = (char) => {\n        base64.push(char);\n    };\n    const state = { queue: 0, queuedBits: 0 };\n    stringToUTF8(str, (byte) => {\n        byteToBase64URL(byte, state, emitter);\n    });\n    byteToBase64URL(null, state, emitter);\n    return base64.join('');\n}\n/**\n * Converts a Base64-URL encoded string into a JavaScript string. It is assumed\n * that the underlying string has been encoded as UTF-8.\n *\n * @param str The Base64-URL encoded string.\n */\nfunction stringFromBase64URL(str) {\n    const conv = [];\n    const utf8Emit = (codepoint) => {\n        conv.push(String.fromCodePoint(codepoint));\n    };\n    const utf8State = {\n        utf8seq: 0,\n        codepoint: 0,\n    };\n    const b64State = { queue: 0, queuedBits: 0 };\n    const byteEmit = (byte) => {\n        stringFromUTF8(byte, utf8State, utf8Emit);\n    };\n    for (let i = 0; i < str.length; i += 1) {\n        byteFromBase64URL(str.charCodeAt(i), b64State, byteEmit);\n    }\n    return conv.join('');\n}\n/**\n * Converts a Unicode codepoint to a multi-byte UTF-8 sequence.\n *\n * @param codepoint The Unicode codepoint.\n * @param emit      Function which will be called for each UTF-8 byte that represents the codepoint.\n */\nfunction codepointToUTF8(codepoint, emit) {\n    if (codepoint <= 0x7f) {\n        emit(codepoint);\n        return;\n    }\n    else if (codepoint <= 0x7ff) {\n        emit(0xc0 | (codepoint >> 6));\n        emit(0x80 | (codepoint & 0x3f));\n        return;\n    }\n    else if (codepoint <= 0xffff) {\n        emit(0xe0 | (codepoint >> 12));\n        emit(0x80 | ((codepoint >> 6) & 0x3f));\n        emit(0x80 | (codepoint & 0x3f));\n        return;\n    }\n    else if (codepoint <= 0x10ffff) {\n        emit(0xf0 | (codepoint >> 18));\n        emit(0x80 | ((codepoint >> 12) & 0x3f));\n        emit(0x80 | ((codepoint >> 6) & 0x3f));\n        emit(0x80 | (codepoint & 0x3f));\n        return;\n    }\n    throw new Error(`Unrecognized Unicode codepoint: ${codepoint.toString(16)}`);\n}\n/**\n * Converts a JavaScript string to a sequence of UTF-8 bytes.\n *\n * @param str  The string to convert to UTF-8.\n * @param emit Function which will be called for each UTF-8 byte of the string.\n */\nfunction stringToUTF8(str, emit) {\n    for (let i = 0; i < str.length; i += 1) {\n        let codepoint = str.charCodeAt(i);\n        if (codepoint > 0xd7ff && codepoint <= 0xdbff) {\n            // most UTF-16 codepoints are Unicode codepoints, except values in this\n            // range where the next UTF-16 codepoint needs to be combined with the\n            // current one to get the Unicode codepoint\n            const highSurrogate = ((codepoint - 0xd800) * 0x400) & 0xffff;\n            const lowSurrogate = (str.charCodeAt(i + 1) - 0xdc00) & 0xffff;\n            codepoint = (lowSurrogate | highSurrogate) + 0x10000;\n            i += 1;\n        }\n        codepointToUTF8(codepoint, emit);\n    }\n}\n/**\n * Converts a UTF-8 byte to a Unicode codepoint.\n *\n * @param byte  The UTF-8 byte next in the sequence.\n * @param state The shared state between consecutive UTF-8 bytes in the\n *              sequence, an object with the shape `{ utf8seq: 0, codepoint: 0 }`.\n * @param emit  Function which will be called for each codepoint.\n */\nfunction stringFromUTF8(byte, state, emit) {\n    if (state.utf8seq === 0) {\n        if (byte <= 0x7f) {\n            emit(byte);\n            return;\n        }\n        // count the number of 1 leading bits until you reach 0\n        for (let leadingBit = 1; leadingBit < 6; leadingBit += 1) {\n            if (((byte >> (7 - leadingBit)) & 1) === 0) {\n                state.utf8seq = leadingBit;\n                break;\n            }\n        }\n        if (state.utf8seq === 2) {\n            state.codepoint = byte & 31;\n        }\n        else if (state.utf8seq === 3) {\n            state.codepoint = byte & 15;\n        }\n        else if (state.utf8seq === 4) {\n            state.codepoint = byte & 7;\n        }\n        else {\n            throw new Error('Invalid UTF-8 sequence');\n        }\n        state.utf8seq -= 1;\n    }\n    else if (state.utf8seq > 0) {\n        if (byte <= 0x7f) {\n            throw new Error('Invalid UTF-8 sequence');\n        }\n        state.codepoint = (state.codepoint << 6) | (byte & 63);\n        state.utf8seq -= 1;\n        if (state.utf8seq === 0) {\n            emit(state.codepoint);\n        }\n    }\n}\n/**\n * Helper functions to convert different types of strings to Uint8Array\n */\nfunction base64UrlToUint8Array(str) {\n    const result = [];\n    const state = { queue: 0, queuedBits: 0 };\n    const onByte = (byte) => {\n        result.push(byte);\n    };\n    for (let i = 0; i < str.length; i += 1) {\n        byteFromBase64URL(str.charCodeAt(i), state, onByte);\n    }\n    return new Uint8Array(result);\n}\nfunction stringToUint8Array(str) {\n    const result = [];\n    stringToUTF8(str, (byte) => result.push(byte));\n    return new Uint8Array(result);\n}\nfunction bytesToBase64URL(bytes) {\n    const result = [];\n    const state = { queue: 0, queuedBits: 0 };\n    const onChar = (char) => {\n        result.push(char);\n    };\n    bytes.forEach((byte) => byteToBase64URL(byte, state, onChar));\n    // always call with `null` after processing all bytes\n    byteToBase64URL(null, state, onChar);\n    return result.join('');\n}\n//# sourceMappingURL=base64url.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/base64url.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/constants.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/constants.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_VERSIONS: () => (/* binding */ API_VERSIONS),\n/* harmony export */   API_VERSION_HEADER_NAME: () => (/* binding */ API_VERSION_HEADER_NAME),\n/* harmony export */   AUDIENCE: () => (/* binding */ AUDIENCE),\n/* harmony export */   AUTO_REFRESH_TICK_DURATION_MS: () => (/* binding */ AUTO_REFRESH_TICK_DURATION_MS),\n/* harmony export */   AUTO_REFRESH_TICK_THRESHOLD: () => (/* binding */ AUTO_REFRESH_TICK_THRESHOLD),\n/* harmony export */   BASE64URL_REGEX: () => (/* binding */ BASE64URL_REGEX),\n/* harmony export */   DEFAULT_HEADERS: () => (/* binding */ DEFAULT_HEADERS),\n/* harmony export */   EXPIRY_MARGIN_MS: () => (/* binding */ EXPIRY_MARGIN_MS),\n/* harmony export */   GOTRUE_URL: () => (/* binding */ GOTRUE_URL),\n/* harmony export */   JWKS_TTL: () => (/* binding */ JWKS_TTL),\n/* harmony export */   NETWORK_FAILURE: () => (/* binding */ NETWORK_FAILURE),\n/* harmony export */   STORAGE_KEY: () => (/* binding */ STORAGE_KEY)\n/* harmony export */ });\n/* harmony import */ var _version__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./version */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/version.js\");\n\n/** Current session will be checked for refresh at this interval. */\nconst AUTO_REFRESH_TICK_DURATION_MS = 30 * 1000;\n/**\n * A token refresh will be attempted this many ticks before the current session expires. */\nconst AUTO_REFRESH_TICK_THRESHOLD = 3;\n/*\n * Earliest time before an access token expires that the session should be refreshed.\n */\nconst EXPIRY_MARGIN_MS = AUTO_REFRESH_TICK_THRESHOLD * AUTO_REFRESH_TICK_DURATION_MS;\nconst GOTRUE_URL = 'http://localhost:9999';\nconst STORAGE_KEY = 'supabase.auth.token';\nconst AUDIENCE = '';\nconst DEFAULT_HEADERS = { 'X-Client-Info': `gotrue-js/${_version__WEBPACK_IMPORTED_MODULE_0__.version}` };\nconst NETWORK_FAILURE = {\n    MAX_RETRIES: 10,\n    RETRY_INTERVAL: 2, // in deciseconds\n};\nconst API_VERSION_HEADER_NAME = 'X-Supabase-Api-Version';\nconst API_VERSIONS = {\n    '2024-01-01': {\n        timestamp: Date.parse('2024-01-01T00:00:00.0Z'),\n        name: '2024-01-01',\n    },\n};\nconst BASE64URL_REGEX = /^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;\nconst JWKS_TTL = 600000; // 10 minutes\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/errors.js":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/errors.js ***!
  \**************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthApiError: () => (/* binding */ AuthApiError),\n/* harmony export */   AuthError: () => (/* binding */ AuthError),\n/* harmony export */   AuthImplicitGrantRedirectError: () => (/* binding */ AuthImplicitGrantRedirectError),\n/* harmony export */   AuthInvalidCredentialsError: () => (/* binding */ AuthInvalidCredentialsError),\n/* harmony export */   AuthInvalidJwtError: () => (/* binding */ AuthInvalidJwtError),\n/* harmony export */   AuthInvalidTokenResponseError: () => (/* binding */ AuthInvalidTokenResponseError),\n/* harmony export */   AuthPKCEGrantCodeExchangeError: () => (/* binding */ AuthPKCEGrantCodeExchangeError),\n/* harmony export */   AuthRetryableFetchError: () => (/* binding */ AuthRetryableFetchError),\n/* harmony export */   AuthSessionMissingError: () => (/* binding */ AuthSessionMissingError),\n/* harmony export */   AuthUnknownError: () => (/* binding */ AuthUnknownError),\n/* harmony export */   AuthWeakPasswordError: () => (/* binding */ AuthWeakPasswordError),\n/* harmony export */   CustomAuthError: () => (/* binding */ CustomAuthError),\n/* harmony export */   isAuthApiError: () => (/* binding */ isAuthApiError),\n/* harmony export */   isAuthError: () => (/* binding */ isAuthError),\n/* harmony export */   isAuthImplicitGrantRedirectError: () => (/* binding */ isAuthImplicitGrantRedirectError),\n/* harmony export */   isAuthRetryableFetchError: () => (/* binding */ isAuthRetryableFetchError),\n/* harmony export */   isAuthSessionMissingError: () => (/* binding */ isAuthSessionMissingError),\n/* harmony export */   isAuthWeakPasswordError: () => (/* binding */ isAuthWeakPasswordError)\n/* harmony export */ });\nclass AuthError extends Error {\n    constructor(message, status, code) {\n        super(message);\n        this.__isAuthError = true;\n        this.name = 'AuthError';\n        this.status = status;\n        this.code = code;\n    }\n}\nfunction isAuthError(error) {\n    return typeof error === 'object' && error !== null && '__isAuthError' in error;\n}\nclass AuthApiError extends AuthError {\n    constructor(message, status, code) {\n        super(message, status, code);\n        this.name = 'AuthApiError';\n        this.status = status;\n        this.code = code;\n    }\n}\nfunction isAuthApiError(error) {\n    return isAuthError(error) && error.name === 'AuthApiError';\n}\nclass AuthUnknownError extends AuthError {\n    constructor(message, originalError) {\n        super(message);\n        this.name = 'AuthUnknownError';\n        this.originalError = originalError;\n    }\n}\nclass CustomAuthError extends AuthError {\n    constructor(message, name, status, code) {\n        super(message, status, code);\n        this.name = name;\n        this.status = status;\n    }\n}\nclass AuthSessionMissingError extends CustomAuthError {\n    constructor() {\n        super('Auth session missing!', 'AuthSessionMissingError', 400, undefined);\n    }\n}\nfunction isAuthSessionMissingError(error) {\n    return isAuthError(error) && error.name === 'AuthSessionMissingError';\n}\nclass AuthInvalidTokenResponseError extends CustomAuthError {\n    constructor() {\n        super('Auth session or user missing', 'AuthInvalidTokenResponseError', 500, undefined);\n    }\n}\nclass AuthInvalidCredentialsError extends CustomAuthError {\n    constructor(message) {\n        super(message, 'AuthInvalidCredentialsError', 400, undefined);\n    }\n}\nclass AuthImplicitGrantRedirectError extends CustomAuthError {\n    constructor(message, details = null) {\n        super(message, 'AuthImplicitGrantRedirectError', 500, undefined);\n        this.details = null;\n        this.details = details;\n    }\n    toJSON() {\n        return {\n            name: this.name,\n            message: this.message,\n            status: this.status,\n            details: this.details,\n        };\n    }\n}\nfunction isAuthImplicitGrantRedirectError(error) {\n    return isAuthError(error) && error.name === 'AuthImplicitGrantRedirectError';\n}\nclass AuthPKCEGrantCodeExchangeError extends CustomAuthError {\n    constructor(message, details = null) {\n        super(message, 'AuthPKCEGrantCodeExchangeError', 500, undefined);\n        this.details = null;\n        this.details = details;\n    }\n    toJSON() {\n        return {\n            name: this.name,\n            message: this.message,\n            status: this.status,\n            details: this.details,\n        };\n    }\n}\nclass AuthRetryableFetchError extends CustomAuthError {\n    constructor(message, status) {\n        super(message, 'AuthRetryableFetchError', status, undefined);\n    }\n}\nfunction isAuthRetryableFetchError(error) {\n    return isAuthError(error) && error.name === 'AuthRetryableFetchError';\n}\n/**\n * This error is thrown on certain methods when the password used is deemed\n * weak. Inspect the reasons to identify what password strength rules are\n * inadequate.\n */\nclass AuthWeakPasswordError extends CustomAuthError {\n    constructor(message, status, reasons) {\n        super(message, 'AuthWeakPasswordError', status, 'weak_password');\n        this.reasons = reasons;\n    }\n}\nfunction isAuthWeakPasswordError(error) {\n    return isAuthError(error) && error.name === 'AuthWeakPasswordError';\n}\nclass AuthInvalidJwtError extends CustomAuthError {\n    constructor(message) {\n        super(message, 'AuthInvalidJwtError', 400, 'invalid_jwt');\n    }\n}\n//# sourceMappingURL=errors.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/errors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/fetch.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/fetch.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _generateLinkResponse: () => (/* binding */ _generateLinkResponse),\n/* harmony export */   _noResolveJsonResponse: () => (/* binding */ _noResolveJsonResponse),\n/* harmony export */   _request: () => (/* binding */ _request),\n/* harmony export */   _sessionResponse: () => (/* binding */ _sessionResponse),\n/* harmony export */   _sessionResponsePassword: () => (/* binding */ _sessionResponsePassword),\n/* harmony export */   _ssoResponse: () => (/* binding */ _ssoResponse),\n/* harmony export */   _userResponse: () => (/* binding */ _userResponse),\n/* harmony export */   handleError: () => (/* binding */ handleError)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/constants.js\");\n/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./helpers */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/helpers.js\");\n/* harmony import */ var _errors__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./errors */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/errors.js\");\nvar __rest = (undefined && undefined.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\n\n\n\nconst _getErrorMessage = (err) => err.msg || err.message || err.error_description || err.error || JSON.stringify(err);\nconst NETWORK_ERROR_CODES = [502, 503, 504];\nasync function handleError(error) {\n    var _a;\n    if (!(0,_helpers__WEBPACK_IMPORTED_MODULE_1__.looksLikeFetchResponse)(error)) {\n        throw new _errors__WEBPACK_IMPORTED_MODULE_2__.AuthRetryableFetchError(_getErrorMessage(error), 0);\n    }\n    if (NETWORK_ERROR_CODES.includes(error.status)) {\n        // status in 500...599 range - server had an error, request might be retryed.\n        throw new _errors__WEBPACK_IMPORTED_MODULE_2__.AuthRetryableFetchError(_getErrorMessage(error), error.status);\n    }\n    let data;\n    try {\n        data = await error.json();\n    }\n    catch (e) {\n        throw new _errors__WEBPACK_IMPORTED_MODULE_2__.AuthUnknownError(_getErrorMessage(e), e);\n    }\n    let errorCode = undefined;\n    const responseAPIVersion = (0,_helpers__WEBPACK_IMPORTED_MODULE_1__.parseResponseAPIVersion)(error);\n    if (responseAPIVersion &&\n        responseAPIVersion.getTime() >= _constants__WEBPACK_IMPORTED_MODULE_0__.API_VERSIONS['2024-01-01'].timestamp &&\n        typeof data === 'object' &&\n        data &&\n        typeof data.code === 'string') {\n        errorCode = data.code;\n    }\n    else if (typeof data === 'object' && data && typeof data.error_code === 'string') {\n        errorCode = data.error_code;\n    }\n    if (!errorCode) {\n        // Legacy support for weak password errors, when there were no error codes\n        if (typeof data === 'object' &&\n            data &&\n            typeof data.weak_password === 'object' &&\n            data.weak_password &&\n            Array.isArray(data.weak_password.reasons) &&\n            data.weak_password.reasons.length &&\n            data.weak_password.reasons.reduce((a, i) => a && typeof i === 'string', true)) {\n            throw new _errors__WEBPACK_IMPORTED_MODULE_2__.AuthWeakPasswordError(_getErrorMessage(data), error.status, data.weak_password.reasons);\n        }\n    }\n    else if (errorCode === 'weak_password') {\n        throw new _errors__WEBPACK_IMPORTED_MODULE_2__.AuthWeakPasswordError(_getErrorMessage(data), error.status, ((_a = data.weak_password) === null || _a === void 0 ? void 0 : _a.reasons) || []);\n    }\n    else if (errorCode === 'session_not_found') {\n        // The `session_id` inside the JWT does not correspond to a row in the\n        // `sessions` table. This usually means the user has signed out, has been\n        // deleted, or their session has somehow been terminated.\n        throw new _errors__WEBPACK_IMPORTED_MODULE_2__.AuthSessionMissingError();\n    }\n    throw new _errors__WEBPACK_IMPORTED_MODULE_2__.AuthApiError(_getErrorMessage(data), error.status || 500, errorCode);\n}\nconst _getRequestParams = (method, options, parameters, body) => {\n    const params = { method, headers: (options === null || options === void 0 ? void 0 : options.headers) || {} };\n    if (method === 'GET') {\n        return params;\n    }\n    params.headers = Object.assign({ 'Content-Type': 'application/json;charset=UTF-8' }, options === null || options === void 0 ? void 0 : options.headers);\n    params.body = JSON.stringify(body);\n    return Object.assign(Object.assign({}, params), parameters);\n};\nasync function _request(fetcher, method, url, options) {\n    var _a;\n    const headers = Object.assign({}, options === null || options === void 0 ? void 0 : options.headers);\n    if (!headers[_constants__WEBPACK_IMPORTED_MODULE_0__.API_VERSION_HEADER_NAME]) {\n        headers[_constants__WEBPACK_IMPORTED_MODULE_0__.API_VERSION_HEADER_NAME] = _constants__WEBPACK_IMPORTED_MODULE_0__.API_VERSIONS['2024-01-01'].name;\n    }\n    if (options === null || options === void 0 ? void 0 : options.jwt) {\n        headers['Authorization'] = `Bearer ${options.jwt}`;\n    }\n    const qs = (_a = options === null || options === void 0 ? void 0 : options.query) !== null && _a !== void 0 ? _a : {};\n    if (options === null || options === void 0 ? void 0 : options.redirectTo) {\n        qs['redirect_to'] = options.redirectTo;\n    }\n    const queryString = Object.keys(qs).length ? '?' + new URLSearchParams(qs).toString() : '';\n    const data = await _handleRequest(fetcher, method, url + queryString, {\n        headers,\n        noResolveJson: options === null || options === void 0 ? void 0 : options.noResolveJson,\n    }, {}, options === null || options === void 0 ? void 0 : options.body);\n    return (options === null || options === void 0 ? void 0 : options.xform) ? options === null || options === void 0 ? void 0 : options.xform(data) : { data: Object.assign({}, data), error: null };\n}\nasync function _handleRequest(fetcher, method, url, options, parameters, body) {\n    const requestParams = _getRequestParams(method, options, parameters, body);\n    let result;\n    try {\n        result = await fetcher(url, Object.assign({}, requestParams));\n    }\n    catch (e) {\n        console.error(e);\n        // fetch failed, likely due to a network or CORS error\n        throw new _errors__WEBPACK_IMPORTED_MODULE_2__.AuthRetryableFetchError(_getErrorMessage(e), 0);\n    }\n    if (!result.ok) {\n        await handleError(result);\n    }\n    if (options === null || options === void 0 ? void 0 : options.noResolveJson) {\n        return result;\n    }\n    try {\n        return await result.json();\n    }\n    catch (e) {\n        await handleError(e);\n    }\n}\nfunction _sessionResponse(data) {\n    var _a;\n    let session = null;\n    if (hasSession(data)) {\n        session = Object.assign({}, data);\n        if (!data.expires_at) {\n            session.expires_at = (0,_helpers__WEBPACK_IMPORTED_MODULE_1__.expiresAt)(data.expires_in);\n        }\n    }\n    const user = (_a = data.user) !== null && _a !== void 0 ? _a : data;\n    return { data: { session, user }, error: null };\n}\nfunction _sessionResponsePassword(data) {\n    const response = _sessionResponse(data);\n    if (!response.error &&\n        data.weak_password &&\n        typeof data.weak_password === 'object' &&\n        Array.isArray(data.weak_password.reasons) &&\n        data.weak_password.reasons.length &&\n        data.weak_password.message &&\n        typeof data.weak_password.message === 'string' &&\n        data.weak_password.reasons.reduce((a, i) => a && typeof i === 'string', true)) {\n        response.data.weak_password = data.weak_password;\n    }\n    return response;\n}\nfunction _userResponse(data) {\n    var _a;\n    const user = (_a = data.user) !== null && _a !== void 0 ? _a : data;\n    return { data: { user }, error: null };\n}\nfunction _ssoResponse(data) {\n    return { data, error: null };\n}\nfunction _generateLinkResponse(data) {\n    const { action_link, email_otp, hashed_token, redirect_to, verification_type } = data, rest = __rest(data, [\"action_link\", \"email_otp\", \"hashed_token\", \"redirect_to\", \"verification_type\"]);\n    const properties = {\n        action_link,\n        email_otp,\n        hashed_token,\n        redirect_to,\n        verification_type,\n    };\n    const user = Object.assign({}, rest);\n    return {\n        data: {\n            properties,\n            user,\n        },\n        error: null,\n    };\n}\nfunction _noResolveJsonResponse(data) {\n    return data;\n}\n/**\n * hasSession checks if the response object contains a valid session\n * @param data A response object\n * @returns true if a session is in the response\n */\nfunction hasSession(data) {\n    return data.access_token && data.refresh_token && data.expires_in;\n}\n//# sourceMappingURL=fetch.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/fetch.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/helpers.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/helpers.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Deferred: () => (/* binding */ Deferred),\n/* harmony export */   decodeJWT: () => (/* binding */ decodeJWT),\n/* harmony export */   expiresAt: () => (/* binding */ expiresAt),\n/* harmony export */   generatePKCEChallenge: () => (/* binding */ generatePKCEChallenge),\n/* harmony export */   generatePKCEVerifier: () => (/* binding */ generatePKCEVerifier),\n/* harmony export */   getAlgorithm: () => (/* binding */ getAlgorithm),\n/* harmony export */   getCodeChallengeAndMethod: () => (/* binding */ getCodeChallengeAndMethod),\n/* harmony export */   getItemAsync: () => (/* binding */ getItemAsync),\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   looksLikeFetchResponse: () => (/* binding */ looksLikeFetchResponse),\n/* harmony export */   parseParametersFromURL: () => (/* binding */ parseParametersFromURL),\n/* harmony export */   parseResponseAPIVersion: () => (/* binding */ parseResponseAPIVersion),\n/* harmony export */   removeItemAsync: () => (/* binding */ removeItemAsync),\n/* harmony export */   resolveFetch: () => (/* binding */ resolveFetch),\n/* harmony export */   retryable: () => (/* binding */ retryable),\n/* harmony export */   setItemAsync: () => (/* binding */ setItemAsync),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   supportsLocalStorage: () => (/* binding */ supportsLocalStorage),\n/* harmony export */   uuid: () => (/* binding */ uuid),\n/* harmony export */   validateExp: () => (/* binding */ validateExp),\n/* harmony export */   validateUUID: () => (/* binding */ validateUUID)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/constants.js\");\n/* harmony import */ var _errors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./errors */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/errors.js\");\n/* harmony import */ var _base64url__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./base64url */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/base64url.js\");\n\n\n\nfunction expiresAt(expiresIn) {\n    const timeNow = Math.round(Date.now() / 1000);\n    return timeNow + expiresIn;\n}\nfunction uuid() {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n        const r = (Math.random() * 16) | 0, v = c == 'x' ? r : (r & 0x3) | 0x8;\n        return v.toString(16);\n    });\n}\nconst isBrowser = () => typeof window !== 'undefined' && typeof document !== 'undefined';\nconst localStorageWriteTests = {\n    tested: false,\n    writable: false,\n};\n/**\n * Checks whether localStorage is supported on this browser.\n */\nconst supportsLocalStorage = () => {\n    if (!isBrowser()) {\n        return false;\n    }\n    try {\n        if (typeof globalThis.localStorage !== 'object') {\n            return false;\n        }\n    }\n    catch (e) {\n        // DOM exception when accessing `localStorage`\n        return false;\n    }\n    if (localStorageWriteTests.tested) {\n        return localStorageWriteTests.writable;\n    }\n    const randomKey = `lswt-${Math.random()}${Math.random()}`;\n    try {\n        globalThis.localStorage.setItem(randomKey, randomKey);\n        globalThis.localStorage.removeItem(randomKey);\n        localStorageWriteTests.tested = true;\n        localStorageWriteTests.writable = true;\n    }\n    catch (e) {\n        // localStorage can't be written to\n        // https://www.chromium.org/for-testers/bug-reporting-guidelines/uncaught-securityerror-failed-to-read-the-localstorage-property-from-window-access-is-denied-for-this-document\n        localStorageWriteTests.tested = true;\n        localStorageWriteTests.writable = false;\n    }\n    return localStorageWriteTests.writable;\n};\n/**\n * Extracts parameters encoded in the URL both in the query and fragment.\n */\nfunction parseParametersFromURL(href) {\n    const result = {};\n    const url = new URL(href);\n    if (url.hash && url.hash[0] === '#') {\n        try {\n            const hashSearchParams = new URLSearchParams(url.hash.substring(1));\n            hashSearchParams.forEach((value, key) => {\n                result[key] = value;\n            });\n        }\n        catch (e) {\n            // hash is not a query string\n        }\n    }\n    // search parameters take precedence over hash parameters\n    url.searchParams.forEach((value, key) => {\n        result[key] = value;\n    });\n    return result;\n}\nconst resolveFetch = (customFetch) => {\n    let _fetch;\n    if (customFetch) {\n        _fetch = customFetch;\n    }\n    else if (typeof fetch === 'undefined') {\n        _fetch = (...args) => Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! @supabase/node-fetch */ \"(ssr)/./node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/lib/index.js\", 23)).then(({ default: fetch }) => fetch(...args));\n    }\n    else {\n        _fetch = fetch;\n    }\n    return (...args) => _fetch(...args);\n};\nconst looksLikeFetchResponse = (maybeResponse) => {\n    return (typeof maybeResponse === 'object' &&\n        maybeResponse !== null &&\n        'status' in maybeResponse &&\n        'ok' in maybeResponse &&\n        'json' in maybeResponse &&\n        typeof maybeResponse.json === 'function');\n};\n// Storage helpers\nconst setItemAsync = async (storage, key, data) => {\n    await storage.setItem(key, JSON.stringify(data));\n};\nconst getItemAsync = async (storage, key) => {\n    const value = await storage.getItem(key);\n    if (!value) {\n        return null;\n    }\n    try {\n        return JSON.parse(value);\n    }\n    catch (_a) {\n        return value;\n    }\n};\nconst removeItemAsync = async (storage, key) => {\n    await storage.removeItem(key);\n};\n/**\n * A deferred represents some asynchronous work that is not yet finished, which\n * may or may not culminate in a value.\n * Taken from: https://github.com/mike-north/types/blob/master/src/async.ts\n */\nclass Deferred {\n    constructor() {\n        // eslint-disable-next-line @typescript-eslint/no-extra-semi\n        ;\n        this.promise = new Deferred.promiseConstructor((res, rej) => {\n            // eslint-disable-next-line @typescript-eslint/no-extra-semi\n            ;\n            this.resolve = res;\n            this.reject = rej;\n        });\n    }\n}\nDeferred.promiseConstructor = Promise;\nfunction decodeJWT(token) {\n    const parts = token.split('.');\n    if (parts.length !== 3) {\n        throw new _errors__WEBPACK_IMPORTED_MODULE_1__.AuthInvalidJwtError('Invalid JWT structure');\n    }\n    // Regex checks for base64url format\n    for (let i = 0; i < parts.length; i++) {\n        if (!_constants__WEBPACK_IMPORTED_MODULE_0__.BASE64URL_REGEX.test(parts[i])) {\n            throw new _errors__WEBPACK_IMPORTED_MODULE_1__.AuthInvalidJwtError('JWT not in base64url format');\n        }\n    }\n    const data = {\n        // using base64url lib\n        header: JSON.parse((0,_base64url__WEBPACK_IMPORTED_MODULE_2__.stringFromBase64URL)(parts[0])),\n        payload: JSON.parse((0,_base64url__WEBPACK_IMPORTED_MODULE_2__.stringFromBase64URL)(parts[1])),\n        signature: (0,_base64url__WEBPACK_IMPORTED_MODULE_2__.base64UrlToUint8Array)(parts[2]),\n        raw: {\n            header: parts[0],\n            payload: parts[1],\n        },\n    };\n    return data;\n}\n/**\n * Creates a promise that resolves to null after some time.\n */\nasync function sleep(time) {\n    return await new Promise((accept) => {\n        setTimeout(() => accept(null), time);\n    });\n}\n/**\n * Converts the provided async function into a retryable function. Each result\n * or thrown error is sent to the isRetryable function which should return true\n * if the function should run again.\n */\nfunction retryable(fn, isRetryable) {\n    const promise = new Promise((accept, reject) => {\n        // eslint-disable-next-line @typescript-eslint/no-extra-semi\n        ;\n        (async () => {\n            for (let attempt = 0; attempt < Infinity; attempt++) {\n                try {\n                    const result = await fn(attempt);\n                    if (!isRetryable(attempt, null, result)) {\n                        accept(result);\n                        return;\n                    }\n                }\n                catch (e) {\n                    if (!isRetryable(attempt, e)) {\n                        reject(e);\n                        return;\n                    }\n                }\n            }\n        })();\n    });\n    return promise;\n}\nfunction dec2hex(dec) {\n    return ('0' + dec.toString(16)).substr(-2);\n}\n// Functions below taken from: https://stackoverflow.com/questions/63309409/creating-a-code-verifier-and-challenge-for-pkce-auth-on-spotify-api-in-reactjs\nfunction generatePKCEVerifier() {\n    const verifierLength = 56;\n    const array = new Uint32Array(verifierLength);\n    if (typeof crypto === 'undefined') {\n        const charSet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';\n        const charSetLen = charSet.length;\n        let verifier = '';\n        for (let i = 0; i < verifierLength; i++) {\n            verifier += charSet.charAt(Math.floor(Math.random() * charSetLen));\n        }\n        return verifier;\n    }\n    crypto.getRandomValues(array);\n    return Array.from(array, dec2hex).join('');\n}\nasync function sha256(randomString) {\n    const encoder = new TextEncoder();\n    const encodedData = encoder.encode(randomString);\n    const hash = await crypto.subtle.digest('SHA-256', encodedData);\n    const bytes = new Uint8Array(hash);\n    return Array.from(bytes)\n        .map((c) => String.fromCharCode(c))\n        .join('');\n}\nasync function generatePKCEChallenge(verifier) {\n    const hasCryptoSupport = typeof crypto !== 'undefined' &&\n        typeof crypto.subtle !== 'undefined' &&\n        typeof TextEncoder !== 'undefined';\n    if (!hasCryptoSupport) {\n        console.warn('WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256.');\n        return verifier;\n    }\n    const hashed = await sha256(verifier);\n    return btoa(hashed).replace(/\\+/g, '-').replace(/\\//g, '_').replace(/=+$/, '');\n}\nasync function getCodeChallengeAndMethod(storage, storageKey, isPasswordRecovery = false) {\n    const codeVerifier = generatePKCEVerifier();\n    let storedCodeVerifier = codeVerifier;\n    if (isPasswordRecovery) {\n        storedCodeVerifier += '/PASSWORD_RECOVERY';\n    }\n    await setItemAsync(storage, `${storageKey}-code-verifier`, storedCodeVerifier);\n    const codeChallenge = await generatePKCEChallenge(codeVerifier);\n    const codeChallengeMethod = codeVerifier === codeChallenge ? 'plain' : 's256';\n    return [codeChallenge, codeChallengeMethod];\n}\n/** Parses the API version which is 2YYY-MM-DD. */\nconst API_VERSION_REGEX = /^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;\nfunction parseResponseAPIVersion(response) {\n    const apiVersion = response.headers.get(_constants__WEBPACK_IMPORTED_MODULE_0__.API_VERSION_HEADER_NAME);\n    if (!apiVersion) {\n        return null;\n    }\n    if (!apiVersion.match(API_VERSION_REGEX)) {\n        return null;\n    }\n    try {\n        const date = new Date(`${apiVersion}T00:00:00.0Z`);\n        return date;\n    }\n    catch (e) {\n        return null;\n    }\n}\nfunction validateExp(exp) {\n    if (!exp) {\n        throw new Error('Missing exp claim');\n    }\n    const timeNow = Math.floor(Date.now() / 1000);\n    if (exp <= timeNow) {\n        throw new Error('JWT has expired');\n    }\n}\nfunction getAlgorithm(alg) {\n    switch (alg) {\n        case 'RS256':\n            return {\n                name: 'RSASSA-PKCS1-v1_5',\n                hash: { name: 'SHA-256' },\n            };\n        case 'ES256':\n            return {\n                name: 'ECDSA',\n                namedCurve: 'P-256',\n                hash: { name: 'SHA-256' },\n            };\n        default:\n            throw new Error('Invalid alg claim');\n    }\n}\nconst UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;\nfunction validateUUID(str) {\n    if (!UUID_REGEX.test(str)) {\n        throw new Error('@supabase/auth-js: Expected parameter to be UUID but is not');\n    }\n}\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/helpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/local-storage.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/local-storage.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localStorageAdapter: () => (/* binding */ localStorageAdapter),\n/* harmony export */   memoryLocalStorageAdapter: () => (/* binding */ memoryLocalStorageAdapter)\n/* harmony export */ });\n/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./helpers */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/helpers.js\");\n\n/**\n * Provides safe access to the globalThis.localStorage property.\n */\nconst localStorageAdapter = {\n    getItem: (key) => {\n        if (!(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.supportsLocalStorage)()) {\n            return null;\n        }\n        return globalThis.localStorage.getItem(key);\n    },\n    setItem: (key, value) => {\n        if (!(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.supportsLocalStorage)()) {\n            return;\n        }\n        globalThis.localStorage.setItem(key, value);\n    },\n    removeItem: (key) => {\n        if (!(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.supportsLocalStorage)()) {\n            return;\n        }\n        globalThis.localStorage.removeItem(key);\n    },\n};\n/**\n * Returns a localStorage-like object that stores the key-value pairs in\n * memory.\n */\nfunction memoryLocalStorageAdapter(store = {}) {\n    return {\n        getItem: (key) => {\n            return store[key] || null;\n        },\n        setItem: (key, value) => {\n            store[key] = value;\n        },\n        removeItem: (key) => {\n            delete store[key];\n        },\n    };\n}\n//# sourceMappingURL=local-storage.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/local-storage.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/locks.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/locks.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LockAcquireTimeoutError: () => (/* binding */ LockAcquireTimeoutError),\n/* harmony export */   NavigatorLockAcquireTimeoutError: () => (/* binding */ NavigatorLockAcquireTimeoutError),\n/* harmony export */   ProcessLockAcquireTimeoutError: () => (/* binding */ ProcessLockAcquireTimeoutError),\n/* harmony export */   internals: () => (/* binding */ internals),\n/* harmony export */   navigatorLock: () => (/* binding */ navigatorLock),\n/* harmony export */   processLock: () => (/* binding */ processLock)\n/* harmony export */ });\n/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./helpers */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/helpers.js\");\n\n/**\n * @experimental\n */\nconst internals = {\n    /**\n     * @experimental\n     */\n    debug: !!(globalThis &&\n        (0,_helpers__WEBPACK_IMPORTED_MODULE_0__.supportsLocalStorage)() &&\n        globalThis.localStorage &&\n        globalThis.localStorage.getItem('supabase.gotrue-js.locks.debug') === 'true'),\n};\n/**\n * An error thrown when a lock cannot be acquired after some amount of time.\n *\n * Use the {@link #isAcquireTimeout} property instead of checking with `instanceof`.\n */\nclass LockAcquireTimeoutError extends Error {\n    constructor(message) {\n        super(message);\n        this.isAcquireTimeout = true;\n    }\n}\nclass NavigatorLockAcquireTimeoutError extends LockAcquireTimeoutError {\n}\nclass ProcessLockAcquireTimeoutError extends LockAcquireTimeoutError {\n}\n/**\n * Implements a global exclusive lock using the Navigator LockManager API. It\n * is available on all browsers released after 2022-03-15 with Safari being the\n * last one to release support. If the API is not available, this function will\n * throw. Make sure you check availablility before configuring {@link\n * GoTrueClient}.\n *\n * You can turn on debugging by setting the `supabase.gotrue-js.locks.debug`\n * local storage item to `true`.\n *\n * Internals:\n *\n * Since the LockManager API does not preserve stack traces for the async\n * function passed in the `request` method, a trick is used where acquiring the\n * lock releases a previously started promise to run the operation in the `fn`\n * function. The lock waits for that promise to finish (with or without error),\n * while the function will finally wait for the result anyway.\n *\n * @param name Name of the lock to be acquired.\n * @param acquireTimeout If negative, no timeout. If 0 an error is thrown if\n *                       the lock can't be acquired without waiting. If positive, the lock acquire\n *                       will time out after so many milliseconds. An error is\n *                       a timeout if it has `isAcquireTimeout` set to true.\n * @param fn The operation to run once the lock is acquired.\n */\nasync function navigatorLock(name, acquireTimeout, fn) {\n    if (internals.debug) {\n        console.log('@supabase/gotrue-js: navigatorLock: acquire lock', name, acquireTimeout);\n    }\n    const abortController = new globalThis.AbortController();\n    if (acquireTimeout > 0) {\n        setTimeout(() => {\n            abortController.abort();\n            if (internals.debug) {\n                console.log('@supabase/gotrue-js: navigatorLock acquire timed out', name);\n            }\n        }, acquireTimeout);\n    }\n    // MDN article: https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request\n    // Wrapping navigator.locks.request() with a plain Promise is done as some\n    // libraries like zone.js patch the Promise object to track the execution\n    // context. However, it appears that most browsers use an internal promise\n    // implementation when using the navigator.locks.request() API causing them\n    // to lose context and emit confusing log messages or break certain features.\n    // This wrapping is believed to help zone.js track the execution context\n    // better.\n    return await Promise.resolve().then(() => globalThis.navigator.locks.request(name, acquireTimeout === 0\n        ? {\n            mode: 'exclusive',\n            ifAvailable: true,\n        }\n        : {\n            mode: 'exclusive',\n            signal: abortController.signal,\n        }, async (lock) => {\n        if (lock) {\n            if (internals.debug) {\n                console.log('@supabase/gotrue-js: navigatorLock: acquired', name, lock.name);\n            }\n            try {\n                return await fn();\n            }\n            finally {\n                if (internals.debug) {\n                    console.log('@supabase/gotrue-js: navigatorLock: released', name, lock.name);\n                }\n            }\n        }\n        else {\n            if (acquireTimeout === 0) {\n                if (internals.debug) {\n                    console.log('@supabase/gotrue-js: navigatorLock: not immediately available', name);\n                }\n                throw new NavigatorLockAcquireTimeoutError(`Acquiring an exclusive Navigator LockManager lock \"${name}\" immediately failed`);\n            }\n            else {\n                if (internals.debug) {\n                    try {\n                        const result = await globalThis.navigator.locks.query();\n                        console.log('@supabase/gotrue-js: Navigator LockManager state', JSON.stringify(result, null, '  '));\n                    }\n                    catch (e) {\n                        console.warn('@supabase/gotrue-js: Error when querying Navigator LockManager state', e);\n                    }\n                }\n                // Browser is not following the Navigator LockManager spec, it\n                // returned a null lock when we didn't use ifAvailable. So we can\n                // pretend the lock is acquired in the name of backward compatibility\n                // and user experience and just run the function.\n                console.warn('@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request');\n                return await fn();\n            }\n        }\n    }));\n}\nconst PROCESS_LOCKS = {};\n/**\n * Implements a global exclusive lock that works only in the current process.\n * Useful for environments like React Native or other non-browser\n * single-process (i.e. no concept of \"tabs\") environments.\n *\n * Use {@link #navigatorLock} in browser environments.\n *\n * @param name Name of the lock to be acquired.\n * @param acquireTimeout If negative, no timeout. If 0 an error is thrown if\n *                       the lock can't be acquired without waiting. If positive, the lock acquire\n *                       will time out after so many milliseconds. An error is\n *                       a timeout if it has `isAcquireTimeout` set to true.\n * @param fn The operation to run once the lock is acquired.\n */\nasync function processLock(name, acquireTimeout, fn) {\n    var _a;\n    const previousOperation = (_a = PROCESS_LOCKS[name]) !== null && _a !== void 0 ? _a : Promise.resolve();\n    const currentOperation = Promise.race([\n        previousOperation.catch(() => {\n            // ignore error of previous operation that we're waiting to finish\n            return null;\n        }),\n        acquireTimeout >= 0\n            ? new Promise((_, reject) => {\n                setTimeout(() => {\n                    reject(new ProcessLockAcquireTimeoutError(`Acquring process lock with name \"${name}\" timed out`));\n                }, acquireTimeout);\n            })\n            : null,\n    ].filter((x) => x))\n        .catch((e) => {\n        if (e && e.isAcquireTimeout) {\n            throw e;\n        }\n        return null;\n    })\n        .then(async () => {\n        // previous operations finished and we didn't get a race on the acquire\n        // timeout, so the current operation can finally start\n        return await fn();\n    });\n    PROCESS_LOCKS[name] = currentOperation.catch(async (e) => {\n        if (e && e.isAcquireTimeout) {\n            // if the current operation timed out, it doesn't mean that the previous\n            // operation finished, so we need contnue waiting for it to finish\n            await previousOperation;\n            return null;\n        }\n        throw e;\n    });\n    // finally wait for the current operation to finish successfully, with an\n    // error or with an acquire timeout error\n    return await currentOperation;\n}\n//# sourceMappingURL=locks.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/locks.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/polyfills.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/polyfills.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   polyfillGlobalThis: () => (/* binding */ polyfillGlobalThis)\n/* harmony export */ });\n/**\n * https://mathiasbynens.be/notes/globalthis\n */\nfunction polyfillGlobalThis() {\n    if (typeof globalThis === 'object')\n        return;\n    try {\n        Object.defineProperty(Object.prototype, '__magic__', {\n            get: function () {\n                return this;\n            },\n            configurable: true,\n        });\n        // @ts-expect-error 'Allow access to magic'\n        __magic__.globalThis = __magic__;\n        // @ts-expect-error 'Allow access to magic'\n        delete Object.prototype.__magic__;\n    }\n    catch (e) {\n        if (typeof self !== 'undefined') {\n            // @ts-expect-error 'Allow access to globals'\n            self.globalThis = self;\n        }\n    }\n}\n//# sourceMappingURL=polyfills.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK2F1dGgtanNAMi43MC4wL25vZGVfbW9kdWxlcy9Ac3VwYWJhc2UvYXV0aC1qcy9kaXN0L21vZHVsZS9saWIvcG9seWZpbGxzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxtZW1ldFxcRGVza3RvcFxcZG9ybV8yMVxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHN1cGFiYXNlK2F1dGgtanNAMi43MC4wXFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxcYXV0aC1qc1xcZGlzdFxcbW9kdWxlXFxsaWJcXHBvbHlmaWxscy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIGh0dHBzOi8vbWF0aGlhc2J5bmVucy5iZS9ub3Rlcy9nbG9iYWx0aGlzXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBwb2x5ZmlsbEdsb2JhbFRoaXMoKSB7XG4gICAgaWYgKHR5cGVvZiBnbG9iYWxUaGlzID09PSAnb2JqZWN0JylcbiAgICAgICAgcmV0dXJuO1xuICAgIHRyeSB7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShPYmplY3QucHJvdG90eXBlLCAnX19tYWdpY19fJywge1xuICAgICAgICAgICAgZ2V0OiBmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlLFxuICAgICAgICB9KTtcbiAgICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvciAnQWxsb3cgYWNjZXNzIHRvIG1hZ2ljJ1xuICAgICAgICBfX21hZ2ljX18uZ2xvYmFsVGhpcyA9IF9fbWFnaWNfXztcbiAgICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvciAnQWxsb3cgYWNjZXNzIHRvIG1hZ2ljJ1xuICAgICAgICBkZWxldGUgT2JqZWN0LnByb3RvdHlwZS5fX21hZ2ljX187XG4gICAgfVxuICAgIGNhdGNoIChlKSB7XG4gICAgICAgIGlmICh0eXBlb2Ygc2VsZiAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIC8vIEB0cy1leHBlY3QtZXJyb3IgJ0FsbG93IGFjY2VzcyB0byBnbG9iYWxzJ1xuICAgICAgICAgICAgc2VsZi5nbG9iYWxUaGlzID0gc2VsZjtcbiAgICAgICAgfVxuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBvbHlmaWxscy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/polyfills.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/types.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/types.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SIGN_OUT_SCOPES: () => (/* binding */ SIGN_OUT_SCOPES)\n/* harmony export */ });\nconst SIGN_OUT_SCOPES = ['global', 'local', 'others'];\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK2F1dGgtanNAMi43MC4wL25vZGVfbW9kdWxlcy9Ac3VwYWJhc2UvYXV0aC1qcy9kaXN0L21vZHVsZS9saWIvdHlwZXMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1AiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbWVtZXRcXERlc2t0b3BcXGRvcm1fMjFcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZSthdXRoLWpzQDIuNzAuMFxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXGF1dGgtanNcXGRpc3RcXG1vZHVsZVxcbGliXFx0eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgU0lHTl9PVVRfU0NPUEVTID0gWydnbG9iYWwnLCAnbG9jYWwnLCAnb3RoZXJzJ107XG4vLyMgc291cmNlTWFwcGluZ1VSTD10eXBlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/version.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/version.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\nconst version = '2.70.0';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK2F1dGgtanNAMi43MC4wL25vZGVfbW9kdWxlcy9Ac3VwYWJhc2UvYXV0aC1qcy9kaXN0L21vZHVsZS9saWIvdmVyc2lvbi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxtZW1ldFxcRGVza3RvcFxcZG9ybV8yMVxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHN1cGFiYXNlK2F1dGgtanNAMi43MC4wXFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxcYXV0aC1qc1xcZGlzdFxcbW9kdWxlXFxsaWJcXHZlcnNpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHZlcnNpb24gPSAnMi43MC4wJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXZlcnNpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/version.js\n");

/***/ })

};
;