"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/checkin/page",{

/***/ "(app-pages-browser)/./src/hooks/useSupabaseData.tsx":
/*!***************************************!*\
  !*** ./src/hooks/useSupabaseData.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useSupabaseData)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/src/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _src_lib_subscriptionManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/src/lib/subscriptionManager */ \"(app-pages-browser)/./src/lib/subscriptionManager.ts\");\n/* harmony import */ var _src_lib_machineStatusManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/lib/machineStatusManager */ \"(app-pages-browser)/./src/lib/machineStatusManager.ts\");\n/* harmony import */ var _src_utils_parseDuration__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/src/utils/parseDuration */ \"(app-pages-browser)/./src/utils/parseDuration.tsx\");\n/* harmony import */ var _src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/src/utils/userIdentification */ \"(app-pages-browser)/./src/utils/userIdentification.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\n\n\n\n\n\n// Helper functions to convert between DB and app formats\nconst dbToMachine = (row)=>{\n    const graceEndAt = row.grace_end_at ? new Date(row.grace_end_at) : row.status === \"finishedGrace\" && row.end_at ? new Date(new Date(row.end_at).getTime() + 5 * 60 * 1000) : undefined;\n    return {\n        id: row.id,\n        name: row.name,\n        status: row.status,\n        startAt: row.start_at ? new Date(row.start_at) : undefined,\n        endAt: row.end_at ? new Date(row.end_at) : undefined,\n        graceEndAt: graceEndAt,\n        updatedAt: new Date(row.updated_at),\n        startedByUserId: row.started_by_user_id || undefined,\n        startedByDeviceFingerprint: row.started_by_device_fingerprint || undefined\n    };\n};\nconst dbToNoise = (row)=>({\n        id: row.id,\n        user: row.user_name,\n        description: row.description || \"Noise reported\",\n        timestamp: new Date(row.timestamp),\n        lastReported: new Date(row.last_reported)\n    });\nconst dbToAnnouncement = (row)=>({\n        id: row.id,\n        user: row.user_name,\n        title: row.title,\n        description: row.description,\n        type: row.announcement_type,\n        timestamp: new Date(row.timestamp)\n    });\nconst dbToSublet = (row)=>({\n        id: row.id,\n        user: row.user_name,\n        duration: row.duration,\n        timestamp: new Date(row.timestamp)\n    });\nconst dbToHelpMe = (row)=>({\n        id: row.id,\n        user: row.user_name,\n        description: row.description,\n        timestamp: new Date(row.timestamp)\n    });\nconst dbToIncident = (row)=>({\n        id: row.id,\n        machineId: row.machine_id,\n        timestamp: new Date(row.timestamp),\n        type: row.incident_type\n    });\nfunction useSupabaseData() {\n    _s();\n    const [laundry, setLaundry] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [noise, setNoise] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [announcements, setAnnouncements] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [helpMe, setHelpMe] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [incidents, setIncidents] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [hasGraceEndColumn, setHasGraceEndColumn] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    // Refs to prevent recursive calls and multiple setups\n    const isLoadingDataRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const lastLoadTimeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const subscriptionSetupRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const statusManagerSetupRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const isMountedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(true);\n    // Debounced data loading function with selective loading\n    const loadAllData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[loadAllData]\": async function(specificTable) {\n            let forceRefresh = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n            const now = Date.now();\n            // Skip debounce for manual refresh (forceRefresh = true)\n            if (!forceRefresh && (isLoadingDataRef.current || now - lastLoadTimeRef.current < 1000)) {\n                console.log(\"⏭️ Skipping data load (too recent or in progress)\");\n                return;\n            }\n            if (!isMountedRef.current) {\n                console.log(\"⏭️ Skipping data load (component unmounted)\");\n                return;\n            }\n            isLoadingDataRef.current = true;\n            lastLoadTimeRef.current = now;\n            try {\n                console.log(\"\\uD83D\\uDD04 Loading data from Supabase\".concat(specificTable ? \" (\".concat(specificTable, \")\") : \"\", \"...\"));\n                setError(null);\n                // If specific table is provided, only load that table\n                if (specificTable) {\n                    await loadSpecificTable(specificTable);\n                } else {\n                    // Load all data\n                    setIsLoading(true);\n                    await loadAllTables();\n                }\n                console.log(\"✅ Data loaded successfully\");\n            } catch (err) {\n                console.error(\"❌ Error loading data:\", err);\n                if (isMountedRef.current) {\n                    setError(err instanceof Error ? err.message : \"Failed to load data\");\n                }\n            } finally{\n                if (isMountedRef.current && !specificTable) {\n                    setIsLoading(false);\n                }\n                isLoadingDataRef.current = false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[loadAllData]\"], []);\n    // Helper function to load specific table\n    const loadSpecificTable = async (table)=>{\n        switch(table){\n            case \"machines\":\n                var _machinesResult_data;\n                const machinesResult = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").select(\"*\").order(\"name\");\n                if (machinesResult.error) throw machinesResult.error;\n                setLaundry(((_machinesResult_data = machinesResult.data) === null || _machinesResult_data === void 0 ? void 0 : _machinesResult_data.map(dbToMachine)) || []);\n                break;\n            case \"noise_reports\":\n                var _noiseResult_data;\n                const noiseResult = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"noise_reports\").select(\"*\").order(\"timestamp\", {\n                    ascending: false\n                }).limit(50);\n                if (noiseResult.error) throw noiseResult.error;\n                setNoise(((_noiseResult_data = noiseResult.data) === null || _noiseResult_data === void 0 ? void 0 : _noiseResult_data.map(dbToNoise)) || []);\n                break;\n            case \"announcements\":\n                var _announcementsResult_data;\n                const announcementsResult = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"announcements\").select(\"*\").order(\"timestamp\", {\n                    ascending: false\n                }).limit(50);\n                if (announcementsResult.error) throw announcementsResult.error;\n                setAnnouncements(((_announcementsResult_data = announcementsResult.data) === null || _announcementsResult_data === void 0 ? void 0 : _announcementsResult_data.map(dbToAnnouncement)) || []);\n                break;\n            case \"help_requests\":\n                var _helpResult_data;\n                const helpResult = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"help_requests\").select(\"*\").gte(\"timestamp\", new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()).order(\"timestamp\", {\n                    ascending: false\n                }).limit(50);\n                if (helpResult.error) throw helpResult.error;\n                setHelpMe(((_helpResult_data = helpResult.data) === null || _helpResult_data === void 0 ? void 0 : _helpResult_data.map(dbToHelpMe)) || []);\n                break;\n            case \"incidents\":\n                var _incidentsResult_data;\n                const incidentsResult = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"incidents\").select(\"*\").order(\"timestamp\", {\n                    ascending: false\n                }).limit(50);\n                if (incidentsResult.error) throw incidentsResult.error;\n                setIncidents(((_incidentsResult_data = incidentsResult.data) === null || _incidentsResult_data === void 0 ? void 0 : _incidentsResult_data.map(dbToIncident)) || []);\n                break;\n        }\n    };\n    // Helper function to load all tables\n    const loadAllTables = async ()=>{\n        var _machinesResult_data, _noiseResult_data, _announcementsResult_data, _helpResult_data, _incidentsResult_data;\n        const timeoutPromise = new Promise((_, reject)=>setTimeout(()=>reject(new Error(\"Request timeout\")), 15000));\n        const dataPromise = Promise.all([\n            _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").select(\"*\").order(\"name\"),\n            _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"noise_reports\").select(\"*\").order(\"timestamp\", {\n                ascending: false\n            }).limit(50),\n            _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"announcements\").select(\"*\").order(\"timestamp\", {\n                ascending: false\n            }).limit(50),\n            _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"help_requests\").select(\"*\").gte(\"timestamp\", new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()).order(\"timestamp\", {\n                ascending: false\n            }).limit(50),\n            _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"incidents\").select(\"*\").order(\"timestamp\", {\n                ascending: false\n            }).limit(50)\n        ]);\n        const [machinesResult, noiseResult, announcementsResult, helpResult, incidentsResult] = await Promise.race([\n            dataPromise,\n            timeoutPromise\n        ]);\n        if (!isMountedRef.current) return;\n        if (machinesResult.error) throw new Error(\"Failed to load machines: \".concat(machinesResult.error.message));\n        if (noiseResult.error) throw new Error(\"Failed to load noise reports: \".concat(noiseResult.error.message));\n        if (announcementsResult.error) throw new Error(\"Failed to load announcements: \".concat(announcementsResult.error.message));\n        if (helpResult.error) throw new Error(\"Failed to load help requests: \".concat(helpResult.error.message));\n        if (incidentsResult.error) throw new Error(\"Failed to load incidents: \".concat(incidentsResult.error.message));\n        setLaundry(((_machinesResult_data = machinesResult.data) === null || _machinesResult_data === void 0 ? void 0 : _machinesResult_data.map(dbToMachine)) || []);\n        setNoise(((_noiseResult_data = noiseResult.data) === null || _noiseResult_data === void 0 ? void 0 : _noiseResult_data.map(dbToNoise)) || []);\n        setAnnouncements(((_announcementsResult_data = announcementsResult.data) === null || _announcementsResult_data === void 0 ? void 0 : _announcementsResult_data.map(dbToAnnouncement)) || []);\n        setHelpMe(((_helpResult_data = helpResult.data) === null || _helpResult_data === void 0 ? void 0 : _helpResult_data.map(dbToHelpMe)) || []);\n        setIncidents(((_incidentsResult_data = incidentsResult.data) === null || _incidentsResult_data === void 0 ? void 0 : _incidentsResult_data.map(dbToIncident)) || []);\n    };\n    // Set up real-time subscriptions with table-specific updates\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseData.useEffect\": ()=>{\n            if (subscriptionSetupRef.current) {\n                return;\n            }\n            subscriptionSetupRef.current = true;\n            console.log(\"🔄 Setting up subscription manager...\");\n            const subscriptionManager = _src_lib_subscriptionManager__WEBPACK_IMPORTED_MODULE_2__[\"default\"].getInstance();\n            // Enhanced callback that handles specific table updates\n            const handleRealtimeUpdate = {\n                \"useSupabaseData.useEffect.handleRealtimeUpdate\": (table, event)=>{\n                    if (table && table !== \"polling\") {\n                        // Load only the specific table that changed for faster updates\n                        loadAllData(table);\n                    } else {\n                        // Fallback to loading all data\n                        loadAllData();\n                    }\n                }\n            }[\"useSupabaseData.useEffect.handleRealtimeUpdate\"];\n            subscriptionManager.addCallback(handleRealtimeUpdate);\n            return ({\n                \"useSupabaseData.useEffect\": ()=>{\n                    console.log(\"🧹 Cleaning up subscription manager...\");\n                    subscriptionManager.removeCallback(handleRealtimeUpdate);\n                    subscriptionSetupRef.current = false;\n                }\n            })[\"useSupabaseData.useEffect\"];\n        }\n    }[\"useSupabaseData.useEffect\"], [\n        loadAllData\n    ]);\n    // Set up machine status monitoring (only once)\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseData.useEffect\": ()=>{\n            if (statusManagerSetupRef.current) {\n                return;\n            }\n            statusManagerSetupRef.current = true;\n            console.log(\"🔄 Setting up machine status manager...\");\n            const statusManager = _src_lib_machineStatusManager__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getInstance();\n            statusManager.startStatusMonitoring();\n            return ({\n                \"useSupabaseData.useEffect\": ()=>{\n                    console.log(\"🧹 Cleaning up machine status manager...\");\n                    statusManager.stopStatusMonitoring();\n                    statusManagerSetupRef.current = false;\n                }\n            })[\"useSupabaseData.useEffect\"];\n        }\n    }[\"useSupabaseData.useEffect\"], []);\n    // Initial data load and cleanup\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseData.useEffect\": ()=>{\n            isMountedRef.current = true;\n            loadAllData();\n            return ({\n                \"useSupabaseData.useEffect\": ()=>{\n                    isMountedRef.current = false;\n                }\n            })[\"useSupabaseData.useEffect\"];\n        }\n    }[\"useSupabaseData.useEffect\"], []) // Only run once on mount\n    ;\n    // Machine operations with optimistic updates\n    const toggleMachineStatus = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[toggleMachineStatus]\": async (id)=>{\n            try {\n                const machine = laundry.find({\n                    \"useSupabaseData.useCallback[toggleMachineStatus].machine\": (m)=>m.id === id\n                }[\"useSupabaseData.useCallback[toggleMachineStatus].machine\"]);\n                if (!machine) {\n                    return false;\n                }\n                let optimisticUpdate;\n                let updateData;\n                if (machine.status === \"free\") {\n                    const startAt = new Date();\n                    const endAt = new Date(startAt.getTime() + 60 * 60 * 1000);\n                    const currentUserId = (0,_src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_5__.getDeviceUserId)();\n                    optimisticUpdate = {\n                        ...machine,\n                        status: \"running\",\n                        startAt,\n                        endAt,\n                        graceEndAt: undefined,\n                        updatedAt: new Date(),\n                        startedByUserId: currentUserId,\n                        startedByDeviceFingerprint: currentUserId\n                    };\n                    updateData = {\n                        status: \"running\",\n                        start_at: startAt.toISOString(),\n                        end_at: endAt.toISOString(),\n                        started_by_user_id: currentUserId,\n                        started_by_device_fingerprint: currentUserId\n                    };\n                    if (hasGraceEndColumn) {\n                        updateData.grace_end_at = null;\n                    }\n                } else if (machine.status === \"running\" || machine.status === \"finishedGrace\") {\n                    // Machine is in use - only owner can stop it\n                    const currentUserId = (0,_src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_5__.getDeviceUserId)();\n                    if (machine.startedByUserId !== currentUserId) {\n                        setError(\"This machine is currently in use by another user\");\n                        return false;\n                    }\n                    // Owner stopping their machine\n                    optimisticUpdate = {\n                        ...machine,\n                        status: \"free\",\n                        startAt: undefined,\n                        endAt: undefined,\n                        graceEndAt: undefined,\n                        updatedAt: new Date(),\n                        startedByUserId: undefined,\n                        startedByDeviceFingerprint: undefined\n                    };\n                    updateData = {\n                        status: \"free\",\n                        start_at: null,\n                        end_at: null,\n                        started_by_user_id: null,\n                        started_by_device_fingerprint: null\n                    };\n                    if (hasGraceEndColumn) {\n                        updateData.grace_end_at = null;\n                    }\n                } else {\n                    // This shouldn't happen with proper UI controls\n                    setError(\"Invalid machine status for this operation\");\n                    return false;\n                }\n                // Apply optimistic update\n                setLaundry({\n                    \"useSupabaseData.useCallback[toggleMachineStatus]\": (prev)=>prev.map({\n                            \"useSupabaseData.useCallback[toggleMachineStatus]\": (m)=>m.id === id ? optimisticUpdate : m\n                        }[\"useSupabaseData.useCallback[toggleMachineStatus]\"])\n                }[\"useSupabaseData.useCallback[toggleMachineStatus]\"]);\n                // Send update to database\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").update(updateData).eq(\"id\", id);\n                if (error) {\n                    if (error.message && error.message.includes(\"grace_end_at\")) {\n                        setHasGraceEndColumn(false);\n                        delete updateData.grace_end_at;\n                        const { error: retryError } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").update(updateData).eq(\"id\", id);\n                        if (retryError) {\n                            setLaundry({\n                                \"useSupabaseData.useCallback[toggleMachineStatus]\": (prev)=>prev.map({\n                                        \"useSupabaseData.useCallback[toggleMachineStatus]\": (m)=>m.id === id ? machine : m\n                                    }[\"useSupabaseData.useCallback[toggleMachineStatus]\"])\n                            }[\"useSupabaseData.useCallback[toggleMachineStatus]\"]);\n                            throw retryError;\n                        }\n                    } else {\n                        setLaundry({\n                            \"useSupabaseData.useCallback[toggleMachineStatus]\": (prev)=>prev.map({\n                                    \"useSupabaseData.useCallback[toggleMachineStatus]\": (m)=>m.id === id ? machine : m\n                                }[\"useSupabaseData.useCallback[toggleMachineStatus]\"])\n                        }[\"useSupabaseData.useCallback[toggleMachineStatus]\"]);\n                        throw error;\n                    }\n                }\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to update machine\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[toggleMachineStatus]\"], [\n        laundry,\n        hasGraceEndColumn\n    ]);\n    const reserveMachine = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[reserveMachine]\": async (id, durationStr)=>{\n            try {\n                const machine = laundry.find({\n                    \"useSupabaseData.useCallback[reserveMachine].machine\": (m)=>m.id === id\n                }[\"useSupabaseData.useCallback[reserveMachine].machine\"]);\n                if (!machine) {\n                    return false;\n                }\n                // Check if machine is available\n                if (machine.status !== \"free\") {\n                    setError(\"This machine is currently in use\");\n                    return false;\n                }\n                const durationSeconds = (0,_src_utils_parseDuration__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(durationStr);\n                const startAt = new Date();\n                const endAt = new Date(startAt.getTime() + durationSeconds * 1000);\n                const currentUserId = (0,_src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_5__.getDeviceUserId)();\n                const optimisticUpdate = {\n                    ...machine,\n                    status: \"running\",\n                    startAt,\n                    endAt,\n                    graceEndAt: undefined,\n                    updatedAt: new Date(),\n                    startedByUserId: currentUserId,\n                    startedByDeviceFingerprint: currentUserId\n                };\n                setLaundry({\n                    \"useSupabaseData.useCallback[reserveMachine]\": (prev)=>prev.map({\n                            \"useSupabaseData.useCallback[reserveMachine]\": (m)=>m.id === id ? optimisticUpdate : m\n                        }[\"useSupabaseData.useCallback[reserveMachine]\"])\n                }[\"useSupabaseData.useCallback[reserveMachine]\"]);\n                const updateData = {\n                    status: \"running\",\n                    start_at: startAt.toISOString(),\n                    end_at: endAt.toISOString(),\n                    started_by_user_id: currentUserId,\n                    started_by_device_fingerprint: currentUserId\n                };\n                if (hasGraceEndColumn) {\n                    updateData.grace_end_at = null;\n                }\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").update(updateData).eq(\"id\", id);\n                if (error) {\n                    if (error.message && error.message.includes(\"grace_end_at\")) {\n                        setHasGraceEndColumn(false);\n                        delete updateData.grace_end_at;\n                        const { error: retryError } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").update(updateData).eq(\"id\", id);\n                        if (retryError) {\n                            setLaundry({\n                                \"useSupabaseData.useCallback[reserveMachine]\": (prev)=>prev.map({\n                                        \"useSupabaseData.useCallback[reserveMachine]\": (m)=>m.id === id ? machine : m\n                                    }[\"useSupabaseData.useCallback[reserveMachine]\"])\n                            }[\"useSupabaseData.useCallback[reserveMachine]\"]);\n                            throw retryError;\n                        }\n                    } else {\n                        setLaundry({\n                            \"useSupabaseData.useCallback[reserveMachine]\": (prev)=>prev.map({\n                                    \"useSupabaseData.useCallback[reserveMachine]\": (m)=>m.id === id ? machine : m\n                                }[\"useSupabaseData.useCallback[reserveMachine]\"])\n                        }[\"useSupabaseData.useCallback[reserveMachine]\"]);\n                        throw error;\n                    }\n                }\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to reserve machine\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[reserveMachine]\"], [\n        laundry,\n        hasGraceEndColumn\n    ]);\n    // New function to adjust machine time (only for machines you started)\n    const adjustMachineTime = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[adjustMachineTime]\": async (id, newMinutes)=>{\n            try {\n                const machine = laundry.find({\n                    \"useSupabaseData.useCallback[adjustMachineTime].machine\": (m)=>m.id === id\n                }[\"useSupabaseData.useCallback[adjustMachineTime].machine\"]);\n                if (!machine) {\n                    return {\n                        success: false,\n                        error: \"Machine not found\"\n                    };\n                }\n                const currentUserId = (0,_src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_5__.getDeviceUserId)();\n                // Check ownership - only the user who started the machine can adjust it\n                if (machine.startedByUserId !== currentUserId) {\n                    return {\n                        success: false,\n                        error: \"You can only adjust machines you started\"\n                    };\n                }\n                // Validate time range (1-120 minutes for adjustment)\n                if (newMinutes < 1 || newMinutes > 120) {\n                    return {\n                        success: false,\n                        error: \"Please enter a number between 1-120 minutes\"\n                    };\n                }\n                // Calculate new end time based on the new total duration from start time\n                const now = new Date();\n                const startTime = machine.startAt || now;\n                const newEndAt = new Date(startTime.getTime() + newMinutes * 60 * 1000);\n                const optimisticUpdate = {\n                    ...machine,\n                    endAt: newEndAt,\n                    updatedAt: new Date()\n                };\n                // Apply optimistic update\n                setLaundry({\n                    \"useSupabaseData.useCallback[adjustMachineTime]\": (prev)=>prev.map({\n                            \"useSupabaseData.useCallback[adjustMachineTime]\": (m)=>m.id === id ? optimisticUpdate : m\n                        }[\"useSupabaseData.useCallback[adjustMachineTime]\"])\n                }[\"useSupabaseData.useCallback[adjustMachineTime]\"]);\n                // Send update to database\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"machines\").update({\n                    end_at: newEndAt.toISOString()\n                }).eq(\"id\", id);\n                if (error) {\n                    // Revert optimistic update on error\n                    setLaundry({\n                        \"useSupabaseData.useCallback[adjustMachineTime]\": (prev)=>prev.map({\n                                \"useSupabaseData.useCallback[adjustMachineTime]\": (m)=>m.id === id ? machine : m\n                            }[\"useSupabaseData.useCallback[adjustMachineTime]\"])\n                    }[\"useSupabaseData.useCallback[adjustMachineTime]\"]);\n                    return {\n                        success: false,\n                        error: \"Unable to update timer. Please try again.\"\n                    };\n                }\n                return {\n                    success: true,\n                    error: null\n                };\n            } catch (err) {\n                return {\n                    success: false,\n                    error: err instanceof Error ? err.message : \"Unable to update timer. Please try again.\"\n                };\n            }\n        }\n    }[\"useSupabaseData.useCallback[adjustMachineTime]\"], [\n        laundry\n    ]);\n    // Simplified CRUD operations\n    const addNoiseWithDescription = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[addNoiseWithDescription]\": async (user, description)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"noise_reports\").insert({\n                    id: Date.now().toString(),\n                    user_name: user,\n                    description: description\n                });\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to add noise report\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[addNoiseWithDescription]\"], []);\n    const deleteNoise = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[deleteNoise]\": async (id)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"noise_reports\").delete().eq(\"id\", id);\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to delete noise report\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[deleteNoise]\"], []);\n    const addAnnouncement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[addAnnouncement]\": async (user, title, description, type)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"announcements\").insert({\n                    id: Date.now().toString(),\n                    user_name: user,\n                    title: title,\n                    description: description,\n                    announcement_type: type\n                });\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to add announcement\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[addAnnouncement]\"], []);\n    const deleteAnnouncement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[deleteAnnouncement]\": async (id)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"announcements\").delete().eq(\"id\", id);\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to delete announcement\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[deleteAnnouncement]\"], []);\n    const addHelpRequest = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[addHelpRequest]\": async (user, description)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"help_requests\").insert({\n                    id: Date.now().toString(),\n                    user_name: user,\n                    description\n                });\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to add help request\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[addHelpRequest]\"], []);\n    const deleteHelpRequest = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[deleteHelpRequest]\": async (id)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"help_requests\").delete().eq(\"id\", id);\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to delete help request\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[deleteHelpRequest]\"], []);\n    const deleteIncident = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseData.useCallback[deleteIncident]\": async (id)=>{\n            try {\n                const { error } = await _src_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"incidents\").delete().eq(\"id\", id);\n                if (error) throw error;\n                return true;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Failed to delete incident\");\n                return false;\n            }\n        }\n    }[\"useSupabaseData.useCallback[deleteIncident]\"], []);\n    return {\n        // Data\n        laundry,\n        noise,\n        announcements,\n        helpMe,\n        incidents,\n        // State\n        isLoading,\n        error,\n        // Operations\n        toggleMachineStatus,\n        reserveMachine,\n        adjustMachineTime,\n        addNoiseWithDescription,\n        deleteNoise,\n        addAnnouncement,\n        deleteAnnouncement,\n        addHelpRequest,\n        deleteHelpRequest,\n        deleteIncident,\n        refreshData: ()=>loadAllData(undefined, true)\n    };\n}\n_s(useSupabaseData, \"2+7VgD2CuMwPZ4C28ZFRT/x3kEM=\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useSupabaseData.tsx\n"));

/***/ })

});