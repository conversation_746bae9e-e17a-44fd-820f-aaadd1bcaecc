"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/LaundryCard.tsx":
/*!****************************************!*\
  !*** ./src/components/LaundryCard.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LaundryCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/src/hooks/useSupabaseData */ \"(app-pages-browser)/./src/hooks/useSupabaseData.tsx\");\n/* harmony import */ var _src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/hooks/useCountdown */ \"(app-pages-browser)/./src/hooks/useCountdown.tsx\");\n/* harmony import */ var _IncidentBadge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./IncidentBadge */ \"(app-pages-browser)/./src/components/IncidentBadge.tsx\");\n/* harmony import */ var _ui_CardWrapper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/CardWrapper */ \"(app-pages-browser)/./src/components/ui/CardWrapper.tsx\");\n/* harmony import */ var _ui_WasherSVG__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/WasherSVG */ \"(app-pages-browser)/./src/components/ui/WasherSVG.tsx\");\n/* harmony import */ var _ui_DryerOutlineSVG__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ui/DryerOutlineSVG */ \"(app-pages-browser)/./src/components/ui/DryerOutlineSVG.tsx\");\n/* harmony import */ var _src_utils_timeUtils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/src/utils/timeUtils */ \"(app-pages-browser)/./src/utils/timeUtils.ts\");\n/* harmony import */ var _src_utils_machineOwnership__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/src/utils/machineOwnership */ \"(app-pages-browser)/./src/utils/machineOwnership.ts\");\n/* harmony import */ var _src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/src/utils/userIdentification */ \"(app-pages-browser)/./src/utils/userIdentification.ts\");\n/* harmony import */ var _TimeAdjustmentModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./TimeAdjustmentModal */ \"(app-pages-browser)/./src/components/TimeAdjustmentModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Component for \"Just Updated\" badge\nfunction JustUpdatedBadge(param) {\n    let { machine } = param;\n    _s();\n    const [showBadge, setShowBadge] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"JustUpdatedBadge.useEffect\": ()=>{\n            if ((0,_src_utils_timeUtils__WEBPACK_IMPORTED_MODULE_8__.isRecentlyUpdated)(machine.updatedAt)) {\n                setShowBadge(true);\n                // Hide badge after 10 seconds\n                const timer = setTimeout({\n                    \"JustUpdatedBadge.useEffect.timer\": ()=>setShowBadge(false)\n                }[\"JustUpdatedBadge.useEffect.timer\"], 10000);\n                return ({\n                    \"JustUpdatedBadge.useEffect\": ()=>clearTimeout(timer)\n                })[\"JustUpdatedBadge.useEffect\"];\n            }\n        }\n    }[\"JustUpdatedBadge.useEffect\"], [\n        machine.updatedAt\n    ]);\n    if (!showBadge) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"absolute -top-2 -left-2 z-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-blue-500 text-white text-xs px-2 py-1 rounded-full shadow-lg animate-pulse-slow\",\n            children: \"Just Updated\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n_s(JustUpdatedBadge, \"mDNabOB/uIKS4gwTk58LpTtHb0g=\");\n_c = JustUpdatedBadge;\n// Component for time ago display\nfunction TimeAgoDisplay(param) {\n    let { machine } = param;\n    _s1();\n    const [timeAgo, setTimeAgo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TimeAgoDisplay.useEffect\": ()=>{\n            const updateTimeAgo = {\n                \"TimeAgoDisplay.useEffect.updateTimeAgo\": ()=>{\n                    setTimeAgo((0,_src_utils_timeUtils__WEBPACK_IMPORTED_MODULE_8__.formatTimeAgo)(machine.updatedAt));\n                }\n            }[\"TimeAgoDisplay.useEffect.updateTimeAgo\"];\n            updateTimeAgo();\n            // Update every 30 seconds\n            const interval = setInterval(updateTimeAgo, 30000);\n            return ({\n                \"TimeAgoDisplay.useEffect\": ()=>clearInterval(interval)\n            })[\"TimeAgoDisplay.useEffect\"];\n        }\n    }[\"TimeAgoDisplay.useEffect\"], [\n        machine.updatedAt\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-xs text-gray-500 mt-1 text-center\",\n        children: timeAgo\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n_s1(TimeAgoDisplay, \"IBf0cw+KUIsg95VJGn4acX1ZO9o=\");\n_c1 = TimeAgoDisplay;\n// Component for ownership badge\nfunction OwnershipBadge(param) {\n    let { machine } = param;\n    if (!(0,_src_utils_machineOwnership__WEBPACK_IMPORTED_MODULE_9__.hasOwner)(machine)) return null;\n    const ownershipText = (0,_src_utils_machineOwnership__WEBPACK_IMPORTED_MODULE_9__.getOwnershipDisplay)(machine);\n    const badgeClasses = (0,_src_utils_machineOwnership__WEBPACK_IMPORTED_MODULE_9__.getOwnershipBadgeClasses)(machine);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border \".concat(badgeClasses),\n        children: ownershipText\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n_c2 = OwnershipBadge;\n// Component for time adjustment button\nfunction TimeAdjustButton(param) {\n    let { machine, onAdjust } = param;\n    const canAdjust = (0,_src_utils_machineOwnership__WEBPACK_IMPORTED_MODULE_9__.canAdjustTime)(machine);\n    const timeUntilAvailable = (0,_src_utils_machineOwnership__WEBPACK_IMPORTED_MODULE_9__.getTimeUntilAdjustmentAvailable)(machine);\n    // Only show for machines the current user owns and are running\n    if (machine.status !== \"running\" || !(0,_src_utils_machineOwnership__WEBPACK_IMPORTED_MODULE_9__.isCurrentUserOwner)(machine)) {\n        return null;\n    }\n    if (canAdjust) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: onAdjust,\n            className: \"inline-flex items-center gap-1 px-2 py-1 text-xs font-medium text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-colors\",\n            children: \"⏱️ Adjust\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n            lineNumber: 89,\n            columnNumber: 7\n        }, this);\n    }\n    // Show countdown until adjustment is available\n    if (timeUntilAvailable > 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"inline-flex items-center gap-1 px-2 py-1 text-xs font-medium text-gray-500\",\n            children: [\n                \"⏱️ Adjust in \",\n                timeUntilAvailable,\n                \"m\"\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n            lineNumber: 101,\n            columnNumber: 7\n        }, this);\n    }\n    return null;\n}\n_c3 = TimeAdjustButton;\nfunction MachineStatus(param) {\n    let { machine } = param;\n    _s2();\n    const countdown = (0,_src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(machine.endAt, machine.graceEndAt);\n    if (machine.status === \"free\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-green-100 text-green-800 border-2 border-green-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"w-2 h-2 bg-green-500 rounded-full mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, this),\n                    \"Available\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                lineNumber: 116,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n            lineNumber: 115,\n            columnNumber: 7\n        }, this);\n    }\n    if (machine.status === \"running\" && machine.endAt) {\n        const hours = Math.floor(countdown.secondsLeft / 3600);\n        const minutes = Math.floor(countdown.secondsLeft % 3600 / 60);\n        const seconds = countdown.secondsLeft % 60;\n        const isOwner = machine.startedByUserId === (0,_src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_10__.getDeviceUserId)();\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-blue-100 text-blue-800 border-2 border-blue-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"w-2 h-2 bg-blue-500 rounded-full mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, this),\n                    isOwner ? \"In Use\" : \"Busy\",\n                    \" - \",\n                    hours,\n                    \":\",\n                    minutes.toString().padStart(2, \"0\"),\n                    \":\",\n                    seconds.toString().padStart(2, \"0\"),\n                    \" left\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                lineNumber: 132,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n            lineNumber: 131,\n            columnNumber: 7\n        }, this);\n    }\n    if (machine.status === \"finishedGrace\") {\n        let graceTimeDisplay = \"5:00\";\n        const isOwner = machine.startedByUserId === (0,_src_utils_userIdentification__WEBPACK_IMPORTED_MODULE_10__.getDeviceUserId)();\n        if (machine.graceEndAt) {\n            const minutes = Math.floor(countdown.graceSecondsLeft / 60);\n            const seconds = countdown.graceSecondsLeft % 60;\n            graceTimeDisplay = \"\".concat(minutes, \":\").concat(seconds.toString().padStart(2, \"0\"));\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-orange-100 text-orange-800 border-2 border-orange-200 animate-pulse-slow\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"w-2 h-2 bg-orange-500 rounded-full mr-2 animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 11\n                    }, this),\n                    isOwner ? \"Collect items\" : \"Busy\",\n                    \" - \",\n                    graceTimeDisplay\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                lineNumber: 152,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n            lineNumber: 151,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-gray-100 text-gray-800 border-2 border-gray-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"w-2 h-2 bg-gray-500 rounded-full mr-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 9\n                }, this),\n                \"Unknown\"\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n            lineNumber: 162,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, this);\n}\n_s2(MachineStatus, \"u8Q9UI2BbjjlXAW3yY+wFIi4lfA=\", false, function() {\n    return [\n        _src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    ];\n});\n_c4 = MachineStatus;\nfunction LaundryCard() {\n    _s3();\n    const { laundry, incidents, deleteIncident, adjustMachineTime } = (0,_src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const [adjustModalOpen, setAdjustModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedMachine, setSelectedMachine] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Count total incidents for the badge\n    const totalIncidents = incidents.length;\n    // Handle time adjustment\n    const handleAdjustTime = (machine)=>{\n        setSelectedMachine(machine);\n        setAdjustModalOpen(true);\n    };\n    const handleCloseModal = ()=>{\n        setAdjustModalOpen(false);\n        setSelectedMachine(null);\n    };\n    // Custom display names for machines\n    const getDisplayName = (machine)=>{\n        const isWasher = machine.name.toLowerCase().includes(\"washer\");\n        if (isWasher) {\n            // Extract number from washer name (e.g., \"Washer 1\" -> \"1\")\n            const numberMatch = machine.name.match(/\\d+/);\n            const number = numberMatch ? numberMatch[0] : \"\";\n            return \"Washer \".concat(number);\n        } else {\n            // For dryers, use \"Dryer 5\" through \"Dryer 8\" based on original number\n            const numberMatch = machine.name.match(/\\d+/);\n            const originalNumber = numberMatch ? Number.parseInt(numberMatch[0]) : 0;\n            const newNumber = originalNumber + 4 // Map 1->5, 2->6, 3->7, 4->8\n            ;\n            return \"Dryer \".concat(newNumber);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_CardWrapper__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        id: \"laundry-machines\",\n        color: \"white\",\n        className: \"border-l-4 border-accent shadow-lg\",\n        count: totalIncidents,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-4 h-4 bg-accent rounded-full mr-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-primary\",\n                                children: \"\\uD83D\\uDC55 Laundry Machines\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 px-4 py-2 rounded-xl shadow-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 bg-green-500 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-bold text-green-800\",\n                                            children: laundry.filter((m)=>m.status === \"free\").length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"of\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-semibold text-gray-800\",\n                                            children: laundry.length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-green-700\",\n                                            children: \"available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-xs\",\n                                children: [\n                                    laundry.filter((m)=>m.status === \"running\").length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1 bg-blue-50 px-2 py-1 rounded-lg border border-blue-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-blue-700\",\n                                                children: [\n                                                    laundry.filter((m)=>m.status === \"running\").length,\n                                                    \" in use\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, this),\n                                    laundry.filter((m)=>m.status === \"finishedGrace\").length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1 bg-orange-50 px-2 py-1 rounded-lg border border-orange-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-orange-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-orange-700\",\n                                                children: [\n                                                    laundry.filter((m)=>m.status === \"finishedGrace\").length,\n                                                    \" ready\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                lineNumber: 212,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\",\n                children: laundry.map((machine)=>{\n                    var _machine_graceEndAt, _machine_endAt;\n                    const machineIncidents = incidents.filter((inc)=>inc.machineId === machine.id);\n                    const isWasher = machine.name.toLowerCase().includes(\"washer\");\n                    const displayName = getDisplayName(machine);\n                    const recentUpdateClasses = (0,_src_utils_timeUtils__WEBPACK_IMPORTED_MODULE_8__.getRecentUpdateClasses)(machine.updatedAt);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border-2 border-gray-100 rounded-xl p-6 shadow-sm hover:shadow-lg transition-all duration-300 hover:border-accent/30 relative group \".concat(recentUpdateClasses),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(JustUpdatedBadge, {\n                                machine: machine\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 15\n                            }, this),\n                            machineIncidents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-3 -right-3 z-10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_IncidentBadge__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    incidents: machineIncidents,\n                                    onDelete: deleteIncident\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 bg-bgLight rounded-xl group-hover:bg-accent/10 transition-colors duration-300\",\n                                    children: isWasher ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_WasherSVG__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-20 h-20\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 31\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_DryerOutlineSVG__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-20 h-20\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 69\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-bold text-primary text-xl mb-1\",\n                                        children: displayName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TimeAgoDisplay, {\n                                        machine: machine\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OwnershipBadge, {\n                                            machine: machine\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MachineStatus, {\n                                        machine: machine\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 17\n                                    }, this),\n                                    machine.status === \"finishedGrace\" && machine.graceEndAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-orange-600 text-center mt-2 bg-orange-50 py-2 px-3 rounded-lg\",\n                                        children: [\n                                            \"Grace ends: \",\n                                            (_machine_graceEndAt = machine.graceEndAt) === null || _machine_graceEndAt === void 0 ? void 0 : _machine_graceEndAt.toLocaleTimeString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 19\n                                    }, this),\n                                    machine.status === \"running\" && machine.endAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-blue-600 text-center mt-2 bg-blue-50 py-2 px-3 rounded-lg\",\n                                        children: [\n                                            \"Will end: \",\n                                            (_machine_endAt = machine.endAt) === null || _machine_endAt === void 0 ? void 0 : _machine_endAt.toLocaleTimeString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mt-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TimeAdjustButton, {\n                                            machine: machine,\n                                            onAdjust: ()=>handleAdjustTime(machine)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, machine.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                lineNumber: 256,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 pt-6 border-t border-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap justify-center gap-4 text-sm text-gray-600\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Available\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"In Use\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"w-2 h-2 bg-orange-500 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Please Collect\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                    lineNumber: 329,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                lineNumber: 328,\n                columnNumber: 7\n            }, this),\n            selectedMachine && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TimeAdjustmentModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                machine: selectedMachine,\n                isOpen: adjustModalOpen,\n                onClose: handleCloseModal,\n                onAdjust: adjustMachineTime\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n                lineNumber: 347,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\src\\\\components\\\\LaundryCard.tsx\",\n        lineNumber: 210,\n        columnNumber: 5\n    }, this);\n}\n_s3(LaundryCard, \"UUtJ1yuarJ02cO9HpKvLUv8baYU=\", false, function() {\n    return [\n        _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n    ];\n});\n_c5 = LaundryCard;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"JustUpdatedBadge\");\n$RefreshReg$(_c1, \"TimeAgoDisplay\");\n$RefreshReg$(_c2, \"OwnershipBadge\");\n$RefreshReg$(_c3, \"TimeAdjustButton\");\n$RefreshReg$(_c4, \"MachineStatus\");\n$RefreshReg$(_c5, \"LaundryCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/LaundryCard.tsx\n"));

/***/ })

});