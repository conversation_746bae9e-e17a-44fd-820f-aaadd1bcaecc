"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/checkin/page",{

/***/ "(app-pages-browser)/./app/checkin/page.tsx":
/*!******************************!*\
  !*** ./app/checkin/page.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CheckInPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var qrcode_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! qrcode.react */ \"(app-pages-browser)/./node_modules/.pnpm/qrcode.react@4.2.0_react@19.0.0/node_modules/qrcode.react/lib/esm/index.js\");\n/* harmony import */ var _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/src/hooks/useSupabaseData */ \"(app-pages-browser)/./src/hooks/useSupabaseData.tsx\");\n/* harmony import */ var _src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/src/hooks/useCountdown */ \"(app-pages-browser)/./src/hooks/useCountdown.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction CheckInPage() {\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const machineId = searchParams.get(\"machine\");\n    const { laundry, toggleMachineStatus, reserveMachine, isLoading, refreshData } = (0,_src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    const [machine, setMachine] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [actionResult, setActionResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [customTime, setCustomTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"60\");\n    const [useCustomTime, setUseCustomTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const countdown = (0,_src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(machine === null || machine === void 0 ? void 0 : machine.endAt, machine === null || machine === void 0 ? void 0 : machine.graceEndAt);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CheckInPage.useEffect\": ()=>{\n            if (!machineId) return;\n            const foundMachine = laundry.find({\n                \"CheckInPage.useEffect.foundMachine\": (m)=>m.id === machineId\n            }[\"CheckInPage.useEffect.foundMachine\"]);\n            setMachine(foundMachine || null);\n        }\n    }[\"CheckInPage.useEffect\"], [\n        machineId,\n        laundry\n    ]);\n    // Reduced auto-refresh to every 10 seconds to prevent conflicts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CheckInPage.useEffect\": ()=>{\n            const interval = setInterval({\n                \"CheckInPage.useEffect.interval\": ()=>{\n                    refreshData();\n                }\n            }[\"CheckInPage.useEffect.interval\"], 10000);\n            return ({\n                \"CheckInPage.useEffect\": ()=>clearInterval(interval)\n            })[\"CheckInPage.useEffect\"];\n        }\n    }[\"CheckInPage.useEffect\"], [\n        refreshData\n    ]);\n    const handleToggle = async ()=>{\n        if (!machineId || isProcessing) return;\n        setIsProcessing(true);\n        setActionResult(\"\");\n        let success = false;\n        if ((machine === null || machine === void 0 ? void 0 : machine.status) === \"free\" && useCustomTime) {\n            // Use custom time\n            success = await reserveMachine(machineId, \"\".concat(customTime, \" minutes\"));\n        } else {\n            // Use default toggle\n            success = await toggleMachineStatus(machineId);\n        }\n        if (success) {\n            setActionResult(\"Status updated successfully!\");\n            // Single refresh after 2 seconds to confirm the change\n            setTimeout(()=>refreshData(), 2000);\n        } else {\n            setActionResult(\"Error updating machine status\");\n        }\n        setIsProcessing(false);\n    };\n    const getStatusDisplay = ()=>{\n        if (!machine) return \"\";\n        switch(machine.status){\n            case \"free\":\n                return \"🟢 Available\";\n            case \"running\":\n                const hours = Math.floor(countdown.secondsLeft / 3600);\n                const minutes = Math.floor(countdown.secondsLeft % 3600 / 60);\n                const seconds = countdown.secondsLeft % 60;\n                return \"\\uD83D\\uDD35 In Use - \".concat(hours, \":\").concat(minutes.toString().padStart(2, \"0\"), \":\").concat(seconds.toString().padStart(2, \"0\"), \" left\");\n            case \"finishedGrace\":\n                // Calculate grace period time remaining\n                let graceTimeDisplay = \"5:00\";\n                if (machine.graceEndAt) {\n                    const graceMinutes = Math.floor(countdown.graceSecondsLeft / 60);\n                    const graceSeconds = countdown.graceSecondsLeft % 60;\n                    graceTimeDisplay = \"\".concat(graceMinutes, \":\").concat(graceSeconds.toString().padStart(2, \"0\"));\n                }\n                return \"⚠️ Please collect items - \".concat(graceTimeDisplay, \" left\");\n            default:\n                return \"Unknown\";\n        }\n    };\n    const getButtonText = ()=>{\n        if (isProcessing) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 11\n                    }, this),\n                    \"Processing...\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 96,\n                columnNumber: 9\n            }, this);\n        }\n        switch(machine === null || machine === void 0 ? void 0 : machine.status){\n            case \"free\":\n                return useCustomTime ? \"▶️ Start Using Machine (\".concat(customTime, \" min)\") : \"▶️ Start Using Machine\";\n            case \"running\":\n                return \"⏹️ Stop Using Machine\";\n            case \"finishedGrace\":\n                return \"✅ I've Collected My Items\";\n            default:\n                return \"Update Status\";\n        }\n    };\n    const getButtonColor = ()=>{\n        if (isProcessing) return \"bg-gray-400 cursor-not-allowed\";\n        switch(machine === null || machine === void 0 ? void 0 : machine.status){\n            case \"free\":\n                return \"bg-blue-500 hover:bg-blue-600\";\n            case \"running\":\n                return \"bg-red-500 hover:bg-red-600\";\n            case \"finishedGrace\":\n                return \"bg-green-500 hover:bg-green-600\";\n            default:\n                return \"bg-gray-500\";\n        }\n    };\n    const handleBackToHome = ()=>{\n        router.push(\"/\");\n    };\n    if (isLoading && !machine) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-8 rounded-lg shadow text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-lg\",\n                        children: \"Loading machine data...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 137,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 136,\n            columnNumber: 7\n        }, this);\n    }\n    if (!machineId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-8 rounded-lg shadow text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-600 text-lg mb-4\",\n                        children: \"Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600\",\n                        children: \"No machine ID provided.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 148,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 147,\n            columnNumber: 7\n        }, this);\n    }\n    if (!machine) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-8 rounded-lg shadow text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-600 text-lg mb-4\",\n                        children: \"Machine Not Found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"The requested machine could not be found.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: refreshData,\n                        className: \"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded\",\n                        children: \"Retry Loading\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 159,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 158,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white p-8 rounded-lg shadow text-center max-w-md w-full mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-4\",\n                    children: \"Machine Check-In\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-600 mb-3\",\n                            children: \"Scan to access this page\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white p-3 rounded-lg border-2 border-gray-200 shadow-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(qrcode_react__WEBPACK_IMPORTED_MODULE_3__.QRCodeSVG, {\n                                    value: \"\".concat( true ? window.location.origin : 0, \"/checkin?machine=\").concat(machineId),\n                                    size: 80,\n                                    fgColor: \"#1A1F36\",\n                                    bgColor: \"#FFFFFF\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-lg font-medium\",\n                            children: machine.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm mt-1 font-semibold \".concat(machine.status === \"free\" ? \"text-green-600\" : machine.status === \"running\" ? \"text-blue-600\" : machine.status === \"finishedGrace\" ? \"text-orange-600\" : \"text-red-600\", \" \").concat(machine.status === \"finishedGrace\" ? \"animate-pulse\" : \"\"),\n                            children: [\n                                \"Status: \",\n                                getStatusDisplay()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 11\n                        }, this),\n                        machine.status === \"running\" && machine.endAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500 mt-1\",\n                            children: [\n                                \"Ends at: \",\n                                machine.endAt.toLocaleTimeString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 13\n                        }, this),\n                        machine.status === \"finishedGrace\" && machine.graceEndAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-orange-500 mt-1\",\n                            children: [\n                                \"Grace period ends at: \",\n                                machine.graceEndAt.toLocaleTimeString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 9\n                }, this),\n                machine.status === \"free\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    id: \"useCustomTime\",\n                                    checked: useCustomTime,\n                                    onChange: (e)=>setUseCustomTime(e.target.checked),\n                                    className: \"mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"useCustomTime\",\n                                    className: \"text-sm\",\n                                    children: \"Set custom time\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 13\n                        }, this),\n                        useCustomTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"number\",\n                                    value: customTime,\n                                    onChange: (e)=>setCustomTime(e.target.value),\n                                    min: \"1\",\n                                    max: \"120\",\n                                    className: \"border rounded p-2 w-20 text-center mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"minutes\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleToggle,\n                    disabled: isProcessing,\n                    className: \"w-full p-3 rounded text-white font-medium mb-4 \".concat(getButtonColor()),\n                    children: getButtonText()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 249,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: refreshData,\n                    disabled: isLoading,\n                    className: \"w-full p-2 rounded border border-gray-300 text-gray-700 hover:bg-gray-50 mb-4\",\n                    children: \"Refresh Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 9\n                }, this),\n                actionResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm mb-4 p-2 rounded \".concat(actionResult.includes(\"Error\") ? \"bg-red-100 text-red-700\" : \"bg-green-100 text-green-700\"),\n                    children: actionResult\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 266,\n                    columnNumber: 11\n                }, this),\n                machine.status === \"finishedGrace\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-orange-50 border border-orange-200 rounded p-3 text-sm text-orange-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium mb-1\",\n                            children: \"⚠️ Grace Period Active\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Please collect your items within the time limit. The machine will become available automatically when the grace period ends.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 172,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n        lineNumber: 171,\n        columnNumber: 5\n    }, this);\n}\n_s(CheckInPage, \"OH9vb5CWT/aUESbsBQ3iKyTpLvI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        _src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n});\n_c = CheckInPage;\nvar _c;\n$RefreshReg$(_c, \"CheckInPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/checkin/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/qrcode.react@4.2.0_react@19.0.0/node_modules/qrcode.react/lib/esm/index.js":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/qrcode.react@4.2.0_react@19.0.0/node_modules/qrcode.react/lib/esm/index.js ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QRCodeCanvas: () => (/* binding */ QRCodeCanvas),\n/* harmony export */   QRCodeSVG: () => (/* binding */ QRCodeSVG)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\nvar __defProp = Object.defineProperty;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\n\n// src/index.tsx\n\n\n// src/third-party/qrcodegen/index.ts\n/**\n * @license QR Code generator library (TypeScript)\n * Copyright (c) Project Nayuki.\n * SPDX-License-Identifier: MIT\n */\nvar qrcodegen;\n((qrcodegen2) => {\n  const _QrCode = class _QrCode {\n    /*-- Constructor (low level) and fields --*/\n    // Creates a new QR Code with the given version number,\n    // error correction level, data codeword bytes, and mask number.\n    // This is a low-level API that most users should not use directly.\n    // A mid-level API is the encodeSegments() function.\n    constructor(version, errorCorrectionLevel, dataCodewords, msk) {\n      this.version = version;\n      this.errorCorrectionLevel = errorCorrectionLevel;\n      // The modules of this QR Code (false = light, true = dark).\n      // Immutable after constructor finishes. Accessed through getModule().\n      this.modules = [];\n      // Indicates function modules that are not subjected to masking. Discarded when constructor finishes.\n      this.isFunction = [];\n      if (version < _QrCode.MIN_VERSION || version > _QrCode.MAX_VERSION)\n        throw new RangeError(\"Version value out of range\");\n      if (msk < -1 || msk > 7)\n        throw new RangeError(\"Mask value out of range\");\n      this.size = version * 4 + 17;\n      let row = [];\n      for (let i = 0; i < this.size; i++)\n        row.push(false);\n      for (let i = 0; i < this.size; i++) {\n        this.modules.push(row.slice());\n        this.isFunction.push(row.slice());\n      }\n      this.drawFunctionPatterns();\n      const allCodewords = this.addEccAndInterleave(dataCodewords);\n      this.drawCodewords(allCodewords);\n      if (msk == -1) {\n        let minPenalty = 1e9;\n        for (let i = 0; i < 8; i++) {\n          this.applyMask(i);\n          this.drawFormatBits(i);\n          const penalty = this.getPenaltyScore();\n          if (penalty < minPenalty) {\n            msk = i;\n            minPenalty = penalty;\n          }\n          this.applyMask(i);\n        }\n      }\n      assert(0 <= msk && msk <= 7);\n      this.mask = msk;\n      this.applyMask(msk);\n      this.drawFormatBits(msk);\n      this.isFunction = [];\n    }\n    /*-- Static factory functions (high level) --*/\n    // Returns a QR Code representing the given Unicode text string at the given error correction level.\n    // As a conservative upper bound, this function is guaranteed to succeed for strings that have 738 or fewer\n    // Unicode code points (not UTF-16 code units) if the low error correction level is used. The smallest possible\n    // QR Code version is automatically chosen for the output. The ECC level of the result may be higher than the\n    // ecl argument if it can be done without increasing the version.\n    static encodeText(text, ecl) {\n      const segs = qrcodegen2.QrSegment.makeSegments(text);\n      return _QrCode.encodeSegments(segs, ecl);\n    }\n    // Returns a QR Code representing the given binary data at the given error correction level.\n    // This function always encodes using the binary segment mode, not any text mode. The maximum number of\n    // bytes allowed is 2953. The smallest possible QR Code version is automatically chosen for the output.\n    // The ECC level of the result may be higher than the ecl argument if it can be done without increasing the version.\n    static encodeBinary(data, ecl) {\n      const seg = qrcodegen2.QrSegment.makeBytes(data);\n      return _QrCode.encodeSegments([seg], ecl);\n    }\n    /*-- Static factory functions (mid level) --*/\n    // Returns a QR Code representing the given segments with the given encoding parameters.\n    // The smallest possible QR Code version within the given range is automatically\n    // chosen for the output. Iff boostEcl is true, then the ECC level of the result\n    // may be higher than the ecl argument if it can be done without increasing the\n    // version. The mask number is either between 0 to 7 (inclusive) to force that\n    // mask, or -1 to automatically choose an appropriate mask (which may be slow).\n    // This function allows the user to create a custom sequence of segments that switches\n    // between modes (such as alphanumeric and byte) to encode text in less space.\n    // This is a mid-level API; the high-level API is encodeText() and encodeBinary().\n    static encodeSegments(segs, ecl, minVersion = 1, maxVersion = 40, mask = -1, boostEcl = true) {\n      if (!(_QrCode.MIN_VERSION <= minVersion && minVersion <= maxVersion && maxVersion <= _QrCode.MAX_VERSION) || mask < -1 || mask > 7)\n        throw new RangeError(\"Invalid value\");\n      let version;\n      let dataUsedBits;\n      for (version = minVersion; ; version++) {\n        const dataCapacityBits2 = _QrCode.getNumDataCodewords(version, ecl) * 8;\n        const usedBits = QrSegment.getTotalBits(segs, version);\n        if (usedBits <= dataCapacityBits2) {\n          dataUsedBits = usedBits;\n          break;\n        }\n        if (version >= maxVersion)\n          throw new RangeError(\"Data too long\");\n      }\n      for (const newEcl of [_QrCode.Ecc.MEDIUM, _QrCode.Ecc.QUARTILE, _QrCode.Ecc.HIGH]) {\n        if (boostEcl && dataUsedBits <= _QrCode.getNumDataCodewords(version, newEcl) * 8)\n          ecl = newEcl;\n      }\n      let bb = [];\n      for (const seg of segs) {\n        appendBits(seg.mode.modeBits, 4, bb);\n        appendBits(seg.numChars, seg.mode.numCharCountBits(version), bb);\n        for (const b of seg.getData())\n          bb.push(b);\n      }\n      assert(bb.length == dataUsedBits);\n      const dataCapacityBits = _QrCode.getNumDataCodewords(version, ecl) * 8;\n      assert(bb.length <= dataCapacityBits);\n      appendBits(0, Math.min(4, dataCapacityBits - bb.length), bb);\n      appendBits(0, (8 - bb.length % 8) % 8, bb);\n      assert(bb.length % 8 == 0);\n      for (let padByte = 236; bb.length < dataCapacityBits; padByte ^= 236 ^ 17)\n        appendBits(padByte, 8, bb);\n      let dataCodewords = [];\n      while (dataCodewords.length * 8 < bb.length)\n        dataCodewords.push(0);\n      bb.forEach((b, i) => dataCodewords[i >>> 3] |= b << 7 - (i & 7));\n      return new _QrCode(version, ecl, dataCodewords, mask);\n    }\n    /*-- Accessor methods --*/\n    // Returns the color of the module (pixel) at the given coordinates, which is false\n    // for light or true for dark. The top left corner has the coordinates (x=0, y=0).\n    // If the given coordinates are out of bounds, then false (light) is returned.\n    getModule(x, y) {\n      return 0 <= x && x < this.size && 0 <= y && y < this.size && this.modules[y][x];\n    }\n    // Modified to expose modules for easy access\n    getModules() {\n      return this.modules;\n    }\n    /*-- Private helper methods for constructor: Drawing function modules --*/\n    // Reads this object's version field, and draws and marks all function modules.\n    drawFunctionPatterns() {\n      for (let i = 0; i < this.size; i++) {\n        this.setFunctionModule(6, i, i % 2 == 0);\n        this.setFunctionModule(i, 6, i % 2 == 0);\n      }\n      this.drawFinderPattern(3, 3);\n      this.drawFinderPattern(this.size - 4, 3);\n      this.drawFinderPattern(3, this.size - 4);\n      const alignPatPos = this.getAlignmentPatternPositions();\n      const numAlign = alignPatPos.length;\n      for (let i = 0; i < numAlign; i++) {\n        for (let j = 0; j < numAlign; j++) {\n          if (!(i == 0 && j == 0 || i == 0 && j == numAlign - 1 || i == numAlign - 1 && j == 0))\n            this.drawAlignmentPattern(alignPatPos[i], alignPatPos[j]);\n        }\n      }\n      this.drawFormatBits(0);\n      this.drawVersion();\n    }\n    // Draws two copies of the format bits (with its own error correction code)\n    // based on the given mask and this object's error correction level field.\n    drawFormatBits(mask) {\n      const data = this.errorCorrectionLevel.formatBits << 3 | mask;\n      let rem = data;\n      for (let i = 0; i < 10; i++)\n        rem = rem << 1 ^ (rem >>> 9) * 1335;\n      const bits = (data << 10 | rem) ^ 21522;\n      assert(bits >>> 15 == 0);\n      for (let i = 0; i <= 5; i++)\n        this.setFunctionModule(8, i, getBit(bits, i));\n      this.setFunctionModule(8, 7, getBit(bits, 6));\n      this.setFunctionModule(8, 8, getBit(bits, 7));\n      this.setFunctionModule(7, 8, getBit(bits, 8));\n      for (let i = 9; i < 15; i++)\n        this.setFunctionModule(14 - i, 8, getBit(bits, i));\n      for (let i = 0; i < 8; i++)\n        this.setFunctionModule(this.size - 1 - i, 8, getBit(bits, i));\n      for (let i = 8; i < 15; i++)\n        this.setFunctionModule(8, this.size - 15 + i, getBit(bits, i));\n      this.setFunctionModule(8, this.size - 8, true);\n    }\n    // Draws two copies of the version bits (with its own error correction code),\n    // based on this object's version field, iff 7 <= version <= 40.\n    drawVersion() {\n      if (this.version < 7)\n        return;\n      let rem = this.version;\n      for (let i = 0; i < 12; i++)\n        rem = rem << 1 ^ (rem >>> 11) * 7973;\n      const bits = this.version << 12 | rem;\n      assert(bits >>> 18 == 0);\n      for (let i = 0; i < 18; i++) {\n        const color = getBit(bits, i);\n        const a = this.size - 11 + i % 3;\n        const b = Math.floor(i / 3);\n        this.setFunctionModule(a, b, color);\n        this.setFunctionModule(b, a, color);\n      }\n    }\n    // Draws a 9*9 finder pattern including the border separator,\n    // with the center module at (x, y). Modules can be out of bounds.\n    drawFinderPattern(x, y) {\n      for (let dy = -4; dy <= 4; dy++) {\n        for (let dx = -4; dx <= 4; dx++) {\n          const dist = Math.max(Math.abs(dx), Math.abs(dy));\n          const xx = x + dx;\n          const yy = y + dy;\n          if (0 <= xx && xx < this.size && 0 <= yy && yy < this.size)\n            this.setFunctionModule(xx, yy, dist != 2 && dist != 4);\n        }\n      }\n    }\n    // Draws a 5*5 alignment pattern, with the center module\n    // at (x, y). All modules must be in bounds.\n    drawAlignmentPattern(x, y) {\n      for (let dy = -2; dy <= 2; dy++) {\n        for (let dx = -2; dx <= 2; dx++)\n          this.setFunctionModule(x + dx, y + dy, Math.max(Math.abs(dx), Math.abs(dy)) != 1);\n      }\n    }\n    // Sets the color of a module and marks it as a function module.\n    // Only used by the constructor. Coordinates must be in bounds.\n    setFunctionModule(x, y, isDark) {\n      this.modules[y][x] = isDark;\n      this.isFunction[y][x] = true;\n    }\n    /*-- Private helper methods for constructor: Codewords and masking --*/\n    // Returns a new byte string representing the given data with the appropriate error correction\n    // codewords appended to it, based on this object's version and error correction level.\n    addEccAndInterleave(data) {\n      const ver = this.version;\n      const ecl = this.errorCorrectionLevel;\n      if (data.length != _QrCode.getNumDataCodewords(ver, ecl))\n        throw new RangeError(\"Invalid argument\");\n      const numBlocks = _QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];\n      const blockEccLen = _QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver];\n      const rawCodewords = Math.floor(_QrCode.getNumRawDataModules(ver) / 8);\n      const numShortBlocks = numBlocks - rawCodewords % numBlocks;\n      const shortBlockLen = Math.floor(rawCodewords / numBlocks);\n      let blocks = [];\n      const rsDiv = _QrCode.reedSolomonComputeDivisor(blockEccLen);\n      for (let i = 0, k = 0; i < numBlocks; i++) {\n        let dat = data.slice(k, k + shortBlockLen - blockEccLen + (i < numShortBlocks ? 0 : 1));\n        k += dat.length;\n        const ecc = _QrCode.reedSolomonComputeRemainder(dat, rsDiv);\n        if (i < numShortBlocks)\n          dat.push(0);\n        blocks.push(dat.concat(ecc));\n      }\n      let result = [];\n      for (let i = 0; i < blocks[0].length; i++) {\n        blocks.forEach((block, j) => {\n          if (i != shortBlockLen - blockEccLen || j >= numShortBlocks)\n            result.push(block[i]);\n        });\n      }\n      assert(result.length == rawCodewords);\n      return result;\n    }\n    // Draws the given sequence of 8-bit codewords (data and error correction) onto the entire\n    // data area of this QR Code. Function modules need to be marked off before this is called.\n    drawCodewords(data) {\n      if (data.length != Math.floor(_QrCode.getNumRawDataModules(this.version) / 8))\n        throw new RangeError(\"Invalid argument\");\n      let i = 0;\n      for (let right = this.size - 1; right >= 1; right -= 2) {\n        if (right == 6)\n          right = 5;\n        for (let vert = 0; vert < this.size; vert++) {\n          for (let j = 0; j < 2; j++) {\n            const x = right - j;\n            const upward = (right + 1 & 2) == 0;\n            const y = upward ? this.size - 1 - vert : vert;\n            if (!this.isFunction[y][x] && i < data.length * 8) {\n              this.modules[y][x] = getBit(data[i >>> 3], 7 - (i & 7));\n              i++;\n            }\n          }\n        }\n      }\n      assert(i == data.length * 8);\n    }\n    // XORs the codeword modules in this QR Code with the given mask pattern.\n    // The function modules must be marked and the codeword bits must be drawn\n    // before masking. Due to the arithmetic of XOR, calling applyMask() with\n    // the same mask value a second time will undo the mask. A final well-formed\n    // QR Code needs exactly one (not zero, two, etc.) mask applied.\n    applyMask(mask) {\n      if (mask < 0 || mask > 7)\n        throw new RangeError(\"Mask value out of range\");\n      for (let y = 0; y < this.size; y++) {\n        for (let x = 0; x < this.size; x++) {\n          let invert;\n          switch (mask) {\n            case 0:\n              invert = (x + y) % 2 == 0;\n              break;\n            case 1:\n              invert = y % 2 == 0;\n              break;\n            case 2:\n              invert = x % 3 == 0;\n              break;\n            case 3:\n              invert = (x + y) % 3 == 0;\n              break;\n            case 4:\n              invert = (Math.floor(x / 3) + Math.floor(y / 2)) % 2 == 0;\n              break;\n            case 5:\n              invert = x * y % 2 + x * y % 3 == 0;\n              break;\n            case 6:\n              invert = (x * y % 2 + x * y % 3) % 2 == 0;\n              break;\n            case 7:\n              invert = ((x + y) % 2 + x * y % 3) % 2 == 0;\n              break;\n            default:\n              throw new Error(\"Unreachable\");\n          }\n          if (!this.isFunction[y][x] && invert)\n            this.modules[y][x] = !this.modules[y][x];\n        }\n      }\n    }\n    // Calculates and returns the penalty score based on state of this QR Code's current modules.\n    // This is used by the automatic mask choice algorithm to find the mask pattern that yields the lowest score.\n    getPenaltyScore() {\n      let result = 0;\n      for (let y = 0; y < this.size; y++) {\n        let runColor = false;\n        let runX = 0;\n        let runHistory = [0, 0, 0, 0, 0, 0, 0];\n        for (let x = 0; x < this.size; x++) {\n          if (this.modules[y][x] == runColor) {\n            runX++;\n            if (runX == 5)\n              result += _QrCode.PENALTY_N1;\n            else if (runX > 5)\n              result++;\n          } else {\n            this.finderPenaltyAddHistory(runX, runHistory);\n            if (!runColor)\n              result += this.finderPenaltyCountPatterns(runHistory) * _QrCode.PENALTY_N3;\n            runColor = this.modules[y][x];\n            runX = 1;\n          }\n        }\n        result += this.finderPenaltyTerminateAndCount(runColor, runX, runHistory) * _QrCode.PENALTY_N3;\n      }\n      for (let x = 0; x < this.size; x++) {\n        let runColor = false;\n        let runY = 0;\n        let runHistory = [0, 0, 0, 0, 0, 0, 0];\n        for (let y = 0; y < this.size; y++) {\n          if (this.modules[y][x] == runColor) {\n            runY++;\n            if (runY == 5)\n              result += _QrCode.PENALTY_N1;\n            else if (runY > 5)\n              result++;\n          } else {\n            this.finderPenaltyAddHistory(runY, runHistory);\n            if (!runColor)\n              result += this.finderPenaltyCountPatterns(runHistory) * _QrCode.PENALTY_N3;\n            runColor = this.modules[y][x];\n            runY = 1;\n          }\n        }\n        result += this.finderPenaltyTerminateAndCount(runColor, runY, runHistory) * _QrCode.PENALTY_N3;\n      }\n      for (let y = 0; y < this.size - 1; y++) {\n        for (let x = 0; x < this.size - 1; x++) {\n          const color = this.modules[y][x];\n          if (color == this.modules[y][x + 1] && color == this.modules[y + 1][x] && color == this.modules[y + 1][x + 1])\n            result += _QrCode.PENALTY_N2;\n        }\n      }\n      let dark = 0;\n      for (const row of this.modules)\n        dark = row.reduce((sum, color) => sum + (color ? 1 : 0), dark);\n      const total = this.size * this.size;\n      const k = Math.ceil(Math.abs(dark * 20 - total * 10) / total) - 1;\n      assert(0 <= k && k <= 9);\n      result += k * _QrCode.PENALTY_N4;\n      assert(0 <= result && result <= 2568888);\n      return result;\n    }\n    /*-- Private helper functions --*/\n    // Returns an ascending list of positions of alignment patterns for this version number.\n    // Each position is in the range [0,177), and are used on both the x and y axes.\n    // This could be implemented as lookup table of 40 variable-length lists of integers.\n    getAlignmentPatternPositions() {\n      if (this.version == 1)\n        return [];\n      else {\n        const numAlign = Math.floor(this.version / 7) + 2;\n        const step = this.version == 32 ? 26 : Math.ceil((this.version * 4 + 4) / (numAlign * 2 - 2)) * 2;\n        let result = [6];\n        for (let pos = this.size - 7; result.length < numAlign; pos -= step)\n          result.splice(1, 0, pos);\n        return result;\n      }\n    }\n    // Returns the number of data bits that can be stored in a QR Code of the given version number, after\n    // all function modules are excluded. This includes remainder bits, so it might not be a multiple of 8.\n    // The result is in the range [208, 29648]. This could be implemented as a 40-entry lookup table.\n    static getNumRawDataModules(ver) {\n      if (ver < _QrCode.MIN_VERSION || ver > _QrCode.MAX_VERSION)\n        throw new RangeError(\"Version number out of range\");\n      let result = (16 * ver + 128) * ver + 64;\n      if (ver >= 2) {\n        const numAlign = Math.floor(ver / 7) + 2;\n        result -= (25 * numAlign - 10) * numAlign - 55;\n        if (ver >= 7)\n          result -= 36;\n      }\n      assert(208 <= result && result <= 29648);\n      return result;\n    }\n    // Returns the number of 8-bit data (i.e. not error correction) codewords contained in any\n    // QR Code of the given version number and error correction level, with remainder bits discarded.\n    // This stateless pure function could be implemented as a (40*4)-cell lookup table.\n    static getNumDataCodewords(ver, ecl) {\n      return Math.floor(_QrCode.getNumRawDataModules(ver) / 8) - _QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver] * _QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];\n    }\n    // Returns a Reed-Solomon ECC generator polynomial for the given degree. This could be\n    // implemented as a lookup table over all possible parameter values, instead of as an algorithm.\n    static reedSolomonComputeDivisor(degree) {\n      if (degree < 1 || degree > 255)\n        throw new RangeError(\"Degree out of range\");\n      let result = [];\n      for (let i = 0; i < degree - 1; i++)\n        result.push(0);\n      result.push(1);\n      let root = 1;\n      for (let i = 0; i < degree; i++) {\n        for (let j = 0; j < result.length; j++) {\n          result[j] = _QrCode.reedSolomonMultiply(result[j], root);\n          if (j + 1 < result.length)\n            result[j] ^= result[j + 1];\n        }\n        root = _QrCode.reedSolomonMultiply(root, 2);\n      }\n      return result;\n    }\n    // Returns the Reed-Solomon error correction codeword for the given data and divisor polynomials.\n    static reedSolomonComputeRemainder(data, divisor) {\n      let result = divisor.map((_) => 0);\n      for (const b of data) {\n        const factor = b ^ result.shift();\n        result.push(0);\n        divisor.forEach((coef, i) => result[i] ^= _QrCode.reedSolomonMultiply(coef, factor));\n      }\n      return result;\n    }\n    // Returns the product of the two given field elements modulo GF(2^8/0x11D). The arguments and result\n    // are unsigned 8-bit integers. This could be implemented as a lookup table of 256*256 entries of uint8.\n    static reedSolomonMultiply(x, y) {\n      if (x >>> 8 != 0 || y >>> 8 != 0)\n        throw new RangeError(\"Byte out of range\");\n      let z = 0;\n      for (let i = 7; i >= 0; i--) {\n        z = z << 1 ^ (z >>> 7) * 285;\n        z ^= (y >>> i & 1) * x;\n      }\n      assert(z >>> 8 == 0);\n      return z;\n    }\n    // Can only be called immediately after a light run is added, and\n    // returns either 0, 1, or 2. A helper function for getPenaltyScore().\n    finderPenaltyCountPatterns(runHistory) {\n      const n = runHistory[1];\n      assert(n <= this.size * 3);\n      const core = n > 0 && runHistory[2] == n && runHistory[3] == n * 3 && runHistory[4] == n && runHistory[5] == n;\n      return (core && runHistory[0] >= n * 4 && runHistory[6] >= n ? 1 : 0) + (core && runHistory[6] >= n * 4 && runHistory[0] >= n ? 1 : 0);\n    }\n    // Must be called at the end of a line (row or column) of modules. A helper function for getPenaltyScore().\n    finderPenaltyTerminateAndCount(currentRunColor, currentRunLength, runHistory) {\n      if (currentRunColor) {\n        this.finderPenaltyAddHistory(currentRunLength, runHistory);\n        currentRunLength = 0;\n      }\n      currentRunLength += this.size;\n      this.finderPenaltyAddHistory(currentRunLength, runHistory);\n      return this.finderPenaltyCountPatterns(runHistory);\n    }\n    // Pushes the given value to the front and drops the last value. A helper function for getPenaltyScore().\n    finderPenaltyAddHistory(currentRunLength, runHistory) {\n      if (runHistory[0] == 0)\n        currentRunLength += this.size;\n      runHistory.pop();\n      runHistory.unshift(currentRunLength);\n    }\n  };\n  /*-- Constants and tables --*/\n  // The minimum version number supported in the QR Code Model 2 standard.\n  _QrCode.MIN_VERSION = 1;\n  // The maximum version number supported in the QR Code Model 2 standard.\n  _QrCode.MAX_VERSION = 40;\n  // For use in getPenaltyScore(), when evaluating which mask is best.\n  _QrCode.PENALTY_N1 = 3;\n  _QrCode.PENALTY_N2 = 3;\n  _QrCode.PENALTY_N3 = 40;\n  _QrCode.PENALTY_N4 = 10;\n  _QrCode.ECC_CODEWORDS_PER_BLOCK = [\n    // Version: (note that index 0 is for padding, and is set to an illegal value)\n    //0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40    Error correction level\n    [-1, 7, 10, 15, 20, 26, 18, 20, 24, 30, 18, 20, 24, 26, 30, 22, 24, 28, 30, 28, 28, 28, 28, 30, 30, 26, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],\n    // Low\n    [-1, 10, 16, 26, 18, 24, 16, 18, 22, 22, 26, 30, 22, 22, 24, 24, 28, 28, 26, 26, 26, 26, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28],\n    // Medium\n    [-1, 13, 22, 18, 26, 18, 24, 18, 22, 20, 24, 28, 26, 24, 20, 30, 24, 28, 28, 26, 30, 28, 30, 30, 30, 30, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],\n    // Quartile\n    [-1, 17, 28, 22, 16, 22, 28, 26, 26, 24, 28, 24, 28, 22, 24, 24, 30, 28, 28, 26, 28, 30, 24, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30]\n    // High\n  ];\n  _QrCode.NUM_ERROR_CORRECTION_BLOCKS = [\n    // Version: (note that index 0 is for padding, and is set to an illegal value)\n    //0, 1, 2, 3, 4, 5, 6, 7, 8, 9,10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40    Error correction level\n    [-1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 4, 4, 4, 4, 4, 6, 6, 6, 6, 7, 8, 8, 9, 9, 10, 12, 12, 12, 13, 14, 15, 16, 17, 18, 19, 19, 20, 21, 22, 24, 25],\n    // Low\n    [-1, 1, 1, 1, 2, 2, 4, 4, 4, 5, 5, 5, 8, 9, 9, 10, 10, 11, 13, 14, 16, 17, 17, 18, 20, 21, 23, 25, 26, 28, 29, 31, 33, 35, 37, 38, 40, 43, 45, 47, 49],\n    // Medium\n    [-1, 1, 1, 2, 2, 4, 4, 6, 6, 8, 8, 8, 10, 12, 16, 12, 17, 16, 18, 21, 20, 23, 23, 25, 27, 29, 34, 34, 35, 38, 40, 43, 45, 48, 51, 53, 56, 59, 62, 65, 68],\n    // Quartile\n    [-1, 1, 1, 2, 4, 4, 4, 5, 6, 8, 8, 11, 11, 16, 16, 18, 16, 19, 21, 25, 25, 25, 34, 30, 32, 35, 37, 40, 42, 45, 48, 51, 54, 57, 60, 63, 66, 70, 74, 77, 81]\n    // High\n  ];\n  let QrCode = _QrCode;\n  qrcodegen2.QrCode = _QrCode;\n  function appendBits(val, len, bb) {\n    if (len < 0 || len > 31 || val >>> len != 0)\n      throw new RangeError(\"Value out of range\");\n    for (let i = len - 1; i >= 0; i--)\n      bb.push(val >>> i & 1);\n  }\n  function getBit(x, i) {\n    return (x >>> i & 1) != 0;\n  }\n  function assert(cond) {\n    if (!cond)\n      throw new Error(\"Assertion error\");\n  }\n  const _QrSegment = class _QrSegment {\n    /*-- Constructor (low level) and fields --*/\n    // Creates a new QR Code segment with the given attributes and data.\n    // The character count (numChars) must agree with the mode and the bit buffer length,\n    // but the constraint isn't checked. The given bit buffer is cloned and stored.\n    constructor(mode, numChars, bitData) {\n      this.mode = mode;\n      this.numChars = numChars;\n      this.bitData = bitData;\n      if (numChars < 0)\n        throw new RangeError(\"Invalid argument\");\n      this.bitData = bitData.slice();\n    }\n    /*-- Static factory functions (mid level) --*/\n    // Returns a segment representing the given binary data encoded in\n    // byte mode. All input byte arrays are acceptable. Any text string\n    // can be converted to UTF-8 bytes and encoded as a byte mode segment.\n    static makeBytes(data) {\n      let bb = [];\n      for (const b of data)\n        appendBits(b, 8, bb);\n      return new _QrSegment(_QrSegment.Mode.BYTE, data.length, bb);\n    }\n    // Returns a segment representing the given string of decimal digits encoded in numeric mode.\n    static makeNumeric(digits) {\n      if (!_QrSegment.isNumeric(digits))\n        throw new RangeError(\"String contains non-numeric characters\");\n      let bb = [];\n      for (let i = 0; i < digits.length; ) {\n        const n = Math.min(digits.length - i, 3);\n        appendBits(parseInt(digits.substring(i, i + n), 10), n * 3 + 1, bb);\n        i += n;\n      }\n      return new _QrSegment(_QrSegment.Mode.NUMERIC, digits.length, bb);\n    }\n    // Returns a segment representing the given text string encoded in alphanumeric mode.\n    // The characters allowed are: 0 to 9, A to Z (uppercase only), space,\n    // dollar, percent, asterisk, plus, hyphen, period, slash, colon.\n    static makeAlphanumeric(text) {\n      if (!_QrSegment.isAlphanumeric(text))\n        throw new RangeError(\"String contains unencodable characters in alphanumeric mode\");\n      let bb = [];\n      let i;\n      for (i = 0; i + 2 <= text.length; i += 2) {\n        let temp = _QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)) * 45;\n        temp += _QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i + 1));\n        appendBits(temp, 11, bb);\n      }\n      if (i < text.length)\n        appendBits(_QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)), 6, bb);\n      return new _QrSegment(_QrSegment.Mode.ALPHANUMERIC, text.length, bb);\n    }\n    // Returns a new mutable list of zero or more segments to represent the given Unicode text string.\n    // The result may use various segment modes and switch modes to optimize the length of the bit stream.\n    static makeSegments(text) {\n      if (text == \"\")\n        return [];\n      else if (_QrSegment.isNumeric(text))\n        return [_QrSegment.makeNumeric(text)];\n      else if (_QrSegment.isAlphanumeric(text))\n        return [_QrSegment.makeAlphanumeric(text)];\n      else\n        return [_QrSegment.makeBytes(_QrSegment.toUtf8ByteArray(text))];\n    }\n    // Returns a segment representing an Extended Channel Interpretation\n    // (ECI) designator with the given assignment value.\n    static makeEci(assignVal) {\n      let bb = [];\n      if (assignVal < 0)\n        throw new RangeError(\"ECI assignment value out of range\");\n      else if (assignVal < 1 << 7)\n        appendBits(assignVal, 8, bb);\n      else if (assignVal < 1 << 14) {\n        appendBits(2, 2, bb);\n        appendBits(assignVal, 14, bb);\n      } else if (assignVal < 1e6) {\n        appendBits(6, 3, bb);\n        appendBits(assignVal, 21, bb);\n      } else\n        throw new RangeError(\"ECI assignment value out of range\");\n      return new _QrSegment(_QrSegment.Mode.ECI, 0, bb);\n    }\n    // Tests whether the given string can be encoded as a segment in numeric mode.\n    // A string is encodable iff each character is in the range 0 to 9.\n    static isNumeric(text) {\n      return _QrSegment.NUMERIC_REGEX.test(text);\n    }\n    // Tests whether the given string can be encoded as a segment in alphanumeric mode.\n    // A string is encodable iff each character is in the following set: 0 to 9, A to Z\n    // (uppercase only), space, dollar, percent, asterisk, plus, hyphen, period, slash, colon.\n    static isAlphanumeric(text) {\n      return _QrSegment.ALPHANUMERIC_REGEX.test(text);\n    }\n    /*-- Methods --*/\n    // Returns a new copy of the data bits of this segment.\n    getData() {\n      return this.bitData.slice();\n    }\n    // (Package-private) Calculates and returns the number of bits needed to encode the given segments at\n    // the given version. The result is infinity if a segment has too many characters to fit its length field.\n    static getTotalBits(segs, version) {\n      let result = 0;\n      for (const seg of segs) {\n        const ccbits = seg.mode.numCharCountBits(version);\n        if (seg.numChars >= 1 << ccbits)\n          return Infinity;\n        result += 4 + ccbits + seg.bitData.length;\n      }\n      return result;\n    }\n    // Returns a new array of bytes representing the given string encoded in UTF-8.\n    static toUtf8ByteArray(str) {\n      str = encodeURI(str);\n      let result = [];\n      for (let i = 0; i < str.length; i++) {\n        if (str.charAt(i) != \"%\")\n          result.push(str.charCodeAt(i));\n        else {\n          result.push(parseInt(str.substring(i + 1, i + 3), 16));\n          i += 2;\n        }\n      }\n      return result;\n    }\n  };\n  /*-- Constants --*/\n  // Describes precisely all strings that are encodable in numeric mode.\n  _QrSegment.NUMERIC_REGEX = /^[0-9]*$/;\n  // Describes precisely all strings that are encodable in alphanumeric mode.\n  _QrSegment.ALPHANUMERIC_REGEX = /^[A-Z0-9 $%*+.\\/:-]*$/;\n  // The set of all legal characters in alphanumeric mode,\n  // where each character value maps to the index in the string.\n  _QrSegment.ALPHANUMERIC_CHARSET = \"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:\";\n  let QrSegment = _QrSegment;\n  qrcodegen2.QrSegment = _QrSegment;\n})(qrcodegen || (qrcodegen = {}));\n((qrcodegen2) => {\n  let QrCode;\n  ((QrCode2) => {\n    const _Ecc = class _Ecc {\n      // The QR Code can tolerate about 30% erroneous codewords\n      /*-- Constructor and fields --*/\n      constructor(ordinal, formatBits) {\n        this.ordinal = ordinal;\n        this.formatBits = formatBits;\n      }\n    };\n    /*-- Constants --*/\n    _Ecc.LOW = new _Ecc(0, 1);\n    // The QR Code can tolerate about  7% erroneous codewords\n    _Ecc.MEDIUM = new _Ecc(1, 0);\n    // The QR Code can tolerate about 15% erroneous codewords\n    _Ecc.QUARTILE = new _Ecc(2, 3);\n    // The QR Code can tolerate about 25% erroneous codewords\n    _Ecc.HIGH = new _Ecc(3, 2);\n    let Ecc = _Ecc;\n    QrCode2.Ecc = _Ecc;\n  })(QrCode = qrcodegen2.QrCode || (qrcodegen2.QrCode = {}));\n})(qrcodegen || (qrcodegen = {}));\n((qrcodegen2) => {\n  let QrSegment;\n  ((QrSegment2) => {\n    const _Mode = class _Mode {\n      /*-- Constructor and fields --*/\n      constructor(modeBits, numBitsCharCount) {\n        this.modeBits = modeBits;\n        this.numBitsCharCount = numBitsCharCount;\n      }\n      /*-- Method --*/\n      // (Package-private) Returns the bit width of the character count field for a segment in\n      // this mode in a QR Code at the given version number. The result is in the range [0, 16].\n      numCharCountBits(ver) {\n        return this.numBitsCharCount[Math.floor((ver + 7) / 17)];\n      }\n    };\n    /*-- Constants --*/\n    _Mode.NUMERIC = new _Mode(1, [10, 12, 14]);\n    _Mode.ALPHANUMERIC = new _Mode(2, [9, 11, 13]);\n    _Mode.BYTE = new _Mode(4, [8, 16, 16]);\n    _Mode.KANJI = new _Mode(8, [8, 10, 12]);\n    _Mode.ECI = new _Mode(7, [0, 0, 0]);\n    let Mode = _Mode;\n    QrSegment2.Mode = _Mode;\n  })(QrSegment = qrcodegen2.QrSegment || (qrcodegen2.QrSegment = {}));\n})(qrcodegen || (qrcodegen = {}));\nvar qrcodegen_default = qrcodegen;\n\n// src/index.tsx\n/**\n * @license qrcode.react\n * Copyright (c) Paul O'Shannessy\n * SPDX-License-Identifier: ISC\n */\nvar ERROR_LEVEL_MAP = {\n  L: qrcodegen_default.QrCode.Ecc.LOW,\n  M: qrcodegen_default.QrCode.Ecc.MEDIUM,\n  Q: qrcodegen_default.QrCode.Ecc.QUARTILE,\n  H: qrcodegen_default.QrCode.Ecc.HIGH\n};\nvar DEFAULT_SIZE = 128;\nvar DEFAULT_LEVEL = \"L\";\nvar DEFAULT_BGCOLOR = \"#FFFFFF\";\nvar DEFAULT_FGCOLOR = \"#000000\";\nvar DEFAULT_INCLUDEMARGIN = false;\nvar DEFAULT_MINVERSION = 1;\nvar SPEC_MARGIN_SIZE = 4;\nvar DEFAULT_MARGIN_SIZE = 0;\nvar DEFAULT_IMG_SCALE = 0.1;\nfunction generatePath(modules, margin = 0) {\n  const ops = [];\n  modules.forEach(function(row, y) {\n    let start = null;\n    row.forEach(function(cell, x) {\n      if (!cell && start !== null) {\n        ops.push(\n          `M${start + margin} ${y + margin}h${x - start}v1H${start + margin}z`\n        );\n        start = null;\n        return;\n      }\n      if (x === row.length - 1) {\n        if (!cell) {\n          return;\n        }\n        if (start === null) {\n          ops.push(`M${x + margin},${y + margin} h1v1H${x + margin}z`);\n        } else {\n          ops.push(\n            `M${start + margin},${y + margin} h${x + 1 - start}v1H${start + margin}z`\n          );\n        }\n        return;\n      }\n      if (cell && start === null) {\n        start = x;\n      }\n    });\n  });\n  return ops.join(\"\");\n}\nfunction excavateModules(modules, excavation) {\n  return modules.slice().map((row, y) => {\n    if (y < excavation.y || y >= excavation.y + excavation.h) {\n      return row;\n    }\n    return row.map((cell, x) => {\n      if (x < excavation.x || x >= excavation.x + excavation.w) {\n        return cell;\n      }\n      return false;\n    });\n  });\n}\nfunction getImageSettings(cells, size, margin, imageSettings) {\n  if (imageSettings == null) {\n    return null;\n  }\n  const numCells = cells.length + margin * 2;\n  const defaultSize = Math.floor(size * DEFAULT_IMG_SCALE);\n  const scale = numCells / size;\n  const w = (imageSettings.width || defaultSize) * scale;\n  const h = (imageSettings.height || defaultSize) * scale;\n  const x = imageSettings.x == null ? cells.length / 2 - w / 2 : imageSettings.x * scale;\n  const y = imageSettings.y == null ? cells.length / 2 - h / 2 : imageSettings.y * scale;\n  const opacity = imageSettings.opacity == null ? 1 : imageSettings.opacity;\n  let excavation = null;\n  if (imageSettings.excavate) {\n    let floorX = Math.floor(x);\n    let floorY = Math.floor(y);\n    let ceilW = Math.ceil(w + x - floorX);\n    let ceilH = Math.ceil(h + y - floorY);\n    excavation = { x: floorX, y: floorY, w: ceilW, h: ceilH };\n  }\n  const crossOrigin = imageSettings.crossOrigin;\n  return { x, y, h, w, excavation, opacity, crossOrigin };\n}\nfunction getMarginSize(includeMargin, marginSize) {\n  if (marginSize != null) {\n    return Math.max(Math.floor(marginSize), 0);\n  }\n  return includeMargin ? SPEC_MARGIN_SIZE : DEFAULT_MARGIN_SIZE;\n}\nfunction useQRCode({\n  value,\n  level,\n  minVersion,\n  includeMargin,\n  marginSize,\n  imageSettings,\n  size,\n  boostLevel\n}) {\n  let qrcode = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    const values = Array.isArray(value) ? value : [value];\n    const segments = values.reduce((accum, v) => {\n      accum.push(...qrcodegen_default.QrSegment.makeSegments(v));\n      return accum;\n    }, []);\n    return qrcodegen_default.QrCode.encodeSegments(\n      segments,\n      ERROR_LEVEL_MAP[level],\n      minVersion,\n      void 0,\n      void 0,\n      boostLevel\n    );\n  }, [value, level, minVersion, boostLevel]);\n  const { cells, margin, numCells, calculatedImageSettings } = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    let cells2 = qrcode.getModules();\n    const margin2 = getMarginSize(includeMargin, marginSize);\n    const numCells2 = cells2.length + margin2 * 2;\n    const calculatedImageSettings2 = getImageSettings(\n      cells2,\n      size,\n      margin2,\n      imageSettings\n    );\n    return {\n      cells: cells2,\n      margin: margin2,\n      numCells: numCells2,\n      calculatedImageSettings: calculatedImageSettings2\n    };\n  }, [qrcode, size, imageSettings, includeMargin, marginSize]);\n  return {\n    qrcode,\n    margin,\n    cells,\n    numCells,\n    calculatedImageSettings\n  };\n}\nvar SUPPORTS_PATH2D = function() {\n  try {\n    new Path2D().addPath(new Path2D());\n  } catch (e) {\n    return false;\n  }\n  return true;\n}();\nvar QRCodeCanvas = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  function QRCodeCanvas2(props, forwardedRef) {\n    const _a = props, {\n      value,\n      size = DEFAULT_SIZE,\n      level = DEFAULT_LEVEL,\n      bgColor = DEFAULT_BGCOLOR,\n      fgColor = DEFAULT_FGCOLOR,\n      includeMargin = DEFAULT_INCLUDEMARGIN,\n      minVersion = DEFAULT_MINVERSION,\n      boostLevel,\n      marginSize,\n      imageSettings\n    } = _a, extraProps = __objRest(_a, [\n      \"value\",\n      \"size\",\n      \"level\",\n      \"bgColor\",\n      \"fgColor\",\n      \"includeMargin\",\n      \"minVersion\",\n      \"boostLevel\",\n      \"marginSize\",\n      \"imageSettings\"\n    ]);\n    const _b = extraProps, { style } = _b, otherProps = __objRest(_b, [\"style\"]);\n    const imgSrc = imageSettings == null ? void 0 : imageSettings.src;\n    const _canvas = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const _image = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const setCanvasRef = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n      (node) => {\n        _canvas.current = node;\n        if (typeof forwardedRef === \"function\") {\n          forwardedRef(node);\n        } else if (forwardedRef) {\n          forwardedRef.current = node;\n        }\n      },\n      [forwardedRef]\n    );\n    const [isImgLoaded, setIsImageLoaded] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const { margin, cells, numCells, calculatedImageSettings } = useQRCode({\n      value,\n      level,\n      minVersion,\n      boostLevel,\n      includeMargin,\n      marginSize,\n      imageSettings,\n      size\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      if (_canvas.current != null) {\n        const canvas = _canvas.current;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) {\n          return;\n        }\n        let cellsToDraw = cells;\n        const image = _image.current;\n        const haveImageToRender = calculatedImageSettings != null && image !== null && image.complete && image.naturalHeight !== 0 && image.naturalWidth !== 0;\n        if (haveImageToRender) {\n          if (calculatedImageSettings.excavation != null) {\n            cellsToDraw = excavateModules(\n              cells,\n              calculatedImageSettings.excavation\n            );\n          }\n        }\n        const pixelRatio = window.devicePixelRatio || 1;\n        canvas.height = canvas.width = size * pixelRatio;\n        const scale = size / numCells * pixelRatio;\n        ctx.scale(scale, scale);\n        ctx.fillStyle = bgColor;\n        ctx.fillRect(0, 0, numCells, numCells);\n        ctx.fillStyle = fgColor;\n        if (SUPPORTS_PATH2D) {\n          ctx.fill(new Path2D(generatePath(cellsToDraw, margin)));\n        } else {\n          cells.forEach(function(row, rdx) {\n            row.forEach(function(cell, cdx) {\n              if (cell) {\n                ctx.fillRect(cdx + margin, rdx + margin, 1, 1);\n              }\n            });\n          });\n        }\n        if (calculatedImageSettings) {\n          ctx.globalAlpha = calculatedImageSettings.opacity;\n        }\n        if (haveImageToRender) {\n          ctx.drawImage(\n            image,\n            calculatedImageSettings.x + margin,\n            calculatedImageSettings.y + margin,\n            calculatedImageSettings.w,\n            calculatedImageSettings.h\n          );\n        }\n      }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      setIsImageLoaded(false);\n    }, [imgSrc]);\n    const canvasStyle = __spreadValues({ height: size, width: size }, style);\n    let img = null;\n    if (imgSrc != null) {\n      img = /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n        \"img\",\n        {\n          src: imgSrc,\n          key: imgSrc,\n          style: { display: \"none\" },\n          onLoad: () => {\n            setIsImageLoaded(true);\n          },\n          ref: _image,\n          crossOrigin: calculatedImageSettings == null ? void 0 : calculatedImageSettings.crossOrigin\n        }\n      );\n    }\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n      \"canvas\",\n      __spreadValues({\n        style: canvasStyle,\n        height: size,\n        width: size,\n        ref: setCanvasRef,\n        role: \"img\"\n      }, otherProps)\n    ), img);\n  }\n);\nQRCodeCanvas.displayName = \"QRCodeCanvas\";\nvar QRCodeSVG = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  function QRCodeSVG2(props, forwardedRef) {\n    const _a = props, {\n      value,\n      size = DEFAULT_SIZE,\n      level = DEFAULT_LEVEL,\n      bgColor = DEFAULT_BGCOLOR,\n      fgColor = DEFAULT_FGCOLOR,\n      includeMargin = DEFAULT_INCLUDEMARGIN,\n      minVersion = DEFAULT_MINVERSION,\n      boostLevel,\n      title,\n      marginSize,\n      imageSettings\n    } = _a, otherProps = __objRest(_a, [\n      \"value\",\n      \"size\",\n      \"level\",\n      \"bgColor\",\n      \"fgColor\",\n      \"includeMargin\",\n      \"minVersion\",\n      \"boostLevel\",\n      \"title\",\n      \"marginSize\",\n      \"imageSettings\"\n    ]);\n    const { margin, cells, numCells, calculatedImageSettings } = useQRCode({\n      value,\n      level,\n      minVersion,\n      boostLevel,\n      includeMargin,\n      marginSize,\n      imageSettings,\n      size\n    });\n    let cellsToDraw = cells;\n    let image = null;\n    if (imageSettings != null && calculatedImageSettings != null) {\n      if (calculatedImageSettings.excavation != null) {\n        cellsToDraw = excavateModules(\n          cells,\n          calculatedImageSettings.excavation\n        );\n      }\n      image = /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n        \"image\",\n        {\n          href: imageSettings.src,\n          height: calculatedImageSettings.h,\n          width: calculatedImageSettings.w,\n          x: calculatedImageSettings.x + margin,\n          y: calculatedImageSettings.y + margin,\n          preserveAspectRatio: \"none\",\n          opacity: calculatedImageSettings.opacity,\n          crossOrigin: calculatedImageSettings.crossOrigin\n        }\n      );\n    }\n    const fgPath = generatePath(cellsToDraw, margin);\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n      \"svg\",\n      __spreadValues({\n        height: size,\n        width: size,\n        viewBox: `0 0 ${numCells} ${numCells}`,\n        ref: forwardedRef,\n        role: \"img\"\n      }, otherProps),\n      !!title && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", null, title),\n      /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n        \"path\",\n        {\n          fill: bgColor,\n          d: `M0,0 h${numCells}v${numCells}H0z`,\n          shapeRendering: \"crispEdges\"\n        }\n      ),\n      /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", { fill: fgColor, d: fgPath, shapeRendering: \"crispEdges\" }),\n      image\n    );\n  }\n);\nQRCodeSVG.displayName = \"QRCodeSVG\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/qrcode.react@4.2.0_react@19.0.0/node_modules/qrcode.react/lib/esm/index.js\n"));

/***/ })

});