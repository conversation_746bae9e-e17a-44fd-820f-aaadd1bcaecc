"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/checkin/page",{

/***/ "(app-pages-browser)/./app/checkin/page.tsx":
/*!******************************!*\
  !*** ./app/checkin/page.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CheckInPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var qrcode_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! qrcode.react */ \"(app-pages-browser)/./node_modules/.pnpm/qrcode.react@4.2.0_react@19.0.0/node_modules/qrcode.react/lib/esm/index.js\");\n/* harmony import */ var _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/src/hooks/useSupabaseData */ \"(app-pages-browser)/./src/hooks/useSupabaseData.tsx\");\n/* harmony import */ var _src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/src/hooks/useCountdown */ \"(app-pages-browser)/./src/hooks/useCountdown.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction CheckInPage() {\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const machineId = searchParams.get(\"machine\");\n    const { laundry, toggleMachineStatus, reserveMachine, adjustMachineTime, isLoading, refreshData } = (0,_src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    const [machine, setMachine] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [actionResult, setActionResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [customTime, setCustomTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"60\");\n    const [useCustomTime, setUseCustomTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [adjustTime, setAdjustTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isAdjusting, setIsAdjusting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [adjustResult, setAdjustResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const countdown = (0,_src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(machine === null || machine === void 0 ? void 0 : machine.endAt, machine === null || machine === void 0 ? void 0 : machine.graceEndAt);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CheckInPage.useEffect\": ()=>{\n            if (!machineId) return;\n            const foundMachine = laundry.find({\n                \"CheckInPage.useEffect.foundMachine\": (m)=>m.id === machineId\n            }[\"CheckInPage.useEffect.foundMachine\"]);\n            setMachine(foundMachine || null);\n        }\n    }[\"CheckInPage.useEffect\"], [\n        machineId,\n        laundry\n    ]);\n    // Reduced auto-refresh to every 10 seconds to prevent conflicts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CheckInPage.useEffect\": ()=>{\n            const interval = setInterval({\n                \"CheckInPage.useEffect.interval\": ()=>{\n                    refreshData();\n                }\n            }[\"CheckInPage.useEffect.interval\"], 10000);\n            return ({\n                \"CheckInPage.useEffect\": ()=>clearInterval(interval)\n            })[\"CheckInPage.useEffect\"];\n        }\n    }[\"CheckInPage.useEffect\"], [\n        refreshData\n    ]);\n    const handleToggle = async ()=>{\n        if (!machineId || isProcessing) return;\n        setIsProcessing(true);\n        setActionResult(\"\");\n        let success = false;\n        if ((machine === null || machine === void 0 ? void 0 : machine.status) === \"free\" && useCustomTime) {\n            // Use custom time\n            success = await reserveMachine(machineId, \"\".concat(customTime, \" minutes\"));\n        } else {\n            // Use default toggle\n            success = await toggleMachineStatus(machineId);\n        }\n        if (success) {\n            setActionResult(\"Status updated successfully!\");\n            // Single refresh after 2 seconds to confirm the change\n            setTimeout(()=>refreshData(), 2000);\n        } else {\n            setActionResult(\"Error updating machine status\");\n        }\n        setIsProcessing(false);\n    };\n    const getStatusDisplay = ()=>{\n        if (!machine) return \"\";\n        switch(machine.status){\n            case \"free\":\n                return \"🟢 Available\";\n            case \"running\":\n                const hours = Math.floor(countdown.secondsLeft / 3600);\n                const minutes = Math.floor(countdown.secondsLeft % 3600 / 60);\n                const seconds = countdown.secondsLeft % 60;\n                return \"\\uD83D\\uDD35 In Use - \".concat(hours, \":\").concat(minutes.toString().padStart(2, \"0\"), \":\").concat(seconds.toString().padStart(2, \"0\"), \" left\");\n            case \"finishedGrace\":\n                // Calculate grace period time remaining\n                let graceTimeDisplay = \"5:00\";\n                if (machine.graceEndAt) {\n                    const graceMinutes = Math.floor(countdown.graceSecondsLeft / 60);\n                    const graceSeconds = countdown.graceSecondsLeft % 60;\n                    graceTimeDisplay = \"\".concat(graceMinutes, \":\").concat(graceSeconds.toString().padStart(2, \"0\"));\n                }\n                return \"⚠️ Please collect items - \".concat(graceTimeDisplay, \" left\");\n            default:\n                return \"Unknown\";\n        }\n    };\n    const getButtonText = ()=>{\n        if (isProcessing) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, this),\n                    \"Processing...\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 100,\n                columnNumber: 9\n            }, this);\n        }\n        switch(machine === null || machine === void 0 ? void 0 : machine.status){\n            case \"free\":\n                return useCustomTime ? \"▶️ Start Using Machine (\".concat(customTime, \" min)\") : \"▶️ Start Using Machine\";\n            case \"running\":\n                return \"⏹️ Stop Using Machine\";\n            case \"finishedGrace\":\n                return \"✅ I've Collected My Items\";\n            default:\n                return \"Update Status\";\n        }\n    };\n    const getButtonColor = ()=>{\n        if (isProcessing) return \"bg-gray-400 cursor-not-allowed\";\n        switch(machine === null || machine === void 0 ? void 0 : machine.status){\n            case \"free\":\n                return \"bg-blue-500 hover:bg-blue-600\";\n            case \"running\":\n                return \"bg-red-500 hover:bg-red-600\";\n            case \"finishedGrace\":\n                return \"bg-green-500 hover:bg-green-600\";\n            default:\n                return \"bg-gray-500\";\n        }\n    };\n    const handleBackToHome = ()=>{\n        router.push(\"/\");\n    };\n    // Custom display names for machines (same logic as LaundryCard)\n    const getDisplayName = (machine)=>{\n        if (!machine) return \"\";\n        const isWasher = machine.name.toLowerCase().includes(\"washer\");\n        if (isWasher) {\n            // Extract number from washer name (e.g., \"Washer 1\" -> \"1\")\n            const numberMatch = machine.name.match(/\\d+/);\n            const number = numberMatch ? numberMatch[0] : \"\";\n            return \"Washer \".concat(number);\n        } else {\n            // For dryers, use \"Dryer 5\" through \"Dryer 8\" based on original number\n            const numberMatch = machine.name.match(/\\d+/);\n            const originalNumber = numberMatch ? Number.parseInt(numberMatch[0]) : 0;\n            const newNumber = originalNumber + 4 // Map 1->5, 2->6, 3->7, 4->8\n            ;\n            return \"Dryer \".concat(newNumber);\n        }\n    };\n    if (isLoading && !machine) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-8 rounded-lg shadow text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-lg\",\n                        children: \"Getting machine information...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 161,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 160,\n            columnNumber: 7\n        }, this);\n    }\n    if (!machineId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-8 rounded-lg shadow text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-600 text-lg mb-4\",\n                        children: \"Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600\",\n                        children: \"No machine ID provided.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 172,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 171,\n            columnNumber: 7\n        }, this);\n    }\n    if (!machine) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-8 rounded-lg shadow text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-600 text-lg mb-4\",\n                        children: \"Machine Not Found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"The requested machine could not be found.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: refreshData,\n                        className: \"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded\",\n                        children: \"Retry Loading\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                lineNumber: 183,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 182,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white p-8 rounded-lg shadow text-center max-w-md w-full mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-4\",\n                    children: \"Machine Check-In\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-600 mb-3\",\n                            children: \"Scan to access this page\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white p-3 rounded-lg border-2 border-gray-200 shadow-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(qrcode_react__WEBPACK_IMPORTED_MODULE_3__.QRCodeSVG, {\n                                    value: \"\".concat( true ? window.location.origin : 0, \"/checkin?machine=\").concat(machineId),\n                                    size: 80,\n                                    fgColor: \"#1A1F36\",\n                                    bgColor: \"#FFFFFF\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-lg font-medium\",\n                            children: getDisplayName(machine)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm mt-1 font-semibold \".concat(machine.status === \"free\" ? \"text-green-600\" : machine.status === \"running\" ? \"text-blue-600\" : machine.status === \"finishedGrace\" ? \"text-orange-600\" : \"text-red-600\", \" \").concat(machine.status === \"finishedGrace\" ? \"animate-pulse\" : \"\"),\n                            children: [\n                                \"Status: \",\n                                getStatusDisplay()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, this),\n                        machine.status === \"running\" && machine.endAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500 mt-1\",\n                            children: [\n                                \"Ends at: \",\n                                machine.endAt.toLocaleTimeString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 13\n                        }, this),\n                        machine.status === \"finishedGrace\" && machine.graceEndAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-orange-500 mt-1\",\n                            children: [\n                                \"Grace period ends at: \",\n                                machine.graceEndAt.toLocaleTimeString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, this),\n                machine.status === \"free\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    id: \"useCustomTime\",\n                                    checked: useCustomTime,\n                                    onChange: (e)=>setUseCustomTime(e.target.checked),\n                                    className: \"mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"useCustomTime\",\n                                    className: \"text-sm\",\n                                    children: \"Set custom time\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 13\n                        }, this),\n                        useCustomTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"number\",\n                                    value: customTime,\n                                    onChange: (e)=>setCustomTime(e.target.value),\n                                    min: \"1\",\n                                    max: \"120\",\n                                    className: \"border rounded p-2 w-20 text-center mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"minutes\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleToggle,\n                    disabled: isProcessing,\n                    className: \"w-full p-3 rounded text-white font-medium mb-4 \".concat(getButtonColor()),\n                    children: getButtonText()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 273,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: refreshData,\n                    disabled: isLoading,\n                    className: \"w-full p-2 rounded border border-gray-300 text-gray-700 hover:bg-gray-50 mb-4\",\n                    children: \"Refresh Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 281,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleBackToHome,\n                    className: \"w-full p-2 rounded bg-accent hover:bg-teal-600 text-white font-medium mb-4 transition-colors duration-200\",\n                    children: \"← Back to Home\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 290,\n                    columnNumber: 9\n                }, this),\n                actionResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm mb-4 p-2 rounded \".concat(actionResult.includes(\"Error\") ? \"bg-red-100 text-red-700\" : \"bg-green-100 text-green-700\"),\n                    children: actionResult\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 298,\n                    columnNumber: 11\n                }, this),\n                machine.status === \"finishedGrace\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-orange-50 border border-orange-200 rounded p-3 text-sm text-orange-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium mb-1\",\n                            children: \"⚠️ Grace Period Active\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Please collect your items within the time limit. The machine will become available automatically when the grace period ends.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n                    lineNumber: 308,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n            lineNumber: 196,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dorm_21\\\\app\\\\checkin\\\\page.tsx\",\n        lineNumber: 195,\n        columnNumber: 5\n    }, this);\n}\n_s(CheckInPage, \"TCaXzYfj362L+fenaMro9hjXXZ4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _src_hooks_useSupabaseData__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        _src_hooks_useCountdown__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n});\n_c = CheckInPage;\nvar _c;\n$RefreshReg$(_c, \"CheckInPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/checkin/page.tsx\n"));

/***/ })

});