{"version": 3, "sources": ["../../src/client/next-dev.ts"], "sourcesContent": ["// TODO: Remove use of `any` type.\nimport './webpack'\nimport { initialize, version, router, emitter } from './'\nimport initHMR from './dev/hot-middleware-client'\nimport { pageBootstrap } from './page-bootstrap'\n\nwindow.next = {\n  version,\n  // router is initialized later so it has to be live-binded\n  get router() {\n    return router\n  },\n  emitter,\n}\n\nconst devClient = initHMR('webpack')\ninitialize({ devClient })\n  .then(({ assetPrefix }) => {\n    return pageBootstrap(assetPrefix)\n  })\n  .catch((err) => {\n    console.error('Error was not caught', err)\n  })\n"], "names": ["window", "next", "version", "router", "emitter", "devClient", "initHMR", "initialize", "then", "assetPrefix", "pageBootstrap", "catch", "err", "console", "error"], "mappings": "AAAA,kCAAkC;;;;;;QAC3B;kBAC8C;8EACjC;+BACU;AAE9BA,OAAOC,IAAI,GAAG;IACZC,SAAAA,SAAO;IACP,0DAA0D;IAC1D,IAAIC,UAAS;QACX,OAAOA,QAAM;IACf;IACAC,SAAAA,SAAO;AACT;AAEA,MAAMC,YAAYC,IAAAA,4BAAO,EAAC;AAC1BC,IAAAA,YAAU,EAAC;IAAEF;AAAU,GACpBG,IAAI,CAAC;QAAC,EAAEC,WAAW,EAAE;IACpB,OAAOC,IAAAA,4BAAa,EAACD;AACvB,GACCE,KAAK,CAAC,CAACC;IACNC,QAAQC,KAAK,CAAC,wBAAwBF;AACxC"}